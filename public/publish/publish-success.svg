<svg width="558" height="314" viewBox="0 0 558 314" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_2538_1699)" filter="url(#filter0_i_2538_1699)">
<rect width="558" height="314" fill="#1F1FCC"/>
<circle cx="281.5" cy="572" r="416" fill="url(#paint0_linear_2538_1699)"/>
<path d="M314.762 61.4678C316.803 60.6226 319.143 61.5917 319.988 63.6323L335.03 99.9456L371.341 114.986C373.382 115.832 374.352 118.172 373.506 120.213L358.465 156.525L373.506 192.837C374.352 194.877 373.383 197.218 371.342 198.064L335.029 213.104L319.988 249.417C319.143 251.458 316.803 252.427 314.762 251.582L278.449 236.54L242.137 251.581C240.096 252.427 237.757 251.458 236.911 249.417L221.87 213.105L185.558 198.064C183.517 197.218 182.548 194.878 183.393 192.837L198.434 156.525L183.393 120.213C182.548 118.172 183.517 115.831 185.558 114.986L221.87 99.945L236.911 63.6331C237.756 61.5922 240.096 60.6229 242.137 61.4683L278.449 76.5092L314.762 61.4678Z" fill="url(#paint1_radial_2538_1699)"/>
<foreignObject x="212" y="87" width="137" height="138"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(2px);clip-path:url(#bgblur_1_2538_1699_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter1_ii_2538_1699)" data-figma-bg-blur-radius="4">
<path fill-rule="evenodd" clip-rule="evenodd" d="M280.5 221C316.122 221 345 191.899 345 156C345 120.101 316.122 91 280.5 91C244.878 91 216 120.101 216 156C216 191.899 244.878 221 280.5 221ZM313.087 146.698C316.895 142.921 316.943 136.748 313.195 132.91C309.446 129.073 303.321 129.024 299.513 132.802L271.9 160.194L261.487 149.864C257.679 146.087 251.554 146.135 247.805 149.973C244.057 153.81 244.105 159.983 247.913 163.761L265.113 180.823C268.878 184.559 274.922 184.559 278.687 180.823L313.087 146.698Z" fill="white" fill-opacity="0.6"/>
</g>
</g>
<defs>
<filter id="filter0_i_2538_1699" x="0" y="0" width="558" height="314" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="2" operator="dilate" in="SourceAlpha" result="effect1_innerShadow_2538_1699"/>
<feOffset/>
<feGaussianBlur stdDeviation="4"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.180392 0 0 0 0 0.180392 0 0 0 0 0.898039 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_2538_1699"/>
</filter>
<filter id="filter1_ii_2538_1699" x="212" y="87" width="137" height="138" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_2538_1699"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="4"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_2538_1699" result="effect2_innerShadow_2538_1699"/>
</filter>
<clipPath id="bgblur_1_2538_1699_clip_path" transform="translate(-212 -87)"><path fill-rule="evenodd" clip-rule="evenodd" d="M280.5 221C316.122 221 345 191.899 345 156C345 120.101 316.122 91 280.5 91C244.878 91 216 120.101 216 156C216 191.899 244.878 221 280.5 221ZM313.087 146.698C316.895 142.921 316.943 136.748 313.195 132.91C309.446 129.073 303.321 129.024 299.513 132.802L271.9 160.194L261.487 149.864C257.679 146.087 251.554 146.135 247.805 149.973C244.057 153.81 244.105 159.983 247.913 163.761L265.113 180.823C268.878 184.559 274.922 184.559 278.687 180.823L313.087 146.698Z"/>
</clipPath><linearGradient id="paint0_linear_2538_1699" x1="281.5" y1="156" x2="281.5" y2="988" gradientUnits="userSpaceOnUse">
<stop stop-color="#1515E4"/>
<stop offset="0.240385" stop-color="#7474EF"/>
<stop offset="0.360577" stop-color="white"/>
</linearGradient>
<radialGradient id="paint1_radial_2538_1699" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(278.45 156.525) rotate(112.5) scale(102.889 102.889)">
<stop offset="0.538462" stop-color="#FFD942"/>
<stop offset="1" stop-color="#FFF2C0"/>
</radialGradient>
<clipPath id="clip0_2538_1699">
<rect width="558" height="314" fill="white"/>
</clipPath>
</defs>
</svg>
