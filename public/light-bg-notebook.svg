<svg width="128" height="128" viewBox="0 0 128 128" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_146_2321)">
<g filter="url(#filter1_i_146_2321)">
<path d="M32 32C32 27.5817 35.5817 24 40 24H104C108.418 24 112 27.5817 112 32V76C112 80.4183 108.418 84 104 84H40C35.5817 84 32 80.4183 32 76V32Z" fill="#1FD1A2"/>
</g>
<foreignObject x="16" y="36" width="80" height="56"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(2px);clip-path:url(#bgblur_0_146_2321_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter2_ii_146_2321)" data-figma-bg-blur-radius="4">
<path d="M20 44C20 41.7909 21.7909 40 24 40H88C90.2091 40 92 41.7909 92 44V84C92 86.2091 90.2091 88 88 88H24C21.7909 88 20 86.2091 20 84V44Z" fill="white" fill-opacity="0.4"/>
</g>
<g filter="url(#filter3_ii_146_2321)">
<path d="M28 51C28 49.3431 29.3431 48 31 48H81C82.6569 48 84 49.3431 84 51C84 52.6569 82.6569 54 81 54H31C29.3431 54 28 52.6569 28 51Z" fill="white" fill-opacity="0.6"/>
</g>
<g filter="url(#filter4_ii_146_2321)">
<path d="M28 63C28 61.3431 29.3431 60 31 60H65C66.6569 60 68 61.3431 68 63C68 64.6569 66.6569 66 65 66H31C29.3431 66 28 64.6569 28 63Z" fill="white" fill-opacity="0.6"/>
</g>
<g filter="url(#filter5_ii_146_2321)">
<path d="M19.0883 94.7351C19.6328 93.1017 21.1613 92 22.883 92H89.117C90.8387 92 92.3672 93.1017 92.9117 94.7351L94.245 98.7351C95.1084 101.325 93.1805 104 90.4503 104H21.5497C18.8195 104 16.8916 101.325 17.755 98.7351L19.0883 94.7351Z" fill="white" fill-opacity="0.4"/>
</g>
</g>
<defs>
<filter id="filter0_d_146_2321" x="13.5466" y="20" width="110.453" height="96" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="4" dy="4"/>
<feGaussianBlur stdDeviation="4"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.145098 0 0 0 0 0.823529 0 0 0 0 0.643137 0 0 0 0.4 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_146_2321"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_146_2321" result="shape"/>
</filter>
<filter id="filter1_i_146_2321" x="32" y="24" width="80" height="60" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="4"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.8 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_146_2321"/>
</filter>
<filter id="filter2_ii_146_2321" x="16" y="36" width="80" height="56" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_146_2321"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="4"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_146_2321" result="effect2_innerShadow_146_2321"/>
</filter>
<clipPath id="bgblur_0_146_2321_clip_path" transform="translate(-16 -36)"><path d="M20 44C20 41.7909 21.7909 40 24 40H88C90.2091 40 92 41.7909 92 44V84C92 86.2091 90.2091 88 88 88H24C21.7909 88 20 86.2091 20 84V44Z"/>
</clipPath><filter id="filter3_ii_146_2321" x="28" y="48" width="56" height="10" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_146_2321"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="4"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_146_2321" result="effect2_innerShadow_146_2321"/>
</filter>
<filter id="filter4_ii_146_2321" x="28" y="60" width="40" height="10" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_146_2321"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="4"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_146_2321" result="effect2_innerShadow_146_2321"/>
</filter>
<filter id="filter5_ii_146_2321" x="17.5466" y="92" width="76.9067" height="16" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_146_2321"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="4"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_146_2321" result="effect2_innerShadow_146_2321"/>
</filter>
</defs>
</svg>
