<svg width="77" height="65" viewBox="0 0 77 65" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_1789_31881)">
<g filter="url(#filter1_i_1789_31881)">
<path d="M14.5 9.84375C14.5 7.08233 16.7386 4.84375 19.5 4.84375H59.5C62.2614 4.84375 64.5 7.08233 64.5 9.84375V37.3438C64.5 40.1052 62.2614 42.3438 59.5 42.3438H19.5C16.7386 42.3438 14.5 40.1052 14.5 37.3438V9.84375Z" fill="#3E40FA"/>
</g>
<foreignObject x="0.5" y="10.8438" width="58" height="45.5"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(2px);clip-path:url(#bgblur_0_1789_31881_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter2_ii_1789_31881)" data-figma-bg-blur-radius="4">
<path fill-rule="evenodd" clip-rule="evenodd" d="M9.5 14.8438C6.73858 14.8438 4.5 17.0823 4.5 19.8437V47.3438C4.5 50.1052 6.73858 52.3438 9.5 52.3438H49.5C52.2614 52.3438 54.5 50.1052 54.5 47.3438V19.8438C54.5 17.0823 52.2614 14.8438 49.5 14.8438H9.5ZM23.25 27.8186V39.3689C23.25 40.2165 24.2001 40.7446 24.9566 40.3175L35.187 34.5423C35.9377 34.1186 35.9377 33.0689 35.187 32.6452L24.9566 26.87C24.2001 26.4429 23.25 26.971 23.25 27.8186Z" fill="white" fill-opacity="0.4"/>
</g>
<g filter="url(#filter3_ii_1789_31881)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M12 18.5938C9.92893 18.5938 8.25 20.2727 8.25 22.3438V44.8438C8.25 46.9148 9.92893 48.5938 12 48.5938H47C49.0711 48.5938 50.75 46.9148 50.75 44.8438V22.3438C50.75 20.2727 49.0711 18.5938 47 18.5938H12ZM23.25 27.8186V39.3689C23.25 40.2165 24.2001 40.7446 24.9566 40.3175L35.187 34.5423C35.9377 34.1186 35.9377 33.0689 35.187 32.6452L24.9566 26.87C24.2001 26.4429 23.25 26.971 23.25 27.8186Z" fill="white" fill-opacity="0.6"/>
</g>
</g>
<defs>
<filter id="filter0_d_1789_31881" x="0.5" y="0.84375" width="76" height="63.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="4" dy="4"/>
<feGaussianBlur stdDeviation="4"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.286275 0 0 0 0 0.290196 0 0 0 0 0.988235 0 0 0 0.4 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1789_31881"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1789_31881" result="shape"/>
</filter>
<filter id="filter1_i_1789_31881" x="14.5" y="4.84375" width="50" height="37.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="4"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.8 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_1789_31881"/>
</filter>
<filter id="filter2_ii_1789_31881" x="0.5" y="10.8438" width="58" height="45.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_1789_31881"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="4"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_1789_31881" result="effect2_innerShadow_1789_31881"/>
</filter>
<clipPath id="bgblur_0_1789_31881_clip_path" transform="translate(-0.5 -10.8438)"><path fill-rule="evenodd" clip-rule="evenodd" d="M9.5 14.8438C6.73858 14.8438 4.5 17.0823 4.5 19.8437V47.3438C4.5 50.1052 6.73858 52.3438 9.5 52.3438H49.5C52.2614 52.3438 54.5 50.1052 54.5 47.3438V19.8438C54.5 17.0823 52.2614 14.8438 49.5 14.8438H9.5ZM23.25 27.8186V39.3689C23.25 40.2165 24.2001 40.7446 24.9566 40.3175L35.187 34.5423C35.9377 34.1186 35.9377 33.0689 35.187 32.6452L24.9566 26.87C24.2001 26.4429 23.25 26.971 23.25 27.8186Z"/>
</clipPath><filter id="filter3_ii_1789_31881" x="8.25" y="18.5938" width="42.5" height="34" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_1789_31881"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="4"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_1789_31881" result="effect2_innerShadow_1789_31881"/>
</filter>
</defs>
</svg>
