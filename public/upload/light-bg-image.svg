<svg width="76" height="64" viewBox="0 0 76 64" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_1882_73592)">
<g filter="url(#filter1_i_1882_73592)">
<path d="M14 9.4375C14 6.67608 16.2386 4.4375 19 4.4375H59C61.7614 4.4375 64 6.67608 64 9.4375V36.9375C64 39.6989 61.7614 41.9375 59 41.9375H19C16.2386 41.9375 14 39.6989 14 36.9375V9.4375Z" fill="#3E40FA"/>
</g>
<foreignObject x="0" y="10.4375" width="58" height="45.5"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(2px);clip-path:url(#bgblur_0_1882_73592_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter2_ii_1882_73592)" data-figma-bg-blur-radius="4">
<path fill-rule="evenodd" clip-rule="evenodd" d="M9 14.4375C6.23858 14.4375 4 16.6761 4 19.4375V46.9375C4 49.6989 6.23858 51.9375 9 51.9375H49C51.7614 51.9375 54 49.6989 54 46.9375V19.4375C54 16.6761 51.7614 14.4375 49 14.4375H9ZM44 29.4375C46.7614 29.4375 49 27.1989 49 24.4375C49 21.6761 46.7614 19.4375 44 19.4375C41.2386 19.4375 39 21.6761 39 24.4375C39 27.1989 41.2386 29.4375 44 29.4375Z" fill="white" fill-opacity="0.4"/>
</g>
<g filter="url(#filter3_ii_1882_73592)">
<path d="M6.5 36.7293C6.5 36.4588 6.58772 36.1956 6.75 35.9793L13.1352 27.4656C13.5917 26.8571 14.4812 26.7938 15.0191 27.3317L23.3249 35.6375C23.7235 36.0361 24.3395 36.1186 24.829 35.8389L32.0591 31.7074C32.4806 31.4665 33.0035 31.4915 33.4001 31.7715L42.8734 38.4585C43.1897 38.6818 43.5917 38.7459 43.9618 38.632L49.8824 36.8103C50.6862 36.563 51.5 37.164 51.5 38.005V46.9376C51.5 48.3183 50.3807 49.4376 49 49.4376H9C7.61929 49.4376 6.5 48.3183 6.5 46.9376V36.7293Z" fill="white" fill-opacity="0.6"/>
</g>
</g>
<defs>
<filter id="filter0_d_1882_73592" x="0" y="0.4375" width="76" height="63.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="4" dy="4"/>
<feGaussianBlur stdDeviation="4"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.286275 0 0 0 0 0.290196 0 0 0 0 0.988235 0 0 0 0.4 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1882_73592"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1882_73592" result="shape"/>
</filter>
<filter id="filter1_i_1882_73592" x="14" y="4.4375" width="50" height="37.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="4"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.8 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_1882_73592"/>
</filter>
<filter id="filter2_ii_1882_73592" x="0" y="10.4375" width="58" height="45.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_1882_73592"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="4"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_1882_73592" result="effect2_innerShadow_1882_73592"/>
</filter>
<clipPath id="bgblur_0_1882_73592_clip_path" transform="translate(0 -10.4375)"><path fill-rule="evenodd" clip-rule="evenodd" d="M9 14.4375C6.23858 14.4375 4 16.6761 4 19.4375V46.9375C4 49.6989 6.23858 51.9375 9 51.9375H49C51.7614 51.9375 54 49.6989 54 46.9375V19.4375C54 16.6761 51.7614 14.4375 49 14.4375H9ZM44 29.4375C46.7614 29.4375 49 27.1989 49 24.4375C49 21.6761 46.7614 19.4375 44 19.4375C41.2386 19.4375 39 21.6761 39 24.4375C39 27.1989 41.2386 29.4375 44 29.4375Z"/>
</clipPath><filter id="filter3_ii_1882_73592" x="6.5" y="26.9656" width="45" height="26.4719" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_1882_73592"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="4"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_1882_73592" result="effect2_innerShadow_1882_73592"/>
</filter>
</defs>
</svg>
