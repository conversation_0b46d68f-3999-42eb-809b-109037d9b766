'use client';

import { Button, Textarea, Typography } from '@/components/ui';
import { routePaths } from '@/config';
import { useClient } from '@/hooks/useClient';
import LearnCourseHeader from '@/modules/courses/features/learn-course/components/LearnCourseHeader';
import SelectViewStar from '@/modules/courses/features/learn-course/components/test/SelectViewStar';
import useCourseReview from '@/modules/courses/features/learn-course/hooks/useCourseReview';
import { useCourseDetail } from '@/modules/courses/hooks';
import { useRouter } from 'next-nprogress-bar';
import Image from 'next/image';
import { useParams } from 'next/navigation';
import React from 'react';
import ReactConfetti from 'react-confetti';

const ReviewCourseContent = ({
  isReviewLoading,
  onReviewCourse,
}: {
  isReviewLoading: boolean;
  onReviewCourse: (data: { rating: number; comment?: string; feelings?: string[] }) => void;
}) => {
  const [stars, setStars] = React.useState(5);
  const [comment, setComment] = React.useState('');

  const handleReview = () => {
    onReviewCourse({ rating: stars, comment });
  };

  return (
    <div className="flex h-full w-1/2 flex-col items-center justify-center gap-8">
      <Image src={'/images/gold_cup.png'} alt={'gold cup'} width={144} height={144} />

      <Typography variant="headlineLg">Chúc mừng</Typography>

      <Typography className="text-center">
        Bạn đã hoàn thành khóa học.
        <br />
        Hãy cho Studify biết đánh giá của bạn về khóa học nhé
      </Typography>

      <SelectViewStar amountStar={stars} setAmountStar={setStars} />

      <div className="flex w-1/2 flex-col gap-2">
        <Typography>Đánh giá về khóa học (không bắt buộc)</Typography>

        <Textarea
          value={comment}
          placeholder="Nhập đánh giá của bạn"
          className="w-full bg-neutral-50"
          count={{ show: false }}
          rows={4}
          onChange={(e) => setComment(e.target.value)}
        />
      </div>

      <div className="w-1/3">
        <Button className="w-full" onClick={handleReview} loading={isReviewLoading}>
          Tiếp tục
        </Button>
      </div>
    </div>
  );
};

const ReviewTestPage = () => {
  const isClient = useClient();

  const router = useRouter();

  const params = useParams<{ courseId: string; sectionId: string; testId: string }>();

  const { courseDetailData: courseInfo } = useCourseDetail({ courseId: params.courseId });

  const { onReviewCourse, isReviewLoading } = useCourseReview();

  const [isSubmit, setIsSubmit] = React.useState(false);

  const [isPending, startTransition] = React.useTransition();

  const handleReview = (data: { rating: number; comment?: string }) => {
    const { rating, comment } = data;
    onReviewCourse({
      courseId: params.courseId,
      data: { rating, comment },
      onSuccess: () => setIsSubmit(true),
    });
  };

  const backToMyCourses = () => {
    startTransition(() => router.replace(routePaths.profile.path));
  };

  return (
    <div className="flex h-screen w-full flex-col">
      <LearnCourseHeader courseInfo={courseInfo!} />
      <div className="flex size-full flex-col items-center justify-center gap-4 py-10">
        {isClient && <ReactConfetti width={window.innerWidth} height={window.innerHeight} />}

        {isSubmit ? (
          <div className="flex flex-col items-center gap-6">
            <Typography variant="headlineLg">Đánh giá của bạn đã được ghi nhận</Typography>

            <div className="flex flex-col gap-1 text-center">
              <Typography>Những góp ý của bạn sẽ giúp chúng tôi nâng cao chất lượng khoá học hơn nữa.</Typography>
              <Typography>Studify xin chân thành cảm ơn bạn đã tin tưởng và lựa chọn.</Typography>
            </div>

            <Button className="w-[360px]" onClick={backToMyCourses} loading={isPending}>
              Trở về trang việc học của tôi
            </Button>
          </div>
        ) : (
          <ReviewCourseContent isReviewLoading={isReviewLoading} onReviewCourse={handleReview} />
        )}
      </div>
    </div>
  );
};

export default ReviewTestPage;
