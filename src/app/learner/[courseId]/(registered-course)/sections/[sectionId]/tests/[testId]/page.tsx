import { LearnCoursePage } from '@/modules/courses';
import { getCourseByIdService } from '@/modules/courses/services/course.service';
import { getTestByIdService } from '@/modules/courses/services/test.service';
import { AppProps } from 'type/appProps';

export default async function LearnLecturePage(props: Readonly<AppProps>) {
  const params = await props.params;
  const { courseId, sectionId, testId = '' } = params;

  const courseData = await getCourseByIdService(courseId);

  const testDetail = await getTestByIdService({ courseId, sectionId, testId });

  if (!courseData || !testDetail) {
    return <div>Không tìm thấy khóa học</div>;
  }

  return <LearnCoursePage testDetail={testDetail} courseInfo={courseData} />;
}
