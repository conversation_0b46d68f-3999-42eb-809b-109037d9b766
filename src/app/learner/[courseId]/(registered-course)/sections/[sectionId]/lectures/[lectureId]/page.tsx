import { LearnCoursePage } from '@/modules/courses';
import { getCourseByIdService, getLessonById } from '@/modules/courses/services/course.service';
import { getTestByIdService } from '@/modules/courses/services/test.service';
import { AppProps } from 'type/appProps';

export default async function LearnLecturePage(props: Readonly<AppProps>) {
  const params = await props.params;
  const { courseId, sectionId, lectureId } = params;

  const courseData = await getCourseByIdService(courseId);

  const lectureDetail = await getLessonById({ courseId, sectionId, lectureId });

  const testDetail = lectureDetail?.test?.id
    ? await getTestByIdService({ courseId, sectionId, testId: lectureDetail?.test?.id || '' })
    : null;

  if (!courseData || !lectureDetail) {
    return <div>Không tìm thấy khóa học</div>;
  }

  return <LearnCoursePage testDetail={testDetail} courseInfo={courseData} lectureInfo={lectureDetail} />;
}
