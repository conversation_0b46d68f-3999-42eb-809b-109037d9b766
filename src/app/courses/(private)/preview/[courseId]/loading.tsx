'use client';

import { Skeleton } from 'antd';

export default function PreviewCourseLoader() {
  return (
    <div className="size-full">
      <div className="flex w-full justify-between px-6 py-3">
        <div className="flex flex-col gap-1">
          <Skeleton.Input active className="h-6 w-1/2" />
          <Skeleton.Input active className="h-8 w-2/3" />
        </div>

        <Skeleton.Button active className="h-12 w-1/3" />
      </div>

      <div className="h-[calc(100vh-73px)] w-full overflow-hidden">
        <Skeleton.Node active className="size-full" />
      </div>
    </div>
  );
}
