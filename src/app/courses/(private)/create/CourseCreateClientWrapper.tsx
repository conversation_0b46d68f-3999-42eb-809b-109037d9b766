'use client';

import { CreateCourseHeader } from '@/modules/courses';
import { Form } from 'antd';
import { OverallContents, useOverallContentsForm } from 'components/courses/overallContents';
import { routePaths } from 'config/constant';
import { ContentTab, CopyrightEnum, CourseLevel, CourseType } from 'constants/enum';
import { useFormChangeDetection } from 'hooks/useFormChangeDetection';
import { useRouter } from 'next-nprogress-bar';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { useEffect } from 'react';
import { CourseActionContainer } from '../../../../components/container/course-action-container';

const items = [
  {
    key: ContentTab.TabInfo.toString(),
    label: (
      <Link href={routePaths.course.create} className={'text-primary'}>
        1. Thông tin
      </Link>
    ),
    disabled: false,
  },
  {
    key: ContentTab.TabContent.toString(),
    label: '2. Nội dung',
    disabled: true,
  },
];

interface CourseCreateClientWrapperProps {
  topics: any;
  packages: any;
  relatedCourses: any;
}

export function CourseCreateClientWrapper({ topics, packages, relatedCourses }: CourseCreateClientWrapperProps) {
  const courseInfo = {
    is_sequential: 0,
    course_type_id: CourseType.Standard,
    package_id: packages[0].id,
    copyright: CopyrightEnum.Individual,
    course_level_id: CourseLevel.Basic,
  } as any;

  const { courseId } = useParams<{ courseId: string }>();

  const { form, courseTypeWatched, setFile, isLoading, onFinish } = useOverallContentsForm({
    courseInfo,
    courseId,
    isEdit: false,
  });

  const router = useRouter();
  const { hasUnsavedChanges } = useFormChangeDetection(form, courseInfo);

  const handleNextStep = async () => {
    const isValid = await form.validateFields();
    if (!isValid) return;

    const values = form.getFieldsValue();
    await onFinish(values, {
      onSuccess: (courseId) => {
        router.push(routePaths.course.design.replace(':courseId', courseId));
      },
    });
  };

  const handleBackToCreatorList = () => {
    if (hasUnsavedChanges) {
      const confirmed = window.confirm('Bạn có thay đổi chưa được lưu. Bạn có chắc chắn muốn rời khỏi trang này?');
      if (confirmed) {
        router.push(routePaths.profile.children.creator.path);
      }
    } else {
      router.push(routePaths.profile.children.creator.path);
    }
  };

  useEffect(() => {
    window.history.pushState(null, '', window.location.href);
    const handlePopState = () => {
      if (hasUnsavedChanges) {
        const confirmed = window.confirm('Bạn có thay đổi chưa được lưu. Bạn có chắc chắn muốn rời khỏi trang này?');
        if (confirmed) {
          router.push(routePaths.profile.children.creator.path);
        }
      } else {
        router.push(routePaths.profile.children.creator.path);
      }
    };
    window.addEventListener('popstate', handlePopState);
  }, []);

  return (
    <Form form={form} onFinish={onFinish} layout={'vertical'} scrollToFirstError={true} initialValues={courseInfo}>
      <CreateCourseHeader
        slotProps={{ setupItem: { status: 'process' } }}
        onNext={handleNextStep}
        onBackToCreatorList={handleBackToCreatorList}
        hasUnsavedChanges={hasUnsavedChanges}
      />
      <main className={'h-[calc(100vh_-_72px)] overflow-auto'}>
        <CourseActionContainer>
          <OverallContents
            topics={topics}
            packages={packages}
            relatedCourses={relatedCourses?.data}
            course={courseInfo}
            form={form}
            courseTypeWatched={courseTypeWatched}
            setFile={setFile}
          />
        </CourseActionContainer>
      </main>
    </Form>
  );
}
