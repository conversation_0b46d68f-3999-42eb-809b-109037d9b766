'use client';

import { routePaths } from '@/config';
import { CreateCourseHeader } from '@/modules/courses';
import { formatApiUrl } from '@/utils/url.util';
import { Form } from 'antd';
import { OverallContents, useOverallContentsForm } from 'components/courses/overallContents';
import { ContentTab } from 'constants/enum';
import { useFormChangeDetection } from 'hooks/useFormChangeDetection';
import { useRouter } from 'next-nprogress-bar';
import queryString from 'query-string';
import { useEffect } from 'react';
import { CourseActionContainer } from '../../../../../../components/container/course-action-container';

const items = [
  {
    key: ContentTab.TabInfo.toString(),
    label: <span className={'text-primary'}>1. Thông tin</span>,
  },
  {
    key: ContentTab.TabContent.toString(),
    label: '2. Nội dung',
    disabled: false,
  },
];

interface CourseEditClientWrapperProps {
  courseId: string;
  course: any;
  topics: any;
  packages: any;
  relatedCourses: any;
  courseDraftId?: string;
}

export function CourseEditClientWrapper({
  courseId,
  course,
  topics,
  packages,
  relatedCourses,
  courseDraftId,
}: CourseEditClientWrapperProps) {
  const { form, courseTypeWatched, setFile, checkIsDirty, onFinish } = useOverallContentsForm({
    courseInfo: course,
    courseId,
    isEdit: true,
  });

  const router = useRouter();
  const { hasUnsavedChanges } = useFormChangeDetection(form, course);

  const designStepPath = queryString.stringifyUrl({
    url: formatApiUrl(routePaths.course.design, { courseId }),
  });

  const handleNextStep = async () => {
    const isValid = await form.validateFields();
    if (!isValid) return;

    const isDirty = checkIsDirty();
    if (isDirty) {
      const values = form.getFieldsValue();
      await onFinish(values, {
        onSuccess: () => {
          router.push(designStepPath);
        },
      });
      return;
    }

    router.push(designStepPath);
  };

  const handleBackToCreatorList = () => {
    if (hasUnsavedChanges) {
      const confirmed = window.confirm('Bạn có thay đổi chưa được lưu. Bạn có chắc chắn muốn rời khỏi trang này?');
      if (confirmed) {
        router.push(routePaths.profile.children.creator.path);
      }
    } else {
      router.push(routePaths.profile.children.creator.path);
    }
  };

  useEffect(() => {
    window.history.pushState(null, '', window.location.href);
    const handlePopState = () => {
      if (hasUnsavedChanges) {
        const confirmed = window.confirm('Bạn có thay đổi chưa được lưu. Bạn có chắc chắn muốn rời khỏi trang này?');
        if (confirmed) {
          router.push(routePaths.profile.children.creator.path);
        }
      } else {
        router.push(routePaths.profile.children.creator.path);
      }
    };
    window.addEventListener('popstate', handlePopState);
  }, []);

  useEffect(() => {
    if (courseDraftId) {
      router.replace(formatApiUrl(routePaths.course.editInfo, { courseId: courseDraftId }));
    }
  }, [courseDraftId]);

  return (
    <Form form={form} onFinish={handleNextStep} layout={'vertical'} scrollToFirstError={true} initialValues={course}>
      <CreateCourseHeader
        onNext={handleNextStep}
        onBackToCreatorList={handleBackToCreatorList}
        hasUnsavedChanges={hasUnsavedChanges}
      />
      <main className={'h-[calc(100vh_-_72px)] overflow-auto'}>
        <CourseActionContainer>
          <OverallContents
            topics={topics}
            packages={packages}
            relatedCourses={relatedCourses}
            course={course}
            form={form}
            courseTypeWatched={courseTypeWatched}
            setFile={setFile}
            isEdit={true}
          />
        </CourseActionContainer>
      </main>
    </Form>
  );
}
