'use client';

import { useCreatorCourses } from '@/hooks/apis/course/useCreatorCourses';
import { useDebounce } from '@/hooks/useDebounce';
// import { Input } from 'antd';
import { MagnifyingGlassIcon } from '@heroicons/react/24/outline';
import { CreatorButton, CreatorTable } from 'components/profileLayout/creator/CreatorList';
import { useEffect, useState } from 'react';
import { Input } from '../../../../lib';

// const { Search } = Input;

export const CreatorCoursesWithSearch = () => {
  const [inputValue, setInputValue] = useState('');
  const debouncedSearchTerm = useDebounce(inputValue, 500);

  const { courses, isLoading, setSearchName, totalCount } = useCreatorCourses({
    limit: 10,
    page: 0,
  });

  useEffect(() => {
    setSearchName(debouncedSearchTerm);
  }, [debouncedSearchTerm, setSearchName]);

  // const handleSearch = (value: string) => {
  //   setInputValue(value);
  //   setSearchName(value);
  // };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setInputValue(value);
  };

  return (
    <div className="mt-5 space-y-4">
      <div className="flex items-center justify-between">
        <div className="w-[500px]">
          {/* <Search
            placeholder="Tìm kiếm khóa học theo tên..."
            allowClear
            size="middle"
            onSearch={handleSearch}
            onChange={handleInputChange}
            loading={isLoading}
            value={inputValue}
            // prefix={<MagnifyingGlassCircleIcon width={20} height={20} />}
          /> */}
          <Input
            colorBgContainer={'white'}
            placeholder={'Tìm kiếm...'}
            onChange={handleInputChange}
            prefix={<MagnifyingGlassIcon className={'h-6 w-6'} />}
            className={'bg-white'}
          />
        </div>
        <CreatorButton />
      </div>
      <CreatorTable coursesCreator={{ data: courses, count: totalCount, limit: 10, page: 0 }} />
    </div>
  );
};
