'use client';

import { Skeleton } from 'antd';

export default function ProfileLoading() {
  return (
    <div className="flex flex-col gap-4 px-8 py-6">
      <Skeleton.Node active className="h-14 w-full" />
      <Skeleton.Input active className="h-8 w-full" />
      <Skeleton.Input active className="h-12 w-full" />
      <Skeleton.Node active className="min-h-[500px] w-full" />

      <div className="grid grid-cols-2 gap-6 sm:grid-cols-3 md:grid-cols-4">
        {Array.from({ length: 8 }).map((_, index) => (
          <Skeleton.Node key={index} active className="h-[200px] w-full" />
        ))}
      </div>
    </div>
  );
}
