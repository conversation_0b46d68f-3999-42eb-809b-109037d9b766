import { Metadata } from 'next';
import { AppProps } from 'type/appProps';
import MetricsPage from './MetricsPage';

export const metadata: Metadata = {
  title: 'Creator studio - Th<PERSON> viện sáng tạo',
  description: '<PERSON><PERSON><PERSON><PERSON> lý thư viện sáng tạo',
};

const DEFAULT_LIMIT = 10;
const DEFAULT_PAGE = 0;

async function CourseDetailedMetrics(props: Readonly<AppProps>) {
  const searchParams = await props.searchParams;
  const params = await props.params;

  return (
    <div className="px-3 pt-6">
      <MetricsPage courseId={params.id} />
    </div>
  );
}

export default CourseDetailedMetrics;
