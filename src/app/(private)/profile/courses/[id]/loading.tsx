'use client';

import { Typography } from '@/components/ui';
import { Skeleton } from 'antd';

export default function CourseDetailLoading() {
  return (
    <div className="size-full">
      <Skeleton.Node active className="h-[250px] w-full" />

      <div className="flex flex-col gap-8 p-8">
        <div className="flex flex-col gap-4">
          <Typography variant="headlineXs">Mô tả khóa học</Typography>
          <Skeleton.Node active className="h-[150px] w-4/5" />
        </div>

        <div className="flex flex-col gap-4">
          <Typography variant="headlineXs">Nội dung khóa học</Typography>

          <div className="flex flex-col gap-4">
            {Array.from({ length: 3 }).map((_, index) => (
              <Skeleton.Node key={index} active className="h-[56px] w-4/5" />
            ))}
          </div>
        </div>

        <div className="flex flex-col gap-4">
          <Typography variant="headlineXs">Đánh giá kh<PERSON>a học</Typography>
          <Skeleton.Node active className="h-[150px] w-4/5" />
        </div>
      </div>
    </div>
  );
}
