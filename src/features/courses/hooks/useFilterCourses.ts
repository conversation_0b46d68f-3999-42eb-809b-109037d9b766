'use client';
import { FormInstance } from 'antd';
import { useRouter } from 'next-nprogress-bar';
import { usePathname, useSearchParams } from 'next/navigation';
import queryString from 'query-string';
import { useEffect } from 'react';
import { CoursesByFilterParams } from '../types/params.type';

interface UseFilterCourseParams {
  form: FormInstance;
  openFilterModal: boolean;
  setOpenFilterModal: (open: boolean) => void;
}

export const useFilterCourse = ({ form, setOpenFilterModal }: UseFilterCourseParams) => {
  const router = useRouter();
  const pathName = usePathname();
  const location = useSearchParams();
  const queryParams = queryString.parse(location.toString());

  const handleFilter = () => {
    const formValues = form.getFieldsValue();
    onRedirect({ ...queryParams, ...formValues });
    form.resetFields();
    setOpenFilterModal(false);
  };

  const onResetForm = () => {
    form.resetFields();
  };

  const onRedirect = (params: CoursesByFilterParams) => {
    router.push(queryString.stringifyUrl({ url: pathName, query: { ...params } }), {
      scroll: false,
    });
  };

  useEffect(() => {
    form.setFieldsValue({ ...queryParams });
  }, []);

  return { handleFilter, onResetForm };
};
