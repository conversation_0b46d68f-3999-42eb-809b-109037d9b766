'use client';

import { QUERY_KEYS } from '@/constants/query-keys';
import { CourseInfo } from '@/features/courses';
import { getCoursesBySearch } from '@/features/courses/services/client/courses';
import { useDebounce } from '@/hooks';
import { useState } from 'react';
import { useQuery } from 'react-query';

const useSearchCourseQuery = (valueDebounce: string) => {
  const { data } = useQuery({
    queryKey: [QUERY_KEYS.SEARCH_COURSE, valueDebounce],
    queryFn: () => getCoursesBySearch({ search: valueDebounce }),
    enabled: !!valueDebounce,
  });

  return { data };
};

export const useSearchCourse = () => {
  const [query, setQuery] = useState('');

  const valueDebounce = useDebounce(query, 500);

  const { data: courseSearched } = useSearchCourseQuery(valueDebounce);

  const courses = (courseSearched || []) as CourseInfo[];

  return { courses, query, setQuery };
};
