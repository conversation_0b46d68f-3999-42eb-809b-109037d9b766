import { revalidatePath } from '@/actions/revalidatePath';
import { routePaths } from '@/config';
import { reviewCourse } from '@/features/courses/services/client';
import { BaseError } from '@/type';
import { App } from 'antd';
import { useRouter } from 'next-nprogress-bar';
import { useMutation } from 'react-query';

const useCourseReview = () => {
  const { notification } = App.useApp();

  const router = useRouter();

  const { isLoading, mutate: reviewCourseMutation } = useMutation({
    mutationFn: (variables: { courseId: string; data: { rating: number; comment?: string; feelings?: string[] } }) =>
      reviewCourse(variables),
    onSuccess: () => {
      notification.success({ message: 'Đ<PERSON>h giá khóa học thành công' });
      router.push(routePaths.profile.children.course.children.myCourse.path);
      revalidatePath(routePaths.profile.children.course.children.myCourse.path, 'page');
    },
    onError: (error: BaseError['response']['data']) => {
      notification.error({
        message: error.message,
      });
    },
  });

  return { isReviewLoading: isLoading, onReviewCourse: reviewCourseMutation };
};

export default useCourseReview;
