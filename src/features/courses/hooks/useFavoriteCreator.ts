import { updateFavoriteCreator } from '@/features/courses/';
import { notifyError, notifySuccess } from '@/utils';
import { useRouter } from 'next/navigation';
import { useMutation } from 'react-query';

const showSuccessNotification = (isFavorite: boolean) => {
  if (isFavorite) {
    notifySuccess('Thêm creator yêu thích thành công!');
  } else {
    notifySuccess('Xóa creator yêu thích thành công!');
  }
};

const showErrorNotification = (message: string) => {
  notifyError(message);
};

export const useFavoriteCreator = () => {
  const router = useRouter();
  const { mutate: updateFavoriteCreatorMutate, isLoading: isUpdatingFavorite } = useMutation(
    (payload: { authorId: string; isFavorite: boolean }) => updateFavoriteCreator(payload),
  );

  const handleFavorite = ({ authorId, isFavorite }: { authorId: string; isFavorite: boolean }) => {
    updateFavoriteCreatorMutate(
      { authorId, isFavorite },
      {
        onSuccess: () => {
          showSuccessNotification(isFavorite);
          router.refresh();
        },
        onError: (err: unknown) => {
          const error = err as { data: { message: string } };
          const message = error?.data?.message;
          showErrorNotification(message);
        },
      },
    );
  };

  return {
    isUpdatingFavorite,
    onFavorite: handleFavorite,
  };
};
