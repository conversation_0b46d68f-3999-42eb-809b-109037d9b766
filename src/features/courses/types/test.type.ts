import { Question } from '@/features/courses/types/slide.type';
import { UserInfo } from '@/type';

export type TestAnswerRequest = {
  answer: number[];
  question_id: string;
};

export type UserTestAnswerRequest = {
  courseId: string;
  sectionId: string;
  testId: string;
  data: TestAnswerRequest[];
};

export type UserTestResult = {
  id: string;
  createdAt: string;
  updatedAt: string;
  times: number;
  answer: string[] | number[];
  point: number;
  createdBy: UserInfo;
  testId: string;
  questionId: string;
};

export type Test = {
  id: string;
  createdAt: string;
  updatedAt: string;
  testName: string;
  minCorrectAnswer: number;
  hasLimitTime: number | null;
  limitTime: number;
  createdBy: UserInfo;
  updatedBy: UserInfo | null;
  questions: Question[];
  totalQuestions: number | null;
};
