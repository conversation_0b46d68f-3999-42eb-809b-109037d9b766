import { InteractionType } from '@/constants';
import { CourseFile, UploadedFile } from '@/features/courses/types/course.type';
import { CaseStudyItem } from '@/features/courses/types/lecture.type';
import { QuestionOption, TextControl } from '@/type';

export interface Question {
  id?: string; // This has id for update and no id for create
  question_type_id?: number;
  question_name: string;
  question_image: string;
  question_options: QuestionOption[] | null;
  correct_answer: number[];
  question_required: boolean;
  question_duration: number;
  reply_right_answer: string;
  reply_wrong_answer: string;
  background_color: string;
  created_by?: number | null;
  updated_by?: number | null;
  created_at?: string | null;
  updated_at?: string | null;
  video_url?: string | null;
}

export type DataSet = {
  label: string;
  data: number[];
  borderColor: string;
  backgroundColor: string;
};

export type ChartData = {
  labels: string[];
  title: string;
  datasets: DataSet[];
  chartType: string;
  description_title: string;
  description_body: string;
};

export type DataLayout = {
  background_image_1: CourseFile | null;
  background_image_2: CourseFile | null;
  background_image_3: CourseFile | null;
  highlightText: string | null;
  id: number | null;
  content_1: {
    title: string;
    description: string;
  };
  content_2: {
    title: string;
    description: string;
  };
  content_3: {
    title: string;
    description: string;
  };
  content_4: {
    title: string;
    description: string;
  };
};

export type LayoutContent = {
  data: DataLayout;
};

export type TableData = {
  title: string;
  content: string;
};

export type ShortClip = {
  file_id: string;
  file_name: string;
  file_url: string;
};

export type SlideItem = {
  id?: string; // This has id for update and no id for create
  chart_data: ChartData;
  updated_by: string | null;
  background_image: string;
  slide_item_type_id: InteractionType;
  slide_item_name: string;
  layout_type_id: number;
  layout_content: LayoutContent;
  background_color: string;
  table_data: TableData[];
  file_id: string | null;
  is_loop: number;
  sort_index: number;
  question: Question | null;
  explore: Explore | null;
  case_study: CaseStudyItem | null;
  short_clip: ShortClip | null;
  sound?: UploadedFile | null;
};

export type Slide = {
  id: string;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  updatedBy: string;
  slideName: string;
  slideItems: SlideItem[];
  file: FormData | null;
};

export type AttachFile = {
  id: string;
  fileName: string;
  fileUrl: string;
  fileSize: number;
};

export interface Explore {
  id?: string;
  slide_item_type_id: number;
  slide_item_name: string;
  sort_index: number;
  title: string;
  description: string;
  text_bottom: string | null;
  style_css: TextControl[];
  background_type: string;
  background_color: string;
  background_image: string | null;
  file_id: string | null;
  created_by: number | null;
  updated_by: number | null;
  created_at: string | null;
  updated_at: string | null;
  attach_files: AttachFile[];
}
