import { InteractionType } from '@/constants';
import { FileType as FileTypeEnum } from '@/constants/file';
import { Explore, Question, Slide } from '@/features/courses/types/slide.type';
import { LectureType } from '@/features/courses/utils';
import { LectureDocument, LectureSegment, UserAnswer, UserInfo } from '@/type';
import { CourseInfo, Test } from '../types/course.type';

export type AnswerItem = {
  name: string;
  answer?: string;
  style_css?: {
    textType: string;
    font: {
      textAlign: string;
      fontFamily: string;
      fontWeight: string;
      fontSize: number;
      action: number;
      color: string;
    };
    background: {
      type: string;
      backgroundColor: string;
      opacity: number;
    };
  };
};

export type CaseStudyItem = {
  id?: string;
  case_study_type_id: number;
  slide_item_name: string;
  sort_index: number;
  title: string;
  description: string;
  text_center_title: string;
  text_center_description: string;
  answers: AnswerItem[];
  correct_answer: number[] | null;
  background_type: number;
  background_color: string;
  background_image: string;
};

export type LectureInteract = {
  id?: string | null;
  interact_type_id: InteractionType;
  interact_name: string;
  start_at: number;
  duration: number;
  lecture_id: string;
  question?: Question | null;
  explore?: Explore | null;
  case_study?: CaseStudyItem | null;
  isShowed?: boolean;
};

export type FileType = {
  id: string;
  fileTypeName: FileTypeEnum;
  acceptFiles: string | null;
  maxFile: number | null;
  createdAt: string;
  updatedAt: string;
};

// TODO: need to merge VideoId and UploadedFile
export type VideoId = {
  id: string;
  countUsed: number;
  fileTypeId: FileTypeEnum;
  fileName: string;
  fileUrl: string;
  fileDuration: number;
  fileSize: number;
  createdBy: string | null;
  updatedBy: string | null;
  createdAt: string;
  updatedAt: string;
};

export type LectureDetail = {
  id: string;
  lectureTypeId: LectureType;
  testId: string | null;
  videoId: VideoId | null;
  thumbnailFileId: string | null;
  lectureThumbnailImage: string | null;
  lectureName: string;
  slideId: string | null;
  sortIndex: number;
  publish: number;
  createdBy: UserInfo;
  updatedBy: string | null;
  createdAt: string;
  updatedAt: string;

  section: {
    course: CourseInfo;
    createdBy: string | null;
    id: string;
    learningGoalId: string | null;
    lectures: null;
    publish: number | null;
    sectionDuration: number;
    sectionName: string;
    sectionTypeId: number;
    sortIndex: number;
    testId: string | null;
    updatedAt: string;
    updatedBy: string | null;
  };

  lectureInteracts: LectureInteract[] | null;
  slide: Slide | null;

  //MOCK type
  user_answers?: UserAnswer[];
  sectionId: string;
  test: Test | null;
  lecture_segments: LectureSegment[];
  lecture_documents: LectureDocument[];
  video: VideoId | null;
};
