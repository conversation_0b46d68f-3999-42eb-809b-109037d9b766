import { UploadedFile } from '@/features/courses/types/course.type';
import { ChartData, Explore, LayoutContent, Question, ShortClip, TableData } from '@/features/courses/types/slide.type';
import { CaseStudyItem, QuestionOption, TextControl } from '@/type';

export type GetTagsResponse = {
  id: string;
  createdAt: string;
  updatedAt: string;
  tag_name: string;
};

export type SlideItemsResponse = {
  id?: string; // This has id for update and no id for create
  chartData: ChartData;
  updatedBy: string | null;
  backgroundImage: string;
  slideItemTypeId: number;
  slideItemName: string;
  layoutTypeId: number;
  layoutContent: LayoutContent;
  backgroundColor: string;
  tableData: TableData[];
  fileId: number | null;
  isLoop: number;
  sortIndex: number;
  question: Question | null;
  explore: Explore | null;
  caseStudy: CaseStudyItem | null;
  shortClip: ShortClip | null;
  sound?: string | null;
  file?: UploadedFile | null;
};

export interface QuestionResponse {
  id?: string; // This has id for update and no id for create
  questionTypeId?: number;
  questionName: string;
  questionImage: string;
  questionOptions: QuestionOption[] | null;
  correctAnswer: number[];
  questionRequired: boolean;
  questionDuration: number;
  replyRightAnswer: string;
  replyWrongAnswer: string;
  backgroundColor: string;
  createdBy?: number | null;
  updatedBy?: number | null;
  createdAt?: string | null;
  updatedAt?: string | null;
  videoUrl?: string | null;
}

export interface ExploreResponse {
  id?: string;
  slideId: number;
  slideItemTypeId: number;
  slideItemName: string;
  sortIndex: number;
  title: string;
  description: string;
  textBottom: string | null;
  styleCss: TextControl[];
  backgroundType: string;
  backgroundColor: string;
  backgroundImage: string | null;
  fileId: string | null;
  createdBy: number | null;
  updatedBy: number | null;
  createdAt: string | null;
  updatedAt: string | null;
  attachFiles: { id: string; fileName: string; fileUrl?: string; fileSize: number }[];
}

export type LectureInteractResponse = {
  id?: string;
  exploreId: string | null;
  questionId: string | null;
  interactTypeId: number;
  interactName: string;
  startAt: number;
  duration: number;
  lectureId: string;
  question: QuestionResponse | null;
  explore: ExploreResponse | null;
  caseStudy: CaseStudyItem | null;
  isShowed: boolean;
};

export type LectureTestResponse = {
  id?: string;
  testName: string;
  minCorrectAnswer: number;
  hasLimitTime: number | null;
  limitTime: number;
  createdBy: number | null;
  updatedBy: number | null;
  createdAt: string | null;
  updatedAt: string | null;
  questions: QuestionResponse[];
};
