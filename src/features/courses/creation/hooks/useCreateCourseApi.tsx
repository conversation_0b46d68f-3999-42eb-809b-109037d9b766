import { API_ENDPOINTS } from '@/constants/api';
import { CourseListBase, UploadedFile } from '@/features/courses/types';
import { clientFetcher } from '@/lib/clientFetcher';
import { PaginatedQueryBase } from '@/type';
import queryString from 'query-string';
import { SectionCreateRequest, SectionEditRequest, SectionEditResponse } from '../types/';
import {
  CreateCourseRequest,
  CreateLectureTestRequest,
  CreateSectionTestRequest,
  DeleteLectureRequest,
  DeleteLectureTestRequest,
  DeleteQuestionTestRequest,
  DeleteSlideRequest,
  DeleteVideoInteractionRequest,
  LectureCreationRequest,
  PublishCourseRequest,
  UpdateLectureRequest,
} from '../types/requests.type';

const useCreateCourseApi = () => {
  const createCourse = async (
    payload: CreateCourseRequest,
  ): Promise<{ id: string; createdAt: string; updatedAt: string }> => {
    const headers = { 'Content-Type': 'application/json' };
    const config = { headers };

    const res = await clientFetcher.post(API_ENDPOINTS.COURSES.POST.CREATE_COURSE, payload, config);
    return res.data;
  };

  const editCourse = async (request: { id: string; payload: CreateCourseRequest }): Promise<{ id: string }> => {
    const headers = { 'Content-Type': 'application/json' };
    const config = { headers };
    const { payload, id } = request;
    const url = API_ENDPOINTS.COURSES.PUT.EDIT_COURSE.replace(':courseId', id);

    const res = await clientFetcher.put(url, payload, config);
    return res.data;
  };

  const createSection = async (payload: SectionCreateRequest): Promise<{ id: string }> => {
    const res = await clientFetcher.post(API_ENDPOINTS.COURSES.POST.CREATE_SECTION, payload);
    return res.data;
  };

  const editSection = async (payload: SectionEditRequest): Promise<SectionEditResponse> => {
    const query = queryString.stringifyUrl({
      url: API_ENDPOINTS.COURSES.PUT.EDIT_SECTION.replace(':courseId', payload.courseId).replace(
        ':sectionId',
        payload.sectionId,
      ),
    });

    const request = { section_name: payload?.sectionName, sort_index: payload?.sort_index };

    const res = await clientFetcher.put(query, request);
    return res.data;
  };

  const deleteSection = async (payload: { courseId: string; sectionId: string }): Promise<{ id: string }> => {
    const query = queryString.stringifyUrl({
      url: API_ENDPOINTS.COURSES.POST.DELETE_SECTION.replace(':courseId', payload.courseId).replace(
        ':sectionId',
        payload.sectionId,
      ),
    });

    const res = await clientFetcher.delete(query);
    return res.data;
  };

  const publicCourse = async (payload: PublishCourseRequest): Promise<{ id: string }> => {
    const query = queryString.stringifyUrl({
      url: API_ENDPOINTS.COURSES.PATCH.PUBLISH_COURSE.replace(':courseId', payload.courseId.toString()),
    });

    const res = await clientFetcher.patch(query);
    return res.data;
  };

  const uploadCourse = async ({
    lectureId,
    sectionId,
    courseId,
    payload,
  }: {
    lectureId: string;
    sectionId: string;
    courseId: string;
    payload: FormData;
  }) => {
    const headers = { 'Content-Type': 'multipart/form-data' };
    const config = { headers };
    const url = API_ENDPOINTS.COURSES.POST.UPLOAD.replace(':courseId', courseId)
      .replace(':sectionId', sectionId)
      .replace(':lectureId', lectureId);

    const res = await clientFetcher.post(url, payload, config);
    return res;
  };

  const createLecture = async (payload: LectureCreationRequest): Promise<{ id: string }> => {
    const courseId = payload.courseId;
    const sectionId = payload.sectionId;
    const url = API_ENDPOINTS.COURSES.POST.CREATE_LECTURE.replace(':courseId', courseId).replace(
      ':sectionId',
      sectionId,
    );
    const request = {
      lecture_name: payload.lectureName,
      lecture_type_id: payload.lectureTypeId,
      sort_index: payload.sortIndex,
    };

    const res = await clientFetcher.post(url, request);
    return res.data;
  };

  const updateLecture = async (request: UpdateLectureRequest): Promise<{ id: string }> => {
    const url = API_ENDPOINTS.COURSES.PATCH.UPDATE_LECTURE.replace(':courseId', request.courseId)
      .replace(':sectionId', request.sectionId)
      .replace(':lectureId', request.lectureId);

    const { fileId } = request.payload ?? {};

    const res = await clientFetcher.patch(url, {
      file_id: fileId,
    });
    return res.data;
  };

  const editLectureName = async (request: {
    courseId: string;
    sectionId: string;
    lectureId: string;
    payload: { lectureName: string };
  }): Promise<{ id: string }> => {
    const url = API_ENDPOINTS.COURSES.PUT.EDIT_LECTURE.replace(':courseId', request.courseId)
      .replace(':sectionId', request.sectionId)
      .replace(':lectureId', request.lectureId);

    const { lectureName } = request.payload ?? {};

    const res = await clientFetcher.put(url, { lecture_name: lectureName });
    return res.data;
  };

  const deleteLecture = async (payload: DeleteLectureRequest): Promise<{ id: string }> => {
    const courseId = payload.courseId;
    const sectionId = payload.sectionId;
    const url = API_ENDPOINTS.COURSES.DELETE.DELETE_LECTURE.replace(':courseId', courseId)
      .replace(':sectionId', sectionId)
      .replace(':lectureId', payload.lectureId);

    const res = await clientFetcher.delete(url);
    return res.data;
  };

  const getUploadedFiles = async ({
    fileTypes,
    limit,
    page,
    fileName,
  }: { fileTypes: 'VIDEO' | 'IMAGE' | 'AUDIO'; fileName?: string } & Partial<PaginatedQueryBase>) => {
    const query = queryString.stringifyUrl({
      url: API_ENDPOINTS.USERS.GET.FILES,
      query: { fileTypes, limit, page, fileName },
    });

    const res = await clientFetcher(query);
    const resData = res.data as CourseListBase<UploadedFile>;
    return { list: resData.data, total: resData.count };
  };

  const uploadFile = async ({ payload }: { payload: FormData }) => {
    const headers = { 'Content-Type': 'multipart/form-data' };
    const config = { headers };

    const res = await clientFetcher.post(API_ENDPOINTS.USERS.POST.UPLOAD_FILE, payload, config);
    return res.data as {
      id: string;
      createdAt: string;
      updatedAt: string;
      countUsed: number;
      fileType: string;
      fileName: string;
      fileUrl: string;
      fileDuration: number;
      fileSize: number;
    };
  };

  const deleteUploadedFile = async (id: string) => {
    const res = await clientFetcher.delete(API_ENDPOINTS.USERS.DELETE.DELETE_FILE.replace(':id', id));
    return res.data;
  };

  const updateFileName = async (id: string, payload: { fileName: string }) => {
    const res = await clientFetcher.patch(API_ENDPOINTS.USERS.PATCH.RENAME_FILE.replace(':id', id), {
      file_name: payload.fileName,
    });
    return res.data;
  };

  const uploadChunkFile = async (formData: FormData) => {
    const headers = { 'Content-Type': 'multipart/form-data' };
    const config = { headers };

    const res = await clientFetcher.post(API_ENDPOINTS.USERS.POST.UPLOAD_FILE_IN_CHUNK, formData, config);
    return res.data;
  };

  const createSlide = async <T,>(requests: {
    courseId: string;
    sectionId: string;
    lectureId: string;
    slideName: string;
  }) => {
    const url = API_ENDPOINTS.COURSES.POST.CREATE_SLIDE.replace(':courseId', requests.courseId)
      .replace(':sectionId', requests.sectionId)
      .replace(':lectureId', requests.lectureId);

    const res = await clientFetcher.post(url, { slide_name: requests.slideName });
    return res.data;
  };

  const createSlideItem = async <T,>(requests: {
    courseId: string;
    sectionId: string;
    lectureId: string;
    slideId: string;
    payload: T;
  }) => {
    const url = API_ENDPOINTS.COURSES.POST.CREATE_SLIDE_ITEM.replace(':courseId', requests.courseId)
      .replace(':sectionId', requests.sectionId)
      .replace(':lectureId', requests.lectureId)
      .replace(':slideId', requests.slideId);

    const payload = JSON.stringify(requests.payload);

    const res = await clientFetcher.post(url, payload);
    return res.data;
  };

  const deleteSlide = async (request: DeleteSlideRequest) => {
    const url = API_ENDPOINTS.COURSES.DELETE.DELETE_SLIDE.replace(':lectureId', request.lectureId)
      .replace(':slideId', request.slideId)
      .replace(':slideItemId', request.slideItemId);

    const res = await clientFetcher.delete(url);
    return res.data;
  };

  const createQuestion = async <T,>(requests: {
    courseId: string;
    sectionId: string;
    lectureId: string;
    payload: T;
  }) => {
    const url = API_ENDPOINTS.COURSES.POST.CREATE_QUESTION.replace(':courseId', requests.courseId)
      .replace(':sectionId', requests.sectionId)
      .replace(':lectureId', requests.lectureId);

    const payload = JSON.stringify(requests.payload);

    const res = await clientFetcher.post(url, payload);
    return res.data;
  };

  const deleteVideoInteraction = async (request: DeleteVideoInteractionRequest) => {
    const url = API_ENDPOINTS.COURSES.DELETE.DELETE_VIDEO_INTERACTION.replace(':courseId', request.courseId)
      .replace(':sectionId', request.sectionId)
      .replace(':lectureId', request.lectureId)
      .replace(':interactId', request.interactId);

    const res = await clientFetcher.delete(url);
    return res.data;
  };

  const createSectionTest = async (requests: CreateSectionTestRequest) => {
    const url = API_ENDPOINTS.COURSES.POST.CREATE_QUESTION_TEST.replace(':courseId', requests.courseId).replace(
      ':sectionId',
      requests.sectionId,
    );

    const res = await clientFetcher.post(url, requests.payload);
    return res.data;
  };

  const deleteSectionTest = async (request: DeleteQuestionTestRequest) => {
    const url = API_ENDPOINTS.COURSES.DELETE.DELETE_QUESTION_TEST.replace(':courseId', request.courseId)
      .replace(':sectionId', request.sectionId)
      .replace(':testId', request.testId)
      .replace(':questionId', request.questionId);

    const res = await clientFetcher.delete(url);
    return res.data;
  };

  const createLectureTest = async (requests: CreateLectureTestRequest) => {
    const url = API_ENDPOINTS.COURSES.POST.CREATE_LECTURE_TEST.replace(':courseId', requests.courseId)
      .replace(':sectionId', requests.sectionId)
      .replace(':lectureId', requests.lectureId);

    const res = await clientFetcher.post(url, requests.payload);
    return res.data;
  };

  const deleteLectureTest = async (requests: DeleteLectureTestRequest) => {
    const url = API_ENDPOINTS.COURSES.DELETE.DELETE_LECTURE_TEST.replace(':courseId', requests.courseId)
      .replace(':sectionId', requests.sectionId)
      .replace(':lectureId', requests.lectureId)
      .replace(':testId', requests.testId);

    const res = await clientFetcher.delete(url);
    return res.data;
  };

  return {
    publicCourse,
    createCourse,
    editCourse,
    uploadCourse,

    createSection,
    editSection,
    deleteSection,

    createLecture,
    updateLecture,
    editLectureName,
    deleteLecture,

    createSlideItem,
    createSlide,
    deleteSlide,

    deleteVideoInteraction,

    createQuestion,

    createSectionTest,
    deleteSectionTest,

    createLectureTest,
    deleteLectureTest,

    getUploadedFiles,
    uploadFile,
    deleteUploadedFile,
    updateFileName,
    uploadChunkFile,
  };
};

export default useCreateCourseApi;
