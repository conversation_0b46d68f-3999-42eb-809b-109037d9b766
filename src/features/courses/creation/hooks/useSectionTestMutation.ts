import { useCreate<PERSON>rouse<PERSON><PERSON> } from '@/features/courses/creation/hooks';
import { CreateSectionTestRequest, DeleteQuestionTestRequest } from '@/features/courses/creation/types';
import { useRouter } from 'next/navigation';
import { useMutation } from 'react-query';

const useSectionTestMutation = () => {
  const { createSectionTest, deleteSectionTest, createLectureTest, deleteLectureTest } = useCreateCrouseApi();

  const router = useRouter();

  const { isLoading: isCreatingQuestionTest, mutate: handleCreateQuestionTest } = useMutation({
    mutationFn: (variables: CreateSectionTestRequest) => {
      if (variables?.lectureId) {
        return createLectureTest({ ...variables, lectureId: variables.lectureId });
      }

      return createSectionTest(variables);
    },
    onSuccess: (res) => {
      router.refresh();
      return res;
    },
    onError: (error: unknown) => {
      console.error('error: ', error);
    },
  });

  const { isLoading: isDeletingQuestionTest, mutate: handleDeleteQuestionTest } = useMutation({
    mutationFn: (variables: DeleteQuestionTestRequest) => {
      return deleteSectionTest(variables);
    },
    onSuccess: (res) => {
      router.refresh();
      return res;
    },
    onError: (error: unknown) => {
      console.error('error: ', error);
    },
  });

  return {
    isCreatingQuestionTest,
    isDeletingQuestionTest,

    onCreateQuestionTest: handleCreateQuestionTest,
    onDeleteQuestionTest: handleDeleteQuestionTest,
  };
};

export default useSectionTestMutation;
