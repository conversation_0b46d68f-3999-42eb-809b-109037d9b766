import { CourseType, IsSequential } from '@/constants/enum';
import { Form } from 'antd';
import { useForm } from 'antd/es/form/Form';
import React from 'react';
import { CourseDetailInfo } from '../../types';
import { CREATE_COURSE_FIELDS } from '../constants/schema';
import { CreateCourseRequest } from '../types';

const getRequestBody = (values: CreateCourseRequest): CreateCourseRequest => {
  const requestBody: CreateCourseRequest = {
    // required fields
    course_thumbnail_image_id: values.course_thumbnail_image_id, // Only send library file ID
    course_name: values.course_name,
    course_description: values.course_description,
    course_type_id: values.course_type_id,
    topic_id: values.topic_id,
    tag_ids: values.tag_ids || [],
    package_id: values.package_id,
    is_sequential: IsSequential.NO,
    course_level_id: '1' as any,

    // optional fields
    course_related_ids:
      Array.isArray(values.course_related_ids) && values.course_related_ids.length > 0 ? values.course_related_ids : [],
    course_faqs: Array.isArray(values.course_faqs) && values.course_faqs.length > 0 ? values.course_faqs : [],
    // userId: '', // This will be set by the backend or passed separately
  };

  return requestBody;
};

const getDefaultValues = (courseInfo: CourseDetailInfo) => {
  const relatedCourses = courseInfo.courseRelated.map((item) => item.related);

  const defaultValues = {
    [CREATE_COURSE_FIELDS.COURSE_TYPE_ID]: courseInfo?.courseTypeId,
    [CREATE_COURSE_FIELDS.PACKAGE_ID]: courseInfo?.package?.id,
    [CREATE_COURSE_FIELDS.TOPIC_ID]: courseInfo?.topic?.id,
    [CREATE_COURSE_FIELDS.TAG_IDS]: courseInfo?.courseTag?.map(({ tag }) => tag.id),
    [CREATE_COURSE_FIELDS.COURSE_RELATED_IDS]: relatedCourses.map((course) => course.id),
    [CREATE_COURSE_FIELDS.COURSE_NAME]: courseInfo?.courseName,
    [CREATE_COURSE_FIELDS.COURSE_DESCRIPTION]: courseInfo?.courseDescription,
    [CREATE_COURSE_FIELDS.COURSE_THUMBNAIL_IMAGE_ID]: courseInfo?.thumbnailFileId,
    [CREATE_COURSE_FIELDS.AUTHOR]: courseInfo?.createdBy.name,
  };

  return defaultValues;
};

const useCourseCreationForm = ({ courseInfo, isEdit = false }: { courseInfo: CourseDetailInfo; isEdit?: boolean }) => {
  const [form] = useForm();
  const [file, setFile] = React.useState<null | File>(null);

  const courseTypeWatched: CourseType =
    Number(Form.useWatch(CREATE_COURSE_FIELDS.COURSE_TYPE_ID, form)) ?? courseInfo.courseTypeId;

  // const handleTransformUrlToFile = async (url: string) => {
  //   const file = await transformImageUrlToFile(url);
  //   if (file) {
  //     setFile(file);
  //     return file;
  //   }
  // };

  React.useEffect(() => {
    if (isEdit) {
      const initForm = async () => {
        // const thumbnailImageFile = handleTransformUrlToFile(courseInfo.courseThumbnailImage);

        const defaultValues = getDefaultValues(courseInfo);
        form.setFieldsValue(defaultValues);
      };
      initForm();
    }
  }, [isEdit]);

  return {
    form,
    file,
    courseTypeWatched,

    setFile,
    getDefaultValues,
    getRequestBody,
  };
};

export default useCourseCreationForm;
