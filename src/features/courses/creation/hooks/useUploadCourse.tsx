'use client';

import { HttpStatusCode } from '@/constants/api';
import useCreateCourseApi from '@/features/courses/creation/hooks/useCreateCourseApi';
import { validateImageFile } from '@/lib/helpers/fileValidation';
import { App as AntApp } from 'antd';
import { useState } from 'react';

const BYTES_PER_KB = 1024;
const CHUNK_SIZE = BYTES_PER_KB * 800;

const getFilePayload = ({
  chunk,
  chunkNumber,
  totalChunks,
  uploadKey,
}: {
  chunk: Blob;
  chunkNumber: number;
  totalChunks: number;
  uploadKey: string;
}) => {
  const formData = new FormData();
  formData.append('file', chunk);
  formData.append('chunk_number', chunkNumber.toString());
  formData.append('total_chunk', totalChunks.toString());
  formData.append('upload_key', uploadKey);

  return formData;
};

const useUploadCourse = () => {
  const { notification } = AntApp.useApp();
  const [percent, setPercent] = useState(0);

  const isUploading = percent > 0 && percent < 100;

  const { uploadCourse: uploadCourseService } = useCreateCourseApi();

  const uploadFile = async (file: File, configIds: { lectureId: string; sectionId: string; courseId: string }) => {
    const totalChunks = Math.ceil(file.size / CHUNK_SIZE);
    const fileName = file.name;

    let start = 0;
    for (let chunkIndex = 1; chunkIndex <= totalChunks; chunkIndex++) {
      const end = Math.min(start + CHUNK_SIZE, file.size);
      const chunk = file.slice(start, end);

      setPercent(((chunkIndex + 1) / totalChunks) * 100);
      try {
        const response = await uploadChunk(chunk, chunkIndex, totalChunks, fileName, configIds);
        if (response.status === HttpStatusCode.CREATED) {
          start = end;
        }

        const isCompleted = response.data?.id;
        if (isCompleted) {
          return response.data;
        }
      } catch (error) {
        console.error(`Error uploading chunk ${chunkIndex}:`, error);
        throw error;
      }
    }
  };

  async function uploadChunk(
    chunk: Blob,
    chunkNumber: number,
    totalChunks: number,
    fileName: string,
    configIds: {
      lectureId: string;
      sectionId: string;
      courseId: string;
    },
  ) {
    const { lectureId, sectionId, courseId } = configIds;

    const formData = getFilePayload({ chunk, chunkNumber, totalChunks, uploadKey: fileName });

    const response = await uploadCourseService({
      lectureId: String(lectureId),
      sectionId: String(sectionId),
      courseId: String(courseId),
      payload: formData,
    });

    return response;
  }

  const handleBeforeUploadVideo = (file: File): boolean => {
    const { isValid, error } = validateImageFile(file);

    if (!isValid) {
      notification.error({ message: error });
      return false;
    }

    return true;
  };

  const handleUploadVideo = async ({
    lectureId,
    sectionId,
    courseId,
    video,
  }: {
    video: File;
    lectureId: string;
    sectionId: string;
    courseId: string;
  }) => {
    if (!video) return;

    const configIds = { lectureId, sectionId, courseId };

    try {
      const { id = null } = await uploadFile(video, configIds);
      if (id) {
        notification.success({ message: 'Thêm video cho bài học thành công' });
        return { success: true, id, file: video };
      }

      return { success: false, id, file: video };
    } catch {
      notification.error({ message: 'Thêm video cho bài học thất bại' });
    } finally {
      setPercent(0);
    }
  };

  return {
    percent,
    isUploading,

    onBeforeUploadVideo: handleBeforeUploadVideo,
    onUploadVideo: handleUploadVideo,
  };
};

export default useUploadCourse;
