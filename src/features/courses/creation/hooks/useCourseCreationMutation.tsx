import { revalidatePath } from '@/actions/revalidatePath';
import { formatUrlCourseEditSection } from '@/components/learner/utils';
import { routePaths } from '@/config';
import { BaseError, ValidationError } from '@/type';
import { App, notification } from 'antd';
import { AxiosResponse } from 'axios';
import { useRouter } from 'next-nprogress-bar';
import { usePathname } from 'next/navigation';
import { useMutation } from 'react-query';
import {
  CreateLectureTestRequest,
  CreateQuestionRequest,
  CreateSlideItemRequest,
  DeleteSlideRequest,
  UpdateLectureRequest,
} from '../../creation/types';
import useCreateCourseApi from './useCreateCourseApi';

const showErrors = (error: ValidationError) => {
  const errorsMessage = error.subErrors.map((item) => <div key={item}>{item}</div>);
  notification.error({
    message: error.message,
    description: errorsMessage,
  });
};

const showMessageError = (error: AxiosResponse<ValidationError>) => {
  if (error.data?.subErrors && error.data.subErrors.length > 0) {
    showErrors(error.data);
    return;
  }

  notification.error({
    message: error?.data.message,
  });
};

const useCourseCreationMutation = () => {
  const router = useRouter();
  const pathname = usePathname();
  const { notification } = App.useApp();

  const {
    createCourse: createCourseService,
    editCourse: editCourseService,
    editSection: editSectionService,
    deleteSection: deleteSectionService,

    updateLecture: updateLectureService,
    deleteLecture: deleteLectureService,

    createSlideItem: createSlideItemService,
    deleteSlide: deleteSlideService,

    createQuestion: createQuestionService,

    createLectureTest: updateLectureTestService,
  } = useCreateCourseApi();

  const createCourseMutation = useMutation({
    mutationFn: createCourseService,
    onSuccess: () => {
      notification.success({ message: 'Tạo khoá học thành công' });
      revalidatePath(routePaths.profile.children.creator.path, 'page');
    },
    onError: (error: AxiosResponse<ValidationError>) => {
      showMessageError(error);
    },
  });

  const editCourseMutation = useMutation({
    mutationFn: editCourseService,
    onSuccess: async (res) => {
      const id = res.id;
      notification.success({ message: 'Cập nhật khoá học thành công' });

      await revalidatePath(routePaths.profile.children.creator.path, 'layout');

      router.push(formatUrlCourseEditSection(id));
    },
    onError: (error: AxiosResponse<ValidationError>) => {
      showMessageError(error);
    },
  });

  const editSectionMutation = useMutation({
    mutationFn: editSectionService,
    onSuccess: async (res) => {
      notification.success({ message: 'Cập nhật tiêu đề chương thành công' });

      await revalidatePath(pathname, 'page');

      return res;
    },
    onError: (error: AxiosResponse<ValidationError>) => {
      showMessageError(error);
    },
  });

  const deleteSectionMutation = useMutation({
    mutationFn: deleteSectionService,
    onSuccess: async (res) => {
      notification.success({ message: 'Xoá tiêu đề chương thành công' });

      await revalidatePath(pathname, 'page');

      return res;
    },
    onError: (error: BaseError) => {
      notification.error({
        message: error.response?.data?.message,
      });
    },
  });

  const updateLectureMutation = useMutation({
    mutationFn: (request: UpdateLectureRequest) => updateLectureService(request),
    onSuccess: async (res) => {
      notification.success({ message: 'Cập nhật video thành công' });
      await revalidatePath(pathname, 'page');
      return res;
    },
    onError: (error: BaseError) => {
      notification.error({
        message: error.response?.data?.message,
      });
    },
  });

  const deleteLectureMutation = useMutation({
    mutationFn: deleteLectureService,
    onError: (error: BaseError) => {
      notification.error({
        message: error.response?.data?.message,
      });
    },
  });

  const addSlideMutation = useMutation({
    mutationFn: (body: CreateSlideItemRequest) => createSlideItemService(body),
    onSuccess: async (res) => {
      router.refresh();
      return res;
    },
    onError: (error: BaseError['response']) => {
      const errorMessage = error?.data?.message || 'Thêm slide thất bại';
      notification.error({
        message: errorMessage,
      });
    },
  });

  const deleteSlideMutation = useMutation({
    mutationFn: (body: DeleteSlideRequest) => deleteSlideService(body),
    onSuccess: async (res) => {
      router.refresh();
      await revalidatePath(pathname, 'page');
      return res;
    },
    onError: (error: BaseError) => {
      notification.error({
        message: error.response?.data?.message,
      });
    },
  });

  const addVideoQuestionMutation = useMutation({
    mutationFn: (body: CreateQuestionRequest) => createQuestionService(body),
    onSuccess: async (res) => {
      router.refresh();
      return res;
    },
    onError: (error: BaseError['response']) => {
      const errorMessage = error?.data?.message || 'Thêm tương tác cho video thất bại';
      notification.error({
        message: errorMessage,
      });
    },
  });

  const updateLectureTestMutation = useMutation({
    mutationFn: (body: CreateLectureTestRequest) => updateLectureTestService(body),
    onSuccess: async (res) => {
      router.refresh();
      return res;
    },
    onError: (error: BaseError['response']) => {
      const errorMessage = error?.data?.message || 'Cập nhật bài kiểm tra thất bại';
      notification.error({
        message: errorMessage,
      });
    },
  });

  return {
    courseCreating: createCourseMutation.isLoading,
    courseEditing: editCourseMutation.isLoading,
    sectionEditing: editSectionMutation.isLoading,
    sectionDeleting: deleteSectionMutation.isLoading,
    isAddingSlide: addSlideMutation.isLoading,
    isAddingQuestion: addVideoQuestionMutation.isLoading,

    onCreateCourse: createCourseMutation.mutate,

    onEditCourse: editCourseMutation.mutate,

    onEditSection: editSectionMutation.mutate,
    onDeleteSection: deleteSectionMutation.mutate,

    onUpdateLecture: updateLectureMutation.mutate,
    onDeleteLecture: deleteLectureMutation.mutate,

    onAddSlideItem: addSlideMutation.mutate,
    onDeleteSlide: deleteSlideMutation.mutate,

    onAddVideoQuestion: addVideoQuestionMutation.mutate,

    onUpdateLectureTest: updateLectureTestMutation.mutate,
  };
};

export default useCourseCreationMutation;
