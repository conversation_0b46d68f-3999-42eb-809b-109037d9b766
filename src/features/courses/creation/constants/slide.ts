import { chartTypeOptions } from '@/constants/option';
import { SlideItemRequest } from '@/features/courses/creation/types';
import { CaseStudyItem, ChartData, Explore, LayoutContent, Question } from '../../types';

export const initChartData: ChartData = {
  labels: ['tháng 5', 'tháng 6', 'tháng 7'],
  title: '',
  datasets: [
    {
      label: 'Đơn vị 1',
      data: [15, 24, 120],
      borderColor: '#FF96D2',
      backgroundColor: '#FF96D2',
    },
    {
      label: 'Đơn vị 2',
      data: [157, 232, 36],
      borderColor: '#494AFC',
      backgroundColor: '#494AFC',
    },
    {
      label: 'Đơn vị 3',
      data: [126, 12, 63],
      borderColor: '#FFC31E',
      backgroundColor: '#FFC31E',
    },
  ],
  chartType: chartTypeOptions[0].value,
  description_title: '',
  description_body: '',
};

export const initSlideContent: LayoutContent = {
  data: {
    background_image_1: null,
    background_image_2: null,
    background_image_3: null,
    highlightText: null,
    id: null,
    content_1: {
      title: '',
      description: '',
    },
    content_2: {
      title: '',
      description: '',
    },
    content_3: {
      title: '',
      description: '',
    },
    content_4: {
      title: '',
      description: '',
    },
  },
};

export const slideInitial: SlideItemRequest = {
  chart_data: initChartData,
  explore: null,
  sound: null,
  updated_by: null,
  background_image: '',
  slide_item_type_id: 1,
  slide_item_name: '',
  layout_type_id: 1,
  layout_content: initSlideContent,
  case_study: null,
  background_color: '#ffffff',
  question: null,
  table_data: [
    {
      title: '',
      content: '',
    },
    {
      title: '',
      content: '',
    },
  ],
  file_id: null,
  is_loop: 0,
  sort_index: 0,
  short_clip: null,
};

export const questionInitial: Question = {
  question_type_id: 0,
  background_color: '#ffffff',
  question_name: '',
  question_required: true,
  question_duration: 0,
  reply_right_answer: '',
  reply_wrong_answer: '',
  question_image: '',
  correct_answer: [],
  video_url: null,
  question_options: [
    {
      option_index: 1,
      option_name: '',
      option_thumbnail_image: '',
    },
    {
      option_index: 2,
      option_name: '',
      option_thumbnail_image: '',
    },
  ],
};

export const caseStudyInitial = {
  case_study_type_id: 0,
  slide_item_name: 'Tình huống',
  sort_index: 0,
  title: 'Nghiên cứu tình huống',
  description: '',
  text_center_title: '',
  text_center_description: '',
  answers: [
    {
      name: '',
      answer: '',
      style_css: {
        textType: 'NOI_DUNG',
        font: {
          textAlign: 'left',
          fontFamily: 'Inter',
          fontWeight: 'normal',
          fontSize: 16,
          action: 0,
          color: '#ffffff',
        },
        background: {
          type: 'MAU_NEN',
          backgroundColor: '#ffffff',
          opacity: 25,
        },
      },
    },
    {
      name: '',
      answer: '',
      style_css: {
        textType: 'NOI_DUNG',
        font: {
          textAlign: 'left',
          fontFamily: 'Inter',
          fontWeight: 'normal',
          fontSize: 16,
          action: 0,
          color: '#ffffff',
        },
        background: {
          type: 'MAU_NEN',
          backgroundColor: '#ffffff',
          opacity: 25,
        },
      },
    },
    {
      name: 'Thêm trường hợp',
    },
  ],
  correct_answer: null,
  background_type: 0,
  background_color: '#000000',
  background_image: '',
} satisfies CaseStudyItem;

export const exploreInitial = {
  title: '',
  description: '',
  text_bottom: null,
  style_css: [
    {
      textType: 'NOI_DUNG 2',
      font: {
        textAlign: 'left',
        fontFamily: 'Inter',
        fontWeight: 'normal',
        fontSize: 16,
        action: 0,
        color: '#ffffff',
      },
      background: {
        type: 'MAU_NEN',
        backgroundColor: '#000000',
        opacity: 100,
      },
    },
  ],
  background_type: '1',
  background_color: '#000000',
  background_image: null,
  attach_files: [],
  created_by: 773,
  updated_by: 773,
  created_at: '',
  updated_at: '',
  sort_index: 0,
  slide_item_type_id: 3,
  slide_item_name: '',
  file_id: null,
} satisfies Explore;
