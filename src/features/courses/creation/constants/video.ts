export const quickQuestionInitialForVideo = {
  question_type_id: 0,
  background_color: '#ffffff',
  question_name: '',
  question_required: true,
  question_duration: 0,
  reply_right_answer: '',
  reply_wrong_answer: '',
  question_image: '',
  correct_answer: [],
  video_url: null,
  question_options: [
    {
      option_index: 1,
      option_name: 'dap an 01',
      option_thumbnail_image: '',
    },
    {
      option_index: 2,
      option_name: 'dap an 02',
      option_thumbnail_image: '',
    },
  ],
};

export const exploreInitialForVideo = {
  slide_id: 0,
  slide_item_type_id: 3,
  slide_item_name: '',
  sort_index: 0,
  explore_id: null,
  title: 'Tìm hiểu thêm',
  description: '',
  text_bottom: '',
  style_css: [
    {
      textType: 'NOI_DUNG',
      font: {
        textAlign: 'left',
        fontFamily: 'Inter',
        fontWeight: 'normal',
        fontSize: 16,
        action: 0,
        color: '#ffffff',
      },
      background: {
        type: 'MAU_NEN',
        backgroundColor: '#000000',
        opacity: 100,
      },
    },
  ],
  background_type: 1,
  background_color: '#000000',
  background_image: '',
  file_id: null,
  attach_files: [],
};

export const interactionInitial = {
  interact_type_id: 2,
  interact_name: '',
  start_at: 0,
  duration: 5,
  question: quickQuestionInitialForVideo,
  explore: {
    slide_id: 0,
    slide_item_type_id: 3,
    slide_item_name: '',
    sort_index: 0,
    explore_id: null,
    title: 'Tìm hiểu thêm',
    description: '',
    text_bottom: '',
    style_css: [
      {
        textType: 'NOI_DUNG',
        font: {
          textAlign: 'left',
          fontFamily: 'Inter',
          fontWeight: 'normal',
          fontSize: 16,
          action: 0,
          color: '#ffffff',
        },
        background: {
          type: 'MAU_NEN',
          backgroundColor: '#000000',
          opacity: 100,
        },
      },
    ],
    background_type: 1,
    background_color: '#000000',
    background_image: '',
    file_id: null,
    attach_files: [],
  },
  case_study: {
    slide_id: 0,
    case_study_type_id: 0,
    slide_item_name: 'Tình huống',
    sort_index: 0,
    case_study_id: null,
    title: 'Nghiên cứu tình huống',
    description: '',
    text_center_title: '',
    text_center_description: '',
    answers: [
      {
        name: '',
        answer: '',
        style_css: {
          textType: 'NOI_DUNG',
          font: {
            textAlign: 'left',
            fontFamily: 'Inter',
            fontWeight: 'normal',
            fontSize: 16,
            action: 0,
            color: '#ffffff',
          },
          background: {
            type: 'MAU_NEN',
            backgroundColor: '#ffffff',
            opacity: 25,
          },
        },
      },
      {
        name: '',
        answer: '',
        style_css: {
          textType: 'NOI_DUNG',
          font: {
            textAlign: 'left',
            fontFamily: 'Inter',
            fontWeight: 'normal',
            fontSize: 16,
            action: 0,
            color: '#ffffff',
          },
          background: {
            type: 'MAU_NEN',
            backgroundColor: '#ffffff',
            opacity: 25,
          },
        },
      },
      {
        name: 'Thêm trường hợp',
      },
    ],
    correct_answer: null,
    background_type: 0,
    background_color: '#000000',
    background_image: '',
  },
};
