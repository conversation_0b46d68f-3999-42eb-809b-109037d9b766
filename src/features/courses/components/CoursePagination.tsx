'use client';

import { Pagination } from 'antd';
import { usePathname, useRouter } from 'next/navigation';
import queryString from 'query-string';
import useSafeSearchParams from '../../../hooks/useSafeSearchParams';
import { CoursesByFilterParams } from '../types/params.type';

const INITIAL_LIMIT = 12;

const useCoursePagination = () => {
  const router = useRouter();
  const pathname = usePathname();

  const { parsedQueryParams } = useSafeSearchParams<CoursesByFilterParams>();

  const onChangePage = (page: number, pageSize: number) => {
    const params = { ...parsedQueryParams, page: page, limit: pageSize };
    router.push(queryString.stringifyUrl({ url: pathname, query: { ...params } }));
  };

  return { onChangePage, parsedQueryParams };
};

const CoursesPagination = ({ total }: { total: number }) => {
  const { onChangePage } = useCoursePagination();
  const { parsedQueryParams } = useSafeSearchParams<CoursesByFilterParams>();

  return (
    <div className="flex items-center justify-center">
      <Pagination
        total={total}
        current={parsedQueryParams.page ? parsedQueryParams.page : 1}
        pageSize={parsedQueryParams.limit || INITIAL_LIMIT}
        onChange={onChangePage}
      />
    </div>
  );
};

export default CoursesPagination;
