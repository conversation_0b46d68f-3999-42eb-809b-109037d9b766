'use client';

import { Icon } from '@/components/client/icon';
import CourseOutLined from '@/icons/CourseOutLined';
import { Space } from 'antd';
import clsx from 'clsx';
import Link from 'next/link';
import { usePathname, useSearchParams } from 'next/navigation';
import querystring from 'query-string';
import 'swiper/css';
import 'swiper/css/navigation';
import { Navigation } from 'swiper/modules';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Topic } from '../types/course.type';

export type CourseTopicsProps = {
  topics: Topic[];
};

const getTopicIcon = (topic: Topic, topicId: string) => {
  const isAllTopic = !topic.id;

  const icon = topic.icon;

  if (isAllTopic) {
    return <CourseOutLined color={topicId === topic.id ? clsx('text-primary') : ''} />;
  }

  return <Icon icon={icon} className="text-primary" />;
};

function CourseTopics(props: CourseTopicsProps) {
  const { topics } = props;

  const params = useSearchParams();

  const pathname = usePathname();

  const topicId = params.get('topic') ?? '';

  return (
    <div className="flex w-full gap-12">
      <div className="w-full">
        <Swiper className={'swiper-custom-navigation'} modules={[Navigation]} navigation slidesPerView={'auto'}>
          {topics.splice(0, 8).map((topic) => {
            const topicIcon = getTopicIcon(topic, topicId);

            return (
              <SwiperSlide key={`topic-${topic.id}`} className={'mr-4 !w-[150px]'}>
                <Link
                  className={
                    topicId === topic.id ? 'text-center text-xs text-primary' : 'text-center text-xs text-black'
                  }
                  scroll={false}
                  href={querystring.stringifyUrl({
                    url: pathname,
                    query: {
                      ...querystring.parse(params.toString()),
                      topic: topic.id,
                      page: 1,
                    },
                  })}
                >
                  <div className="flex cursor-pointer justify-center hover:text-primary">
                    <Space direction={'vertical'} align={'center'} size={13}>
                      {topicIcon}

                      <p className={topicId === topic.id ? 'text-center text-xs text-primary' : 'text-center text-xs'}>
                        {topic.topicName}
                      </p>
                    </Space>
                  </div>
                </Link>
              </SwiperSlide>
            );
          })}
        </Swiper>
      </div>
    </div>
  );
}

export default CourseTopics;
