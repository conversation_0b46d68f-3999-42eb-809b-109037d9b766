import {
  AttachFile,
  Explore,
  ExploreResponse,
  LectureDetail,
  LectureInteract,
  LectureInteractResponse,
  LectureTestResponse,
  Question,
  QuestionResponse,
  Slide,
  SlideItem,
  SlideItemsResponse,
} from '@/features/courses/types';

export const mapSlideItemResponse = (slide: SlideItemsResponse) => {
  return {
    chart_data: slide.chartData,
    updated_by: slide.updatedBy,
    id: slide.id,
    background_image: slide.backgroundImage,
    slide_item_type_id: slide.slideItemTypeId,
    slide_item_name: slide.slideItemName,
    layout_type_id: slide.layoutTypeId,
    layout_content: slide.layoutContent,
    background_color: slide.backgroundColor,
    table_data: slide.tableData,
    file_id: slide.file ? slide.file?.id : '',
    is_loop: slide.isLoop,
    sort_index: slide.sortIndex,
    question: slide.question,
    explore: slide.explore,
    case_study: slide.caseStudy,
    short_clip: slide.shortClip,
    sound: slide.file,
  } satisfies SlideItem;
};

export const mapQuestionResponse = (question: QuestionResponse | null) => {
  if (!question) return question;

  return {
    id: question.id,
    question_type_id: question.questionTypeId,
    question_name: question.questionName,
    question_image: question.questionImage,
    question_options: question.questionOptions,
    correct_answer: question.correctAnswer,
    question_required: question.questionRequired,
    question_duration: question.questionDuration,
    reply_right_answer: question.replyRightAnswer,
    reply_wrong_answer: question.replyWrongAnswer,
    background_color: question.backgroundColor,
    created_by: question.createdBy,
    updated_by: question.updatedBy,
    created_at: question.createdAt,
    updated_at: question.updatedAt,
    video_url: question.videoUrl,
  } satisfies Question;
};

export const mapLectureTestResponse = (lectureTest: LectureTestResponse | null) => {
  if (!lectureTest) return null;

  return {
    id: lectureTest.id,
    test_name: lectureTest.testName,
    min_correct_answer: lectureTest.minCorrectAnswer,
    has_limit_time: lectureTest.hasLimitTime,
    limit_time: lectureTest.limitTime,
    created_by: lectureTest.createdBy,
    updated_by: lectureTest.updatedBy,
    created_at: lectureTest.createdAt,
    updated_at: lectureTest.updatedAt,
    questions: lectureTest.questions,
  };
};

export const mapSlidesResponse = (slides: SlideItem[]) => {
  return slides.map((slide) => {
    const questionItem = slide.question as unknown as QuestionResponse;
    const slideItem = slide as unknown as SlideItemsResponse;
    const explore = slide.explore as unknown as ExploreResponse;
    return {
      ...mapSlideItemResponse(slideItem),
      question: slide.question ? mapQuestionResponse(questionItem) : null,
      explore: mapExploreResponse(explore),
    };
  });
};

export const mapLectureInteracts = (lectureInteracts: LectureInteractResponse[]) => {
  return lectureInteracts.map((lectureInteract) => {
    return mapLectureInteractResponse(lectureInteract);
  });
};

export const mapLectureDetail = (lectureDetail: LectureDetail) => {
  const lectureDetailMapped = {
    ...lectureDetail,
    slide: {
      ...lectureDetail?.slide,
      slideItems: mapSlidesResponse(lectureDetail?.slide?.slideItems || []),
    } as Slide,
    lectureInteracts: mapLectureInteracts(
      (lectureDetail?.lectureInteracts as unknown as LectureInteractResponse[]) || [],
    ),
    test: mapLectureTestResponse(lectureDetail?.test as unknown as LectureTestResponse),
  } as LectureDetail;

  return lectureDetailMapped;
};

export const mapExploreResponse = (explore: ExploreResponse | null) => {
  if (!explore) return explore;

  return {
    id: explore.id,
    slide_item_type_id: explore.slideItemTypeId,
    slide_item_name: explore.slideItemName,
    sort_index: explore.sortIndex,
    title: explore.title,
    description: explore.description,
    text_bottom: explore.textBottom,
    style_css: explore.styleCss,
    background_type: explore.backgroundType,
    background_color: explore.backgroundColor,
    background_image: explore.backgroundImage,
    file_id: explore.fileId,
    created_by: explore.createdBy,
    updated_by: explore.updatedBy,
    created_at: explore.createdAt,
    updated_at: explore.updatedAt,
    attach_files: Array.isArray(explore.attachFiles)
      ? explore.attachFiles.map(
          (file) =>
            ({
              id: file.id,
              fileName: file.fileName,
              fileUrl: file.fileUrl || '',
              fileSize: file.fileSize,
            }) satisfies AttachFile,
        )
      : [],
  } satisfies Explore;
};

export const mapLectureInteractResponse = (lectureInteract: LectureInteractResponse) => {
  const question = lectureInteract?.question || null;
  return {
    id: lectureInteract.id,
    interact_type_id: lectureInteract.interactTypeId,
    interact_name: lectureInteract.interactName,
    start_at: lectureInteract.startAt,
    duration: lectureInteract.duration,
    lecture_id: lectureInteract.lectureId,
    question: mapQuestionResponse(question),
    explore: mapExploreResponse(lectureInteract.explore),
    case_study: lectureInteract.caseStudy,
    isShowed: lectureInteract.isShowed,
  } satisfies LectureInteract;
};
