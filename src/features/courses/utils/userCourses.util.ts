import { STATUS_COURSES } from '@/constants';
import { UserCourse } from '../types';

export const getInProgressUserCourses = (userCourses: UserCourse[]) => {
  return userCourses.find((item) => item.isCompleted === STATUS_COURSES.IN_PROGRESS);
};

export const getCompletedUserCourses = (userCourses: UserCourse[]) => {
  return userCourses.find((item) => item.isCompleted === STATUS_COURSES.COMPLETED);
};

export const getUserCoursesByStatus = (userCourses: UserCourse[], status: STATUS_COURSES) => {
  return userCourses.filter((item) => item.isCompleted === status);
};

export const getNearestUserCourses = (userCourses: UserCourse[]) => {
  return userCourses.sort((a, b) => {
    return new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime();
  });
};
