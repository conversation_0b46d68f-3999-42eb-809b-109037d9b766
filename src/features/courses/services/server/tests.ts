import { API_ENDPOINTS } from '@/constants/api';
import { UserTestResult } from '@/features/courses/types';
import { serverFetcher } from '@/lib/serverFetcher';

export const getTestAnswersForLearner = async (payload: {
  courseId: string;
  sectionId: string;
  testId: string;
  questionId: string;
}) => {
  const { courseId, sectionId, testId, questionId } = payload;

  const url = API_ENDPOINTS.COURSES.GET.LEANER_TEST_ANSWER.replace(':courseId', courseId)
    .replace(':sectionId', sectionId)
    .replace(':testId', testId)
    .replace(':questionId', questionId);

  const { data } = await serverFetcher<UserTestResult[]>(url);
  return data;
};
