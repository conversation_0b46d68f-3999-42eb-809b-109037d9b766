import { API_ENDPOINTS } from '@/constants/api';
import { UserTestAnswerRequest, UserTestResult } from '@/features/courses/types';
import { clientFetcher } from '@/lib/clientFetcher';

export const getLearnerAnswerTest = async (payload: {
  courseId: string;
  sectionId: string;
  testId: string;
  times: number;
}) => {
  const { courseId, sectionId, testId } = payload;

  const url = API_ENDPOINTS.COURSES.POST.LEANER_TEST_ANSWER.replace(':courseId', courseId)
    .replace(':sectionId', sectionId)
    .replace(':testId', testId);

  const res = await clientFetcher.get(url);
  return res?.data as UserTestResult[];
};

export const submitTestQuestion = async (request: UserTestAnswerRequest) => {
  const { courseId, sectionId, testId, data } = request;

  const url = API_ENDPOINTS.COURSES.POST.LEANER_TEST_ANSWER.replace(':courseId', courseId)
    .replace(':sectionId', sectionId)
    .replace(':testId', testId);

  const res = await clientFetcher.post(url, { data });
  return res?.data;
};
