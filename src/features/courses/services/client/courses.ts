'use client';

import { API_ENDPOINTS } from '@/constants/api';
import { clientFetcher } from '@/lib/clientFetcher';
import queryString from 'query-string';
import { CourseInfo, CourseListBase, GetTagsResponse } from '../../types';
import { CoursesBySearchParams } from '../../types/params.type';

export const getCoursesBySearch = async (payload: CoursesBySearchParams) => {
  const { search, limit = 5, page = 0 } = payload;

  const query = queryString.stringifyUrl({
    url: API_ENDPOINTS.COURSES.GET.SEARCH_COURSES,
    query: { search, page, limit },
  });

  const response = await fetch(query);

  if (!response.ok) {
    throw new Error('Failed to fetch data');
  }

  const dataJSON = await response.json();
  return dataJSON.data as CourseListBase<CourseInfo>;
};

export const getTags = async () => {
  const res = await clientFetcher.get(API_ENDPOINTS.COURSES.GET.TAGS);
  const tags = res?.data || [];
  return tags as GetTagsResponse[];
};

export const createTag = async (payload: { tag_name: string }): Promise<{ id: string }> => {
  const res = await clientFetcher.post(API_ENDPOINTS.COURSES.POST.CREATE_TAG, payload);
  return res?.data;
};

export const updateFavoriteCourse = async (payload: { courseId: string; isFavorite: boolean }) => {
  const res = await clientFetcher.post(
    API_ENDPOINTS.COURSES.POST.FAVORITE_COURSE.replace(':courseId', payload.courseId),
    { favorite: payload.isFavorite },
  );
  return res?.data;
};

export const processCourse = async (payload: {
  courseId: string;
  sectionId: string;
  lectureId: string;
  isCompleted: boolean;
}) => {
  const url = API_ENDPOINTS.COURSES.POST.PROCESS_LEARNER_COURSE.replace(':courseId', payload.courseId)
    .replace(':sectionId', payload.sectionId)
    .replace(':lectureId', payload.lectureId);

  const res = await clientFetcher.post(url, { is_complete: payload.isCompleted });
  return res?.data;
};

export const reviewCourse = async (request: {
  courseId: string;
  data: { rating: number; comment?: string; feelings?: string[] };
}) => {
  const url = API_ENDPOINTS.COURSES.POST.COURSE_REVIEW.replace(':courseId', request.courseId);

  const res = await clientFetcher.post(url, request.data);
  return res?.data;
};

export const swapSection = async ({ courseId, from, to }: { courseId: string; from: number; to: number }) => {
  const url = API_ENDPOINTS.COURSES.PATCH.SWAP_SECTION.replace(':courseId', courseId);

  const res = await clientFetcher.patch(url, { from, to });
  return res?.data;
};

export const swapLecture = async ({
  courseId,
  sectionId,
  from,
  to,
}: {
  courseId: string;
  sectionId: string;
  from: number;
  to: number;
}) => {
  const url = API_ENDPOINTS.COURSES.PATCH.SWAP_LECTURE.replace(':courseId', courseId).replace(':sectionId', sectionId);

  const res = await clientFetcher.patch(url, { from, to });
  return res?.data;
};
