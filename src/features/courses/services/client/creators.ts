import { API_ENDPOINTS } from '@/constants/api';
import { clientFetcher } from '@/lib/clientFetcher';
import { UserInfo } from '@/type';
import queryString from 'query-string';
import { CourseListBase } from '../../types';

export const updateFavoriteCreator = async (request: { authorId: string; isFavorite: boolean }) => {
  const query = queryString.stringifyUrl({
    url: API_ENDPOINTS.COURSES.POST.FAVORITE_CREATOR.replace(':authorId', request.authorId),
  });
  const favorite = request.isFavorite;

  const { data } = await clientFetcher.post<CourseListBase<UserInfo>>(query, { favorite });
  return data;
};
