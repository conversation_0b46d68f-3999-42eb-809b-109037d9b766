'use client';

import { createContext, PropsWithChildren, use, useMemo, useState } from 'react';
import { UserInfo } from 'type/common';

type AuthProviderProps = {
  userInfoDefault: UserInfo | null;
  isAuthenticated: boolean;
  isVipUser: boolean;
  accessToken: string;
} & PropsWithChildren;

type AuthContextState = {
  userInfo: UserInfo | null;
  isAuthenticated: boolean;
  isVipUser: boolean;
};

type AuthContextActions = {
  setUserInfo: (userInfo: UserInfo) => void;
};

type AuthContextValue = AuthContextState & AuthContextActions;

const AuthContext = createContext<AuthContextState | undefined>(undefined);

export function AuthProvider(props: AuthProviderProps) {
  const { userInfoDefault, isAuthenticated, isVipUser, children } = props;

  const [userInfo, setUserInfo] = useState(userInfoDefault);

  const handleSetUserInfo = (userInfo: UserInfo) => {
    if (userInfo?.id) {
      setUserInfo(userInfo);
    }
  };

  const value = useMemo(
    () => ({
      isVipUser,
      isAuthenticated,
      userInfo,
      setUserInfo: handleSetUserInfo,
    }),
    [isVipUser, isAuthenticated, userInfo],
  ) as AuthContextValue;

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export const useAuthProvider = () => {
  const ctx = use(AuthContext);

  if (!ctx) {
    throw new Error('useAuthProvider must be used within a AuthProvider');
  }

  return ctx as AuthContextValue;
};
