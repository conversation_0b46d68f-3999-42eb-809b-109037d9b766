import { getAccessToken, getUserFromCookie } from '@/actions/cookieAction';
import { revalidatePath } from '@/actions/revalidatePath';
import { routePaths } from '@/config';
import { API_ENDPOINTS } from '@/constants/api';
import { useUserInfoProvider } from '@/utils/providers/UserInfoProvider';
import { AxiosError } from 'axios';
import { useRouter } from 'next-nprogress-bar';
import { ReactNode, useState, useTransition } from 'react';
import { LoginRequest } from '../types/requests.type';
import { useAuthApi } from './useAuthApi';

const useAuth = () => {
  const router = useRouter();

  const { userInfo, setUserInfo } = useUserInfoProvider();

  const { login, logout } = useAuthApi();

  const [isPending, startTransition] = useTransition();

  const [errors, setErrors] = useState<ReactNode | string | null>(null);

  const handleLogin = async (values: LoginRequest) => {
    setErrors(null);

    startTransition(async () => {
      try {
        const res = await login(values);

        if (res.status === 201 || res.status === 200) {
          const { userInfoParsed } = await getUserFromCookie();
          const accessToken = await getAccessToken();
          const newUserInfo = { info: userInfoParsed, token: accessToken ?? '' };
          setUserInfo(newUserInfo);

          revalidatePath(routePaths.homePage, 'layout');
          router.push(routePaths.profile.path);
          return;
        }

        throw new Error(res.data);
      } catch (error: unknown) {
        if (error instanceof AxiosError) {
          const errorData = error?.response?.data;
          const message = (errorData as { message: string }).message;
          setErrors(<div>{message}</div>);
        }
      }
    });
  };

  const handleLoginByGoogle = () => {
    router.push(API_ENDPOINTS.USERS.GET.GOOGLE_LOGIN);
  };

  const handleLogout = async () => {
    const userId = userInfo?.info.id || '';
    await logout({ userId });
    await revalidatePath(routePaths.homePage, 'layout');
    router.push(routePaths.login);
  };

  return {
    errors,
    loginLoading: isPending,

    onLogin: handleLogin,
    onLoginByGoogle: handleLoginByGoogle,
    logout: handleLogout,
  };
};

export default useAuth;
