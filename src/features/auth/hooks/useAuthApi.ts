import { API_ENDPOINTS } from '@/constants/api';
import { useAxios } from 'hooks';
import { LoginRequest, RegisterRequest } from '../types/requests.type';

export function useAuthApi() {
  const http = useAxios();

  const login = async (payload: LoginRequest) => http.post(API_ENDPOINTS.USERS.POST.LOGIN, payload);

  const register = async (payload: RegisterRequest) => http.post(API_ENDPOINTS.USERS.POST.REGISTER, payload);

  const logout = async (payload: { userId: string }) => {
    return http.post(API_ENDPOINTS.USERS.POST.LOGOUT, payload);
  };

  const confirmEmail = async (payload: { confirm_code: string }) => {
    return http.post(API_ENDPOINTS.USERS.POST.CONFIRM_EMAIL, payload);
  };

  const resendEmail = async (payloadResend: { name: string; email: string }) =>
    http.post(API_ENDPOINTS.USERS.POST.RESEND_EMAIL, payloadResend);

  const googleLoginUrlCallback = async (callbackUrl: string) =>
    http.get(`${API_ENDPOINTS.USERS.GET.GOOGLE_LOGIN_URL_CALLBACK}${callbackUrl}`);

  return {
    login,
    register,
    logout,
    confirmEmail,
    resendEmail,
    googleLoginUrlCallback,
  };
}
