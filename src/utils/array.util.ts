export const swapElements = <T>(elements: T[], srcIndex: number, desIndex: number, sortField: string = 'sortIndex') => {
  if (srcIndex < 0 || srcIndex >= elements.length || desIndex < 0 || desIndex >= elements.length) {
    return elements;
  }

  const newElements = [...elements];
  const [removed] = newElements.splice(srcIndex, 1);
  newElements.splice(desIndex, 0, removed);
  return newElements.map((item, index) => ({
    ...item,
    [sortField]: index + 1,
  }));
};
