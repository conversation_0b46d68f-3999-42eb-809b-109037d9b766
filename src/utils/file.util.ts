export async function transformImageUrlToFile(url: string) {
  const fileExtension = url.split('.').pop()?.toLowerCase();
  const mimeType = fileExtension === 'jpg' ? 'image/jpeg' : `image/${fileExtension}`;
  const fileName = url.split('/').pop()?.split('.')[0] || '';

  try {
    const response = await fetch(url);
    if (!response.ok) throw new Error('Failed to fetch image');

    const blob = await response.blob();

    const file = new File([blob], `${fileName}.${fileExtension}`, {
      type: blob.type || mimeType,
    });

    return file;
  } catch (error) {
    console.error('Error:', error);
    return null;
  }
}

export const downloadFile = async (url: string, name: string) => {
  try {
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error('Failed to download file');
    }

    const blob = await response.blob();
    const downloadUrl = window.URL.createObjectURL(blob);

    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = name;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(downloadUrl);
  } catch (error) {
    console.error('Download error:', error);
  }
};

export const convertImageFileToBase64 = (file: File) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = (error) => reject(error);
  });
};

export const generateThumbnail = async ({
  file,
  video,
  onSuccess,
}: {
  file: File;
  video: HTMLVideoElement;
  onSuccess: (thumbnail: string) => void;
}): Promise<void> => {
  try {
    // Create a blob URL for the video
    const videoUrl = URL.createObjectURL(file);
    if (!video) return;

    video.src = videoUrl;
    video.muted = true;
    video.playsInline = true;
    video.preload = 'metadata';

    // Wait for video metadata to load
    video.onloadedmetadata = async () => {
      // Seek to a specific time (e.g., 1 second) to capture a frame
      video.currentTime = 1;

      // Wait for the video to seek to the desired time
      await new Promise((resolve) => {
        video.onseeked = resolve;
      });

      // Create a canvas to draw the video frame
      const canvas = document.createElement('canvas');
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;
      const ctx = canvas.getContext('2d');
      if (!ctx) return;

      // Draw the current video frame on the canvas
      ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

      // Convert the canvas to a base64 image
      const thumbnail = canvas.toDataURL('image/jpeg');

      // Set the thumbnail URL in state
      onSuccess(thumbnail);

      // Clean up the blob URL
      URL.revokeObjectURL(videoUrl);
    };

    // Play the video briefly to ensure the frame is loaded
    video.play().catch((err) => {
      console.error('Error playing video:', err);
    });
  } catch (err) {
    console.error('Error generating thumbnail:', err);
  }
};

export const getDurationFromVideoFile = async (file: File) => {
  const videoUrl = URL.createObjectURL(file);
  const video = document.createElement('video');
  video.src = videoUrl;

  try {
    const duration: number = await new Promise((resolve, reject) => {
      const cleanup = () => {
        video.removeEventListener('loadedmetadata', onLoadedMetadata);
        video.removeEventListener('error', onError);
        clearTimeout(timeoutId);
      };

      const onLoadedMetadata = () => {
        if (typeof video.duration === 'number' && video.duration) {
          cleanup();
          resolve(video.duration);
        } else {
          cleanup();
          reject(new Error('Invalid video duration'));
        }
      };

      const onError = () => {
        cleanup();
        reject(new Error('Failed to load video metadata'));
      };

      video.addEventListener('loadedmetadata', onLoadedMetadata);
      video.addEventListener('error', onError);

      const timeoutId = setTimeout(() => {
        cleanup();
        reject(new Error('Timeout waiting for video metadata'));
      }, 5000);
    });

    return duration;
  } finally {
    URL.revokeObjectURL(videoUrl);
  }
};

export const convertKbToMb = (sizeInKb: number): number => {
  return Number((sizeInKb / 1024).toFixed(2));
};

export const convertMbToKb = (sizeInMb: number): number => {
  return Number((sizeInMb * 1024).toFixed(2));
};

export const convertFileSize = (bytes: number): string => {
  const GB = 1024 * 1024 * 1024;
  const MB = 1024 * 1024;
  const KB = 1024;

  if (bytes >= GB) {
    return `${(bytes / GB).toFixed(2)} GB`;
  } else if (bytes >= MB) {
    return `${(bytes / MB).toFixed(2)} MB`;
  } else if (bytes >= KB) {
    return `${(bytes / KB).toFixed(2)} KB`;
  } else {
    return `${bytes} bytes`;
  }
};
