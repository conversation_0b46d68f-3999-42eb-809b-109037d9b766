/**
 * Extract plain text from HTML content
 * Similar to ReactQuill's getText() method
 */
const getPlainTextFromHtml = (html: string | null | undefined): string => {
  if (!html) return '';

  // Create temporary DOM element to extract text
  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = html;

  // Get plain text (removes all HTML tags)
  const plainText = tempDiv.textContent || tempDiv.innerText || '';

  return plainText.trim();
};

/**
 * Check if HTML content is empty (no actual text)
 */
const isHtmlContentEmpty = (html: string | null | undefined): boolean => {
  const plainText = getPlainTextFromHtml(html);
  return plainText.length === 0;
};

/**
 * Get text length from HTML content
 */
const getHtmlContentLength = (html: string | null | undefined): number => {
  const plainText = getPlainTextFromHtml(html);
  return plainText.length;
};

export { getHtmlContentLength, getPlainTextFromHtml, isHtmlContentEmpty };
