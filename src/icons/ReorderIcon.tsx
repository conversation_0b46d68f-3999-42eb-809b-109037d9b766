import { SVGProps } from 'react';

const ReorderIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg xmlns="http://www.w3.org/2000/svg" width={24} height={24} viewBox="0 0 24 24" fill="none" {...props}>
    <circle cx="8.5" cy={6} r="1.5" fill="#0C0C0C" />
    <circle cx="8.5" cy={12} r="1.5" fill="#0C0C0C" />
    <circle cx="8.5" cy={18} r="1.5" fill="#0C0C0C" />
    <circle cx="15.5" cy={6} r="1.5" fill="#0C0C0C" />
    <circle cx="15.5" cy={12} r="1.5" fill="#0C0C0C" />
    <circle cx="15.5" cy={18} r="1.5" fill="#0C0C0C" />
  </svg>
);

export default ReorderIcon;
