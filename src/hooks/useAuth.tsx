import { API_ENDPOINTS, HttpStatusCode } from '@/constants/api';
import { AxiosError } from 'axios';
import { useRouter } from 'next-nprogress-bar';
import { ReactNode, useState, useTransition } from 'react';
import { getAccessToken, getUserFromCookie } from '../actions/cookieAction';
import { revalidatePath } from '../actions/revalidatePath';
import { routePaths } from '../config';
import { useUserInfoProvider } from '../utils/providers/UserInfoProvider';
import { LoginPayloadProps, useAuthApi } from './apis';

const useAuth = () => {
  const router = useRouter();

  const { userInfo, setUserInfo } = useUserInfoProvider();

  const { login, logout } = useAuthApi();

  const [isPending, startTransition] = useTransition();

  const [errors, setErrors] = useState<ReactNode | string | null>(null);

  const handleLogin = async (values: LoginPayloadProps) => {
    setErrors(null);

    const payload = { email: values.email?.trim(), password: values.password?.trim() };

    startTransition(async () => {
      try {
        const res = await login(payload);

        if (res.status === HttpStatusCode.CREATED || res.status === HttpStatusCode.SUCCESS) {
          const { userInfoParsed } = await getUserFromCookie();
          const accessToken = await getAccessToken();
          const newUserInfo = { info: userInfoParsed, token: accessToken ?? '' };
          setUserInfo(newUserInfo);

          const redirectUrl = new URLSearchParams(window.location.search).get('redirectUrl');
          if (redirectUrl) {
            const decodedRedirectUrl = decodeURIComponent(redirectUrl);
            router.push(decodedRedirectUrl);
            return;
          }

          revalidatePath(routePaths.homePage, 'layout');
          router.push(routePaths.profile.path);
          return;
        }

        throw new Error(res.data);
      } catch (error: unknown) {
        if (error instanceof AxiosError) {
          const errorData = error?.response?.data;
          const message = (errorData as { message: string }).message;
          setErrors(<div>{message}</div>);
        }
      }
    });
  };

  const handleLoginByGoogle = () => {
    router.push(API_ENDPOINTS.USERS.GET.GOOGLE_LOGIN);
  };

  const handleLogout = async () => {
    const userId = userInfo?.info.id || '';
    try {
      const res = await logout({ userId });
      if (res) {
        await revalidatePath(routePaths.homePage, 'layout');
        router.push(routePaths.login);
      }
    } catch (error) {
      console.error('error: ', error);
    }
  };

  return {
    errors,
    loginLoading: isPending,

    onLogin: handleLogin,
    onLoginByGoogle: handleLoginByGoogle,
    logout: handleLogout,
  };
};

export default useAuth;
