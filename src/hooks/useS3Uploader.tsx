'use client';

import { S3MultipartUploader } from '@/lib/s3-uploader';
import React from 'react';

const useS3Uploader = () => {
  const [isUploading, setIsUploading] = React.useState<boolean>(false);
  const [progress, setProgress] = React.useState<number>(0);
  const uploaderRef = React.useRef<S3MultipartUploader | null>(null);

  const uploadFile = async <T = unknown,>(
    { file, fileDuration }: { file: File; fileDuration?: number },
    options?: { onSuccess?: (uploadedRes: T) => void; onError?: (error: unknown) => void },
  ) => {
    const { onSuccess, onError } = options ?? {};

    try {
      setIsUploading(true);
      setProgress(0);
      const s3Uploader = new S3MultipartUploader(file);
      uploaderRef.current = s3Uploader;

      const uploadedRes = await s3Uploader.uploadFile({
        fileDuration,
        onProgress: (progress) => setProgress(progress),
      });

      onSuccess?.(uploadedRes as T);
    } catch (error) {
      onError?.(error);
    } finally {
      setIsUploading(false);
      setProgress(0);
      uploaderRef.current = null;
    }
  };

  const abortUpload = () => {
    if (uploaderRef.current) {
      uploaderRef.current.abortUpload();
    }
  };

  return { isUploading, progress, uploadFile, abortUpload };
};

export default useS3Uploader;
