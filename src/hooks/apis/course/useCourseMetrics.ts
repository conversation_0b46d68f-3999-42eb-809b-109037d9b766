import { useQuery } from 'react-query';
import { useCourse } from './index';

export const useCourseMetricsDetailed = (
  courseId: string | number,
  fromDate?: string,
  toDate?: string,
  options?: {
    enabled?: boolean;
  }
) => {
  const { getCourseMetricsDetailed } = useCourse();
  
  return useQuery(
    ['courseMetricsDetailed', courseId, fromDate, toDate],
    () => getCourseMetricsDetailed(courseId, fromDate, toDate),
    {
      enabled: options?.enabled ?? true,
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
    }
  );
};