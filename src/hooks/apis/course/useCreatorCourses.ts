'use client';

import { useState, useEffect } from 'react';
import { useQuery } from 'react-query';
import { API_ENDPOINTS } from '@/constants/api';
import { clientFetcher } from '@/lib/clientFetcher';
import { CourseInfo } from '@/features/courses/types/course.type';
import { CourseListBase } from '@/features/courses/types/common.type';
import queryString from 'query-string';

interface UseCreatorCoursesParams {
  limit?: number;
  page?: number;
  publish?: number;
  name?: string;
}

interface UseCreatorCoursesResult {
  courses: CourseInfo[];
  totalCount: number;
  currentPage: number;
  limit: number;
  isLoading: boolean;
  isError: boolean;
  error: any;
  refetch: () => void;
  searchName: string;
  setSearchName: (name: string) => void;
}

const fetchAllCoursesOfUser = async (params: UseCreatorCoursesParams) => {
  const { page = 0, limit = 10, publish, name } = params;
  
  const queryParams: Record<string, any> = { page, limit };
  if (publish !== undefined) queryParams.publish = publish;
  if (name && name.trim()) queryParams.name = name.trim();

  const query = queryString.stringifyUrl({
    url: API_ENDPOINTS.COURSES.GET.ALL_COURSES,
    query: queryParams,
  });

  const response = await clientFetcher.get(query);
  return response.data as CourseListBase<CourseInfo>;
};

export const useCreatorCourses = (
  initialParams: Omit<UseCreatorCoursesParams, 'name'> = {}
): UseCreatorCoursesResult => {
  const [searchName, setSearchName] = useState('');
  const { limit = 10, page = 0, publish } = initialParams;

  const {
    data,
    isLoading,
    isError,
    error,
    refetch,
  } = useQuery(
    ['creatorCourses', page, limit, publish, searchName],
    () => fetchAllCoursesOfUser({ page, limit, publish, name: searchName }),
    {
      keepPreviousData: true,
      staleTime: 5 * 60 * 1000, // 5 minutes
    }
  );

  return {
    courses: data?.data || [],
    totalCount: data?.count || 0,
    currentPage: data?.page || 0,
    limit: data?.limit || limit,
    isLoading,
    isError,
    error,
    refetch,
    searchName,
    setSearchName,
  };
};