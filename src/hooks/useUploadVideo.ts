'use client';

import { G<PERSON><PERSON>Y<PERSON> } from '@/constants/file';
import { useCreateCrouseApi } from '@/features/courses';
import useSafeSearchParams from '@/hooks/useSafeSearchParams';
import { S3MultipartUploader } from '@/lib/s3-uploader';
import { App } from 'antd';
import { useParams, useRouter } from 'next/navigation';
import React from 'react';

const CHUNK_SIZE = 1 * GIGABYTE;

const useUploadVideo = () => {
  const { parsedQueryParams } = useSafeSearchParams<{ lectureId: string; sectionId: string }>();
  const params: { courseId: string } = useParams();
  const router = useRouter();
  const { notification } = App.useApp();

  const { updateLecture: updateLectureService } = useCreateCrouseApi();

  const [loading, setLoading] = React.useState(false);

  const uploadVideoMultipart = async ({ file }: { file: File }) => {
    setLoading(true);
    try {
      const uploader = new S3MultipartUploader(file);
      const uploadedRes = await uploader.uploadFile();

      const updatedLectureRes = await updateLectureService({
        lectureId: parsedQueryParams?.lectureId,
        sectionId: parsedQueryParams?.sectionId,
        courseId: params?.courseId,
        payload: { fileId: uploadedRes.id },
      });

      const success = !!updatedLectureRes.id;
      if (success) {
        notification.success({ message: 'Thêm video cho bài học thành công' });
        router.refresh();
      }
      return uploadedRes;
    } catch (error: unknown) {
      console.error('upload video failed: ', error);
      notification.error({ message: 'Thêm video cho bài học thất bại' });
    } finally {
      setLoading(false);
    }
  };

  return {
    uploading: loading,
    uploadVideoMultipart,
  };
};

export default useUploadVideo;
