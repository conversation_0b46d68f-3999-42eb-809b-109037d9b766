import { RefObject, useEffect } from 'react';

export function useClickOutside(ref: RefObject<HTMLElement>, onClickOutside: (event: MouseEvent | TouchEvent) => void) {
  useEffect(() => {
    function handleEvent(event: MouseEvent | TouchEvent) {
      const el = ref?.current;
      if (!el || el.contains(event.target as Node)) return;

      onClickOutside(event);
    }

    document.addEventListener('mousedown', handleEvent);
    document.addEventListener('touchstart', handleEvent);

    return () => {
      document.removeEventListener('mousedown', handleEvent);
      document.removeEventListener('touchstart', handleEvent);
    };
  }, [ref, onClickOutside]);
}
