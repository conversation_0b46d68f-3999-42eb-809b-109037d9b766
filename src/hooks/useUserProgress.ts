'use client';

import { useQuery } from 'react-query';
import { useCourse } from './apis/course';
import { UserProgressResponse } from 'types/userProgress';

export interface UseUserProgressProps {
  userId?: string;
  lectureId?: string;
  enabled?: boolean;
}

export const useUserProgress = ({ userId, lectureId, enabled = true }: UseUserProgressProps) => {
  const { getUserProgress } = useCourse();

  return useQuery<UserProgressResponse, Error>(
    ['userProgress', userId, lectureId],
    () => {
      if (!userId || !lectureId) {
        throw new Error('userId and lectureId are required');
      }
      return getUserProgress({ userId, lectureId });
    },
    {
      enabled: enabled && !!userId && !!lectureId,
      staleTime: 30000, // 30 seconds
      cacheTime: 300000, // 5 minutes
      refetchOnWindowFocus: false,
      retry: (failureCount, error) => {
        // Don't retry if it's a 404 (no progress found)
        if (error?.message?.includes('404')) {
          return false;
        }
        return failureCount < 3;
      },
    }
  );
};