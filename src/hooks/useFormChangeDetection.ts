import { useCourseStore } from '@/z-store/course.store';
import { FormInstance } from 'antd';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

// Deep equality check for form values
const isEqual = (obj1: unknown, obj2: unknown): boolean => {
  if (obj1 === obj2) return true;
  if (obj1 == null || obj2 == null) return obj1 === obj2;
  if (typeof obj1 !== typeof obj2) return false;
  if (typeof obj1 !== 'object') return obj1 === obj2;

  // Handle arrays
  if (Array.isArray(obj1) && Array.isArray(obj2)) {
    if (obj1.length !== obj2.length) return false;
    return obj1.every((item, index) => isEqual(item, obj2[index]));
  }

  // Handle objects
  const keys1 = Object.keys(obj1 as object);
  const keys2 = Object.keys(obj2 as object);

  if (keys1.length !== keys2.length) return false;

  for (const key of keys1) {
    if (!keys2.includes(key)) return false;
    if (!isEqual((obj1 as any)[key], (obj2 as any)[key])) return false;
  }

  return true;
};

export const useFormChangeDetection = (form: FormInstance, initialValues?: any) => {
  const { hasUnsavedChanges, setHasUnsavedChanges, resetUnsavedChanges } = useCourseStore();
  const [initialFormValues, setInitialFormValues] = useState<any>(null);
  const router = useRouter();
  useEffect(() => {
    if (initialValues && !initialFormValues) {
      // Wait for form to be properly initialized
      setTimeout(() => {
        const formValues = form.getFieldsValue();
        // Use form values if they exist, otherwise use initial values
        const hasFormData = Object.keys(formValues).some(
          (key) => formValues[key] !== undefined && formValues[key] !== null && formValues[key] !== '',
        );
        const valuesToUse = hasFormData ? formValues : initialValues;
        setInitialFormValues(valuesToUse);
      }, 100);
    }
  }, [initialValues, initialFormValues, form]);

  useEffect(() => {
    const checkForChanges = () => {
      if (!initialFormValues) return;

      const currentValues = form.getFieldsValue();
      const hasChanges = !isEqual(currentValues, initialFormValues);

      setHasUnsavedChanges(hasChanges);
    };

    // Initial check
    checkForChanges();

    // Set up an interval to check for changes (for cases like text editor)
    const interval = setInterval(checkForChanges, 1000);

    return () => {
      clearInterval(interval);
    };
  }, [form, initialFormValues, hasUnsavedChanges]);

  // Handle browser back button and page unload

  const resetChangeDetection = () => {
    const currentValues = form.getFieldsValue();
    setInitialFormValues(currentValues);
    resetUnsavedChanges();
  };

  return {
    hasUnsavedChanges,
    resetChangeDetection,
  };
};
