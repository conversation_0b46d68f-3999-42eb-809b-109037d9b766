'use client';

import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import queryString from 'query-string';

const useSafeSearchParams = <T>() => {
  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();

  // Parse query params with generic type
  const parsedQueryParams = queryString.parse(searchParams.toString()) as T;

  // Set search params with type safety
  const setSearchParams = (params: Partial<T>, options: { replace?: boolean } = {}) => {
    const newQuery = {
      ...parsedQueryParams,
      ...params,
    };

    // Stringify the updated query params
    const queryStringified = queryString.stringify(newQuery, {
      skipNull: true,
      skipEmptyString: true,
    });

    // Update URL using router
    const url = `${pathname}${queryStringified ? `?${queryStringified}` : ''}`;

    if (options.replace) {
      router.replace(url);
    } else {
      router.push(url);
    }
  };

  // Get specific search param by key
  const getSearchParams = <K extends keyof T>(key: K): T[K] | undefined => {
    const value = searchParams.get(key as string);
    return value === null ? undefined : (value as T[K]);
  };

  return { searchParams, parsedQueryParams, getSearchParams, setSearchParams };
};

export default useSafeSearchParams;
