import { LabelValueType } from '@/type';

export const MAX_SIZE_VIDEO_UPLOAD = 2048;

export const MAX_SIZE_IMAGE_UPLOAD = 5;

export const MAX_SIZE_AUDIO_UPLOAD = 5;

export const KILOBYTE = 1024;
export const MEGABYTE = KILOBYTE * 1024;
export const GIGABYTE = MEGABYTE * 1024;

export enum FileType {
  IMAGE = 'Image',
  VIDEO = 'Video',
  AUDIO = 'Audio',
  SLIDE = 'Slide',
}

export const formatFiles = [
  {
    value: '.mp4, .avi, .mov',
    file_type_id: 1,
    expand: '',
    type: FileType.VIDEO,
  },
  {
    value: '.jpeg, .jpg,.png',
    expand: '',
    file_type_id: 2,
    type: FileType.IMAGE,
  },
  {
    value: '.pdf',
    expand: '',
    file_type_id: 3,
    type: FileType.SLIDE,
  },
  {
    value: '.mp3, .wav, .ogg',
    expand: 'audio/ogg, audio/wav, mpeg',
    // expand: 'audio/x-m4a, audio/ogg, audio/vnd.dlna.adts, audio/flac, audio/wav, audio/x-ms-wma, video/mpeg',
    file_type_id: 4,
    type: FileType.AUDIO,
  },
] as { value: string; file_type_id: number; expand?: string; type: FileType }[];

export enum FileActions {
  Add = 'ADD',
  Download = 'DOWNLOAD',
  Delete = 'DELETE',
  AddAudioForSlide = 'ADD_AUDIO_FOR_SLIDE',
  AddShortClip = 'ADD_SHORT_CLIP',
}

export const fileTypes = [
  {
    label: 'Video',
    value: FileType.VIDEO,
  },
  {
    label: 'Hình ảnh',
    value: FileType.IMAGE,
  },
  {
    label: 'Âm thanh',
    value: FileType.AUDIO,
  },
] satisfies LabelValueType<FileType>[];
