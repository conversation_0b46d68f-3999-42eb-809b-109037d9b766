export const MINUTE = 60;
export const HOUR = 60 * MINUTE;

export const DAY = 24 * HOUR;
export const WEEK = 7 * DAY;
export const MONTH = 30 * DAY;
export const YEAR = 365 * DAY;

export const transformSecondToHour = (seconds: number) => {
  return Math.floor(seconds / HOUR);
};

export const transformSecondToMinute = (seconds: number) => {
  return Math.floor((seconds % HOUR) / MINUTE);
};

export const transformSecondToSecond = (seconds: number) => {
  return Math.floor(seconds % MINUTE);
};
