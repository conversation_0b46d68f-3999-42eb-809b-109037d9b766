import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

interface CourseStore {
  hasUnsavedChanges: boolean;
  setHasUnsavedChanges: (hasChanges: boolean) => void;
  resetUnsavedChanges: () => void;
}

export const useCourseStore = create<CourseStore>()(
  devtools(
    immer((set) => ({
      hasUnsavedChanges: false,
      setHasUnsavedChanges: (hasChanges) =>
        set((state) => {
          state.hasUnsavedChanges = hasChanges;
        }),
      resetUnsavedChanges: () =>
        set((state) => {
          state.hasUnsavedChanges = false;
        }),
    })),
  ),
);