import { InteractionType } from '@/modules/courses/constants/course.const';
import { UserInfo } from '@/type/common';
import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

export type QuestionType = 'single' | 'multiple';

export interface QuestionOption {
  option_index: number;
  option_name: string;
  option_thumbnail_image_file:
    | {
        id: string;
        fileName: string;
        fileUrl: string;
        fileSize: string;
      }
    | string
    | null;
}

export interface Question {
  questionTypeId: number | null;
  questionName: string | null;
  questionImageFile:
    | {
        id: string;
        fileName: string;
        fileUrl: string;
        fileSize: string;
      }
    | string
    | null;
  videoFile:
    | {
        id: string;
        fileName: string;
        fileUrl: string;
        fileSize: string;
      }
    | string
    | null;
  questionOptions: QuestionOption[] | null;
  questionAnswers: string | null;
  correctAnswer: number[] | null;
  questionRequired: boolean | null;
  questionDuration: number | null;
  replyRightAnswer: string | null;
  replyWrongAnswer: string | null;
  showResponse: boolean | null;
  backgroundColor: string | null;
  createdBy: UserInfo | null;
  updatedBy: UserInfo | null;
}

export interface Interaction {
  question: Question;
  id: string | null;
  startAt: number | null;
  duration: number | null;
}

export interface LessonSettings {
  isOpenInteractionType: InteractionType;
  interactionId: string | null;
  appearanceTime: string;
  questionType: QuestionType;
  answerRequired: boolean;
  skipTime: number;
  showResponse: boolean;
  correctAnswerText: string;
  wrongAnswerText: string;
}

interface LessonStore {
  settings: LessonSettings;
  interaction: Interaction;
  updateSettings: (updates: Partial<LessonSettings>) => void;
  updateInteraction: (updates: Partial<Interaction>) => void;
  updateInteractionStartAt: (startAt: number | null) => void;
  updateInteractionDuration: (duration: number | null) => void;
  updateQuestionName: (questionName: string) => void;
  updateQuestionImage: (fileId: string | null) => void;
  updateQuestionVideo: (fileId: string | null) => void;
  updateQuestionTypeId: (questionTypeId: number) => void;
  updateQuestionOptions: (options: QuestionOption[]) => void;
  updateQuestionOptionText: (optionIndex: number, text: string) => void;
  updateQuestionOptionImage: (optionIndex: number, imageUrl: string) => void;
  addQuestionOption: () => void;
  removeQuestionOption: (optionIndex: number) => void;
  updateCorrectAnswer: (answerIds: number[]) => void;
  reorderQuestionOptions: (startIndex: number, endIndex: number) => void;
  updateQuestionRequired: (required: boolean) => void;
  updateQuestionDuration: (duration: number) => void;
  updateReplyRightAnswer: (reply: string) => void;
  updateReplyWrongAnswer: (reply: string) => void;
  updateShowResponse: (showResponse: boolean) => void;
  resetSettings: () => void;
  resetInteraction: () => void;
  ensureSequentialIndices: () => void;
}

// Ensure all question options have sequential indices
const ensureSequentialQuestionOptionIndices = (options: QuestionOption[]): QuestionOption[] => {
  return options.map((option, index) => {
    return { ...option, option_index: index };
  });
};

const defaultSettings: LessonSettings = {
  isOpenInteractionType: InteractionType.Default,
  interactionId: null,
  appearanceTime: '03:35',
  questionType: 'multiple',
  answerRequired: false,
  skipTime: 10,
  showResponse: true,
  correctAnswerText: '',
  wrongAnswerText: '',
};

export const createDefaultQuestion = (): Question => ({
  questionTypeId: 0,
  questionName: '',
  questionImageFile: null,
  videoFile: null,
  questionOptions: [
    { option_index: 0, option_name: '', option_thumbnail_image_file: '' },
    { option_index: 1, option_name: '', option_thumbnail_image_file: '' },
  ],
  questionAnswers: null,
  correctAnswer: [],
  questionRequired: null,
  questionDuration: null,
  replyRightAnswer: null,
  replyWrongAnswer: null,
  showResponse: true,
  backgroundColor: null,
  createdBy: null,
  updatedBy: null,
});

const createDefaultInteraction = (): Interaction => ({
  question: createDefaultQuestion(),
  id: null,
  startAt: null,
  duration: null,
});

export const useLessonStore = create<LessonStore>()(
  devtools(
    immer((set) => ({
      settings: defaultSettings,
      interaction: createDefaultInteraction(),
      updateSettings: (updates) =>
        set((state) => {
          Object.assign(state.settings, updates);
        }),
      updateInteraction: (updates) =>
        set((state) => {
          Object.assign(state.interaction, updates);
          // Ensure question options have sequential indices after update
          if (updates.question?.questionOptions) {
            state.interaction.question.questionOptions = ensureSequentialQuestionOptionIndices(
              updates.question.questionOptions,
            );
          }
        }),
      updateInteractionStartAt: (startAt) =>
        set((state) => {
          state.interaction.startAt = startAt;
        }),
      updateInteractionDuration: (duration) =>
        set((state) => {
          state.interaction.duration = duration;
        }),
      updateQuestionName: (questionName) =>
        set((state) => {
          state.interaction.question.questionName = questionName;
        }),
      updateQuestionImage: (fileId) =>
        set((state) => {
          state.interaction.question.questionImageFile = fileId;
        }),
      updateQuestionVideo: (fileId) =>
        set((state) => {
          state.interaction.question.videoFile = fileId;
        }),
      updateQuestionTypeId: (questionTypeId) =>
        set((state) => {
          state.interaction.question.questionTypeId = questionTypeId;
        }),
      updateQuestionOptions: (options) =>
        set((state) => {
          state.interaction.question.questionOptions = options;
        }),
      updateQuestionOptionText: (optionIndex, text) =>
        set((state) => {
          const options = state.interaction.question.questionOptions;
          if (options) {
            const option = options.find((o) => o.option_index === optionIndex);
            if (option) {
              option.option_name = text;
            }
          }
        }),
      updateQuestionOptionImage: (optionIndex, imageUrl) =>
        set((state) => {
          const options = state.interaction.question.questionOptions;
          if (options) {
            const option = options.find((o) => o.option_index === optionIndex);
            if (option) {
              option.option_thumbnail_image_file = imageUrl;
            }
          }
        }),
      addQuestionOption: () =>
        set((state) => {
          const options = state.interaction.question.questionOptions || [];
          const newIndex = options.length;
          state.interaction.question.questionOptions = [
            ...options,
            { option_index: newIndex, option_name: '', option_thumbnail_image_file: '' },
          ];
        }),
      removeQuestionOption: (optionIndex) =>
        set((state) => {
          const options = state.interaction.question.questionOptions;
          if (!options || options.length <= 2) return;

          // Find the option being removed
          const removedOption = options.find((o) => o.option_index === optionIndex);
          if (!removedOption) return;

          // Remove the option
          state.interaction.question.questionOptions = options.filter((o) => o.option_index !== optionIndex);

          // Update correctAnswer indices - remove the deleted index and adjust higher indices
          if (state.interaction.question.correctAnswer) {
            state.interaction.question.correctAnswer = state.interaction.question.correctAnswer
              .filter((index) => index !== optionIndex) // Remove the deleted option's index
              .map((index) => (index > optionIndex ? index - 1 : index)); // Adjust higher indices
          }

          // Reassign indices to maintain sequential order
          if (state.interaction.question.questionOptions) {
            state.interaction.question.questionOptions = state.interaction.question.questionOptions.map(
              (option, index) => ({ ...option, option_index: index }),
            );
          }
        }),
      updateCorrectAnswer: (answerIds) =>
        set((state) => {
          state.interaction.question.correctAnswer = answerIds;
        }),
      reorderQuestionOptions: (startIndex, endIndex) =>
        set((state) => {
          const options = state.interaction.question.questionOptions;
          if (options) {
            const newOptions = [...options];
            const [removed] = newOptions.splice(startIndex, 1);
            newOptions.splice(endIndex, 0, removed);
            state.interaction.question.questionOptions = newOptions;

            // Update correctAnswer indices to match the new order
            if (state.interaction.question.correctAnswer) {
              const updatedCorrectAnswer = state.interaction.question.correctAnswer.map((index) => {
                if (index === startIndex) {
                  // The moved option goes to endIndex
                  return endIndex;
                } else if (startIndex < endIndex) {
                  // Moving down: items between startIndex+1 and endIndex move up
                  if (index > startIndex && index <= endIndex) {
                    return index - 1;
                  }
                } else if (startIndex > endIndex) {
                  // Moving up: items between endIndex and startIndex-1 move down
                  if (index >= endIndex && index < startIndex) {
                    return index + 1;
                  }
                }
                return index;
              });
              state.interaction.question.correctAnswer = updatedCorrectAnswer;
            }

            // Reassign indices to maintain sequential order after reordering
            state.interaction.question.questionOptions = state.interaction.question.questionOptions.map(
              (option, index) => ({ ...option, option_index: index }),
            );
          }
        }),
      updateQuestionRequired: (required) =>
        set((state) => {
          state.interaction.question.questionRequired = required;
        }),
      updateQuestionDuration: (duration) =>
        set((state) => {
          state.interaction.question.questionDuration = duration;
        }),
      updateReplyRightAnswer: (reply) =>
        set((state) => {
          state.interaction.question.replyRightAnswer = reply;
        }),
      updateReplyWrongAnswer: (reply) =>
        set((state) => {
          state.interaction.question.replyWrongAnswer = reply;
        }),
      updateShowResponse: (showResponse) =>
        set((state) => {
          state.interaction.question.showResponse = showResponse;
        }),
      resetSettings: () =>
        set((state) => {
          state.settings = defaultSettings;
        }),
      resetInteraction: () =>
        set((state) => {
          state.interaction = createDefaultInteraction();
        }),
      ensureSequentialIndices: () =>
        set((state) => {
          if (state.interaction.question.questionOptions) {
            state.interaction.question.questionOptions = ensureSequentialQuestionOptionIndices(
              state.interaction.question.questionOptions,
            );
          }
        }),
    })),
  ),
);
