const BASE_API_URL = process.env.NEXT_PUBLIC_BASE_API_URL;

export const PROFILE_DASHBOARD_PATH = `${BASE_API_URL}/profile`;

export const apiUrls = {
  bugReport: `${BASE_API_URL}/bug-reports`,
  author: `${BASE_API_URL}/authors`,
  package: `${BASE_API_URL}/packages`,
  paymentType: `${BASE_API_URL}/payment-types`,
  //start course
  masterDataCourse: `${BASE_API_URL}/course/master-data`,
  searchCourses: `${BASE_API_URL}/course`,
  createCourse: `${BASE_API_URL}/course`,
  draftCourse: `${BASE_API_URL}/course/draft`,
  createTag: `${BASE_API_URL}/tag`,
  getCourseById: `${BASE_API_URL}/course`,
  courseByCurrentUser: `${BASE_API_URL}/course/current-user`,
  deleteCourse: `${BASE_API_URL}/course`,
  uploadVideoForShortCourse: `${BASE_API_URL}/course/upload-video`,

  publishLecture: `${BASE_API_URL}/lecture`,
  publishSection: `${BASE_API_URL}/section`,

  createChapter: `${BASE_API_URL}/section`,
  updateChapter: `${BASE_API_URL}/section`,
  deleteChapter: `${BASE_API_URL}/section`,
  swapChapter: `${BASE_API_URL}/section/change-position`,

  createLecture: `${BASE_API_URL}/lecture`,
  getLectureById: `${BASE_API_URL}/lecture`,
  updateLecture: `${BASE_API_URL}/lecture`,
  deleteLecture: `${BASE_API_URL}/lecture`,
  uploadLectureVideo: `${BASE_API_URL}/lecture`,
  swapLecture: `${BASE_API_URL}/lecture/change-position`,

  getFiles: `${BASE_API_URL}/file`,
  fileUpload: `${BASE_API_URL}/file`,
  updateFileName: `${BASE_API_URL}/file`,

  updateLectureInteraction: `${BASE_API_URL}/lecture`,

  createLectureSegment: `${BASE_API_URL}/lecture-segment`,
  updateLectureSegment: `${BASE_API_URL}/lecture-segment`,
  deleteLectureSegment: `${BASE_API_URL}/lecture-segment`,
  swapLectureSegment: `${BASE_API_URL}/lecture-segment/change-position`,

  updateLectureDocuments: `${BASE_API_URL}/lecture-document`,
  deleteLectureDocument: `${BASE_API_URL}/lecture-document`,

  createSlide: `${BASE_API_URL}/slide`,
  updateSlide: `${BASE_API_URL}/slide`,
  getSlideItem: `${BASE_API_URL}/slide/slide-item`,
  createSlideItem: `${BASE_API_URL}/slide/slide-item`,
  updateSlideItem: `${BASE_API_URL}/slide/slide-item`,
  deleteSlideItem: `${BASE_API_URL}/slide/slide-item`,
  swapSlideItem: `${BASE_API_URL}/slide/slide-item/change-position`,
  updateSlideSound: `${BASE_API_URL}/slide/slide-sound`,

  createTest: `${BASE_API_URL}/test`,
  updateTest: `${BASE_API_URL}/test`,

  createTarget: `${BASE_API_URL}/lecture/learning-goals`,
  updateTarget: `${BASE_API_URL}/lecture/learning-goals`,
  deleteStudyResultAndTarget: `${BASE_API_URL}/lecture/learning-goals`,

  releaseCourse: `${BASE_API_URL}/course`,
  //end course
  publishedCourses: `${BASE_API_URL}/course/all-publish`,
  // start homepage
  userFavourite: `${BASE_API_URL}/user-favorite`,
  prominentAuthors: <AUTHORS>
  comment: `${BASE_API_URL}/comment`,
  discussion: `${BASE_API_URL}/discussion`,
  getLastViewCourse: `${BASE_API_URL}/user/last-view-course`,
  managementProfile: `${BASE_API_URL}/profile`,
  getUserInfo: `${BASE_API_URL}/user`,

  // end homepage

  // payment
  packagePrices: `${BASE_API_URL}/payment/package-prices`,
  makeTransactionPayment: `${BASE_API_URL}/payment`,
  getPaymentStatus: `${BASE_API_URL}/payment`,
  // end payment

  // start learner

  userCourse: `${BASE_API_URL}/user-course`,
  userAnswer: `${BASE_API_URL}/user-answer`,
  hypernote: `${BASE_API_URL}/hyper-note`,
  hypernoteLeaner: `${BASE_API_URL}/hyper-note/get-paginate-course`,
  completedLecture: `${BASE_API_URL}/user-course/completed-lecture`,
  courseProgress: `${BASE_API_URL}/courses/progress`,
  getUserProgress: `${BASE_API_URL}/courses/progress`,
  // end learner

  resendEmail: `${BASE_API_URL}/resend-email`,

  googleLoginUrlCallback: `${BASE_API_URL}/auth/google/url/callback`,
  topic: `${BASE_API_URL}/topics`,
  onboardingCreator: `${BASE_API_URL}/onboarding/creators`,
  onboardingMasterData: `${BASE_API_URL}/onboarding/master-data`,
  onboardingLearner: `${BASE_API_URL}/onboarding/learner`,
  paymentMethod: `${BASE_API_URL}/payment-methods`,

  // start province
  getProvinces: `${BASE_API_URL}/address/provinces`,
  getDistricts: `${BASE_API_URL}/address/districts`,
  getWards: `${BASE_API_URL}/address/wards`,
  // end province

  //guest
  guest: {
    courses: `${BASE_API_URL}/guest/courses`,
  },
  profile: {
    creators: {
      path: `${PROFILE_DASHBOARD_PATH}/creators`,
      children: {
        courses: {
          path: `${PROFILE_DASHBOARD_PATH}/creators/courses`,
          detail: `${PROFILE_DASHBOARD_PATH}/creators/courses/:courseId`,
          info: `${PROFILE_DASHBOARD_PATH}/creators/courses/:courseId/infos`,
          section: `${PROFILE_DASHBOARD_PATH}/creators/courses/:courseId/sections`,
        },
      },
    },
  },
  userInfo: `${BASE_API_URL}/users`,
  learningGoal: `${BASE_API_URL}/learning-goal`,
};

export const COURSE_PATH = '/courses';
export const courseRoutePath = {
  list: COURSE_PATH,
  onboarding: `${COURSE_PATH}/onboarding`,
  detail: `${COURSE_PATH}/:id`,
  shortCourse: `${COURSE_PATH}/:id/short`,
};

export const routePaths = {
  homePage: '/',
  payment: {
    path: '/payment',
  },
  welcome: {
    path: '/welcome',
  },
  createCourse: '/courses/create',
  login: '/login',
  register: '/register',
  creator: {
    path: '/creators',
    children: {
      onboarding: {
        path: '/creators/onboarding',
      },
    },
  },
  course: {
    editInfo: '/courses/edit/:courseId/info',
    design: '/courses/design/:courseId/',
    editSections: '/courses/edit/:courseId/sections',
    index: '/courses',
    create: '/courses/create',
    publish: '/courses/publish/:courseId',
    preview: '/courses/preview/:courseId',
    expiredEdit: '/courses/edit/expired',
  },
  learner: {
    path: '/learner/:courseId',
    children: {
      onboarding: { path: '/learner/onboarding' },
      video: {
        path: '/learner/video',
      },
      preview: {
        path: '/learner/:courseId/preview',
      },
      review: {
        path: '/learner/:courseId/review',
      },
      slide: {
        path: '/learner/slide',
      },
      learningGoal: {
        path: '/learner/:courseId/learning-goals',
      },
      learningOutcome: {
        path: '/learner/:courseId/learning-outcomes',
      },
      test: {
        path: '/learner/:courseId/tests',
      },
      lecture: {
        path: '/learner/:courseId/sections/:sectionId/lectures/:lectureId',
      },
      finalTest: {
        children: {
          review: {
            path: '/learner/:courseId/sections/:sectionId/tests/:testId/review',
          },
        },
        path: '/learner/:courseId/sections/:sectionId/tests/:testId',
      },

      path: '/learner/video',
    },
  },
  forgetPassword: '/forgot-password',
  courseRoutePath,
  confirmEmail: 'confirm',
  googleLoginCallback: '/auth/google',
  aboutUs: '/about-us',
  policy: '/policy',
  term: '/term-condition',
  profile: {
    path: '/profile',
    children: {
      creator: {
        path: '/profile/creators',
      },
      course: {
        path: '/profile/courses',
        children: {
          discovery: {
            path: '/profile/courses/discovery',
          },
          short: {
            path: '/profile/courses/:id/short',
          },
          metrics: {
            path: '/profile/courses/:id/metrics',
          },
          myCourse: {
            path: '/profile/courses/me',
          },
          wishlist: {
            path: '/profile/courses/wishlist',
          },
          detail: {
            path: '/profile/courses/:id',
          },
        },
      },
      hyperNote: {
        path: '/profile/hyper-notes',
        children: {
          path: '/profile/hyper-notes/:id',
        },
      },
      discuss: {
        path: '/profile/discuss',
      },
      setting: {
        path: '/profile/setting',
      },
      learningMap: {
        path: '/profile/learning-maps',
      },
    },
  },
};

export const cookieConstant = {
  userInfoKey: 'user_info',
  lastVisited: 'last_visited',
  onboarding: 'onboarding',
  callbackAfterLogin: 'callback',
};

export const notEmpty = 'không được để trống';
export const notEmptyV2 = 'Không được để trống';

export enum DesignType {
  AVAILABLE = 'AVAILABLE',
  DESIGN = 'DESIGN',
}

export enum ActionSectionValue {
  EditSectionContent = 1,
  EditSectionName,
  DeleteSection,
}

export const actionsChapter = [
  {
    label: 'Sửa nội dung chương',
    value: ActionSectionValue.EditSectionContent,
  },
  {
    label: 'Đổi tên chương',
    value: ActionSectionValue.EditSectionName,
  },
  {
    label: 'Xóa chương',
    value: ActionSectionValue.DeleteSection,
  },
];

export const actionsLecture = [
  {
    label: 'Đổi tên bài học',
    value: 1,
  },
  {
    label: 'Đổi ảnh thumbnail',
    value: 2,
  },
];

export const courseTypeMasterData = [
  {
    title: 'Nội dung ngắn',
    description: 'Là nội dung dạng video (thời lượng tối đa 5 phút) phục vụ mục tiêu học tập nhanh chóng và xúc tích.',
    id: 2,
  },
  {
    title: 'Nội dung tiêu chuẩn',
    description: 'Là nội dung được thiết kế đầy đủ cấu trúc chương và bài học kèm theo các tính năng tương tác.',
    id: 1,
  },
];

export enum SectionType {
  Default = 1,
  Test = 3,
  Target = 4,
  Result = 5,
}

export enum LectureType {
  Video = 1,
  Slide = 2,
  Test = 3,
  Target = 4,
  Result = 5,
}

export enum FileType {
  Video = 1,
  Image = 2,
  Slide = 3,
  Audio = 4,
}
