import { API_ENDPOINTS } from '@/constants/api';
import { UploadedFile } from '@/features/courses';

export type S3CompletedPart = { ETag: string; PartNumber: number };

export type InitiateUploadRequest = {
  file_name: string;
  file_size: number;
};

export type InitiateUploadParams = {
  fileName: string;
  fileSize: number;
};

export type InitiateUploadResponse = {
  upload_id: string;
  key: string;
  presigned_urls: string[];
  part_size: number;
};

export type UploadPartParams = {
  chunk: Blob;
  partNumber: number;
  uploadId: string;
  key: string;
  presignedUrl: string;
  retries?: number;
};

export type CompleteUploadParams = {
  key: string;
  uploadId: string;
  parts: S3CompletedPart[];
  fileSize: number;
  fileName: string;
  fileDuration?: number;
};

export type CompleteUploadResponse = {} & UploadedFile;

export type CompleteUploadRequest = {
  key: string;
  upload_id: string;
  parts: S3CompletedPart[];
  file_size: number;
  file_name: string;
  duration?: number;
};

export type AbortUploadRequest = {
  key: string;
  upload_id: string;
};

export type InitiateUploadData = { uploadId: string; key: string; presignedUrls: string[]; partSize: number };

export class S3MultipartUploader {
  private file: File;
  private maxRetries: number;
  private batchSize: number;
  private abortController: AbortController;

  private apiUrlComplete: string = API_ENDPOINTS.USERS.POST.UPLOAD_FILE_IN_MULTIPART_COMPLETE;
  private apiUrlInitiate: string = API_ENDPOINTS.USERS.POST.UPLOAD_FILE_IN_MULTIPART;

  constructor(file: File, maxRetries: number = 3, batchSize: number = 3) {
    this.file = file;
    this.maxRetries = maxRetries;
    this.batchSize = batchSize;
    this.abortController = new AbortController();
  }

  private async fetchWithErrorHandling<T>(url: string, options: RequestInit): Promise<T> {
    const response = await fetch(url, {
      ...options,
      credentials: 'include',
      signal: this.abortController.signal,
    });
    if (!response.ok) {
      throw new Error(`HTTP error ${response.status}: ${await response.text()}`);
    }
    return response.json();
  }

  abortUpload() {
    this.abortController.abort();
  }

  async initiateUpload(params: InitiateUploadParams): Promise<InitiateUploadData> {
    const { fileName, fileSize } = params;

    const payload = { file_name: fileName, file_size: fileSize } satisfies InitiateUploadRequest;

    const response = await this.fetchWithErrorHandling<InitiateUploadResponse>(this.apiUrlInitiate, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(payload),
    });

    const { upload_id, key, presigned_urls, part_size } = response;

    const responseData = { uploadId: upload_id, key, presignedUrls: presigned_urls, partSize: part_size };
    return responseData;
  }

  async uploadPart(params: UploadPartParams): Promise<{ ETag: string; PartNumber: number }> {
    const { chunk, partNumber, retries = 0, presignedUrl } = params;

    try {
      const response = await fetch(presignedUrl, {
        method: 'PUT',
        headers: { 'Content-Type': this.file.type },
        body: chunk,
        signal: this.abortController.signal,
      });

      if (!response.ok) {
        throw new Error(`Failed to upload chunk ${partNumber}`);
      }

      const eTag = response.headers.get('ETag');
      if (!eTag) {
        throw new Error('ETag not found in response');
      }

      return { ETag: eTag, PartNumber: partNumber };
    } catch {
      if (this.abortController.signal.aborted) {
        const abortedError = { cause: { aborted: true } } satisfies ErrorOptions;
        throw new Error('Upload aborted by user', abortedError);
      }

      if (retries < this.maxRetries) {
        return this.uploadPart({ ...params, retries: retries + 1 });
      }
      throw new Error(`Failed to upload chunk ${partNumber} after ${this.maxRetries} retries`);
    }
  }

  async completeUpload(params: CompleteUploadParams): Promise<CompleteUploadResponse> {
    const { key, uploadId, parts, fileSize, fileName, fileDuration } = params;

    const payload = {
      file_name: fileName,
      key,
      upload_id: uploadId,
      parts,
      file_size: fileSize,
      duration: fileDuration,
    } satisfies CompleteUploadRequest;

    return await this.fetchWithErrorHandling(this.apiUrlComplete, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(payload),
    });
  }

  async uploadFile(params?: {
    onProgress?: (progress: number) => void;
    fileDuration?: number;
  }): Promise<CompleteUploadResponse> {
    const { fileDuration, onProgress } = params ?? {};
    try {
      // initiate multipart upload
      const {
        uploadId,
        key,
        presignedUrls,
        partSize: chunkSize,
      } = await this.initiateUpload({
        fileName: this.file.name,
        fileSize: this.file.size,
      });

      // Split file into chunks
      const totalChunks = Math.ceil(this.file.size / chunkSize);
      const chunks: Blob[] = [];

      for (let i = 0; i < totalChunks; i++) {
        const start = i * chunkSize;
        const end = Math.min(start + chunkSize, this.file.size);
        chunks.push(this.file.slice(start, end));
      }

      // Upload in batch
      const parts: S3CompletedPart[] = [];
      for (let i = 0; i < totalChunks; i += this.batchSize) {
        const batch = chunks.slice(i, i + this.batchSize);
        const batchPromises = batch.map((chunk, index) => {
          const presignedUrl = presignedUrls[i + index];
          const partNumber = i + index + 1;

          return this.uploadPart({ chunk, partNumber, uploadId, key, presignedUrl });
        });

        const batchResults = await Promise.all(batchPromises);
        parts.push(...batchResults);

        onProgress?.(Math.round(((i + batch.length) / totalChunks) * 100));
      }

      const sortedParts = parts.sort((a, b) => a.PartNumber - b.PartNumber);
      const completedRes = await this.completeUpload({
        key,
        uploadId,
        parts: sortedParts,
        fileSize: this.file.size,
        fileName: this.file.name,
        fileDuration,
      });

      return completedRes;
    } catch (err: unknown) {
      if ((err as Error).message !== 'Upload aborted by user') {
        console.error('Upload failed:', err as Error);
      }
      throw err;
    }
  }
}
