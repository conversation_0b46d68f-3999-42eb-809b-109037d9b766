export const REGEX_RULES = {
  WHITE_SPACE: /\s/,
  PASSWORD: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
};

export const VALIDATION_MESSAGES = {
  PASSWORD: {
    MESSAGE: 'Mật khẩu phải có ít nhất 8 ký tự, bao gồm chữ hoa, chữ thường và số.',
  },
};

export const ERROR_MESSAGES = {
  NAME: {
    REQUIRED: 'Vui lòng nhập họ và tên',
    MIN: 'Họ và tên tối thiểu 5 ký tự',
  },
  PASSWORD: {
    REQUIRED: 'Vui lòng nhập mật khẩu',
    GENERAL: 'Mật khẩu phải bao gồm chữ hoa, số và ký tự đặc biệt',
    WHITE_SPACE: 'M<PERSON>t khẩu không được chứa khoảng trắng',
    MIN: 'M<PERSON>t khẩu tối thiểu 8 ký tự',
  },
};
