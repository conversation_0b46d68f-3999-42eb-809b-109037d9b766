import { clearCookies, getCookiesAsString } from '@/actions/cookieAction';
import { redirectPath } from '@/actions/redirectPath';
import { HttpStatusCode, NEXT_API_ENDPOINTS } from '@/constants/api';
import { routePaths } from 'config/constant';

interface ApiResponse<T> {
  success: boolean;
  data: T | null;
  status: number;
  message: string;
}

type FetchConfig = RequestInit & {
  // maxRetries?: number;
  // retryDelay?: (attempt: number) => number;
};

const handleExpiredToken = async () => {
  const isClient = typeof window !== 'undefined';
  if (isClient) {
    await clearCookies();
    const redirectUrl = window.location.pathname + window.location.search;
    location.href = routePaths.login + `?redirectUrl=${encodeURIComponent(redirectUrl)}`;
  } else {
    await redirectPath(NEXT_API_ENDPOINTS.AUTH.LOGOUT);
  }
};

async function parseResponse<T>(res: Response): Promise<ApiResponse<T>> {
  const contentType = res.headers.get('content-type');
  if (contentType?.includes('application/json')) {
    const data = await res.json();
    return { success: res.ok, data, status: res.status, message: res.statusText };
  }

  return { success: res.ok, data: null, status: res.status, message: res.statusText };
}

const createHeaders = async (config: FetchConfig) => {
  const isClient = typeof window !== 'undefined';
  const isFormData = config.body instanceof FormData;

  const headersDefault: Record<string, string> = isFormData ? {} : { 'Content-Type': 'application/json' };

  if (isClient) {
    return { ...headersDefault, ...config.headers };
  }

  const cookie = await getCookiesAsString();
  return { ...headersDefault, ...config.headers, ...(cookie && { Cookie: cookie }) };
};

export async function fetcher<T>(url: string, config: FetchConfig = {}): Promise<ApiResponse<T>> {
  const headers = await createHeaders(config);

  const options = { ...config, headers, credentials: 'include' } satisfies RequestInit;

  try {
    const res = await fetch(url, options);

    if (res.ok) {
      const parsedData = await parseResponse<T>(res);
      return parsedData;
    }

    throw res;
  } catch (error) {
    const res = error as Response;
    console.error('fetcher error: ', { url: res.url, status: res.status, statusText: res.statusText });

    if (res.status === HttpStatusCode.UNAUTHORIZED) {
      await handleExpiredToken();
    }

    return Promise.reject({
      success: false,
      data: null,
      status: res.status,
      message: res.statusText || 'An unexpected error occurred',
    });
  }
}
