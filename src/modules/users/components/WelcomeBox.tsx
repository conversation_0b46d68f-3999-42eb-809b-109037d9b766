'use client';

import { Typography } from '@/components/ui';
import { useUserInfoProvider } from '@/utils/providers/UserInfoProvider';
import { Avatar } from 'antd';
import Image from 'next/image';

function WelcomeBox() {
  const { userInfo } = useUserInfoProvider();

  const userName = userInfo?.info.name;
  const avatar = userInfo?.info.avatar || '/images/avatar.png';

  return (
    <div className="flex gap-4">
      <Avatar
        size={48}
        src={
          <Image src={avatar} alt={`${userName || 'User'}'s avatar`} width={48} height={48} className="object-cover" />
        }
      />

      <div className="flex flex-col">
        <Typography variant="bodyLg" className="text-secondary_text">
          Mừng bạn quay trở lại.
        </Typography>
        <Typography variant="headlineXs">{userName}</Typography>
      </div>
    </div>
  );
}

export default WelcomeBox;
