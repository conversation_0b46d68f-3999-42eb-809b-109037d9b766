import { API_ENDPOINTS, HttpMethod } from '@/constants/api';
import { fetcher } from '@/lib/fetcher';
import { handleAsync } from '@/lib/handle-async';
import { CourseStatusEnum } from '@/modules/courses/constants/course.const';
import { CourseInfo } from '@/modules/courses/types/course.type';
import { SearchParamsProps } from '@/type/appProps';

import { formatApiUrl } from '@/utils/url.util';
import queryString from 'query-string';

class CourseDraftService {
  static async createCourseDraftService(payload: { courseId: string }) {
    const url = formatApiUrl(API_ENDPOINTS.COURSES.POST.COURSE_DRAFT, { courseId: payload.courseId });
    const response = await fetcher<{ id: string }>(url, { method: HttpMethod.POST });
    return response.data;
  }

  static async confirmCourseDraftService(payload: { courseId: string; state: 'CONFIRMED' | 'REJECTED' }) {
    const url = queryString.stringifyUrl({
      url: formatApiUrl(API_ENDPOINTS.COURSES.PATCH.CONFIRM_COURSE_DRAFT, { courseId: payload.courseId }),
      query: { state: payload.state },
    });
    const response = await fetcher<{ id: string }>(url, { method: HttpMethod.PATCH });
    return response.data;
  }

  static getConfirmationState(searchParams: SearchParamsProps) {
    const { edit_more, proceed } = searchParams;
    const isEditMore = edit_more === 'true';
    const isProceed = proceed === 'true';
    if (isEditMore) return 'REJECTED';
    if (isProceed) return 'CONFIRMED';
    return null;
  }

  static isEditMore(edit_more: string): boolean {
    return edit_more === 'true';
  }

  static isProceed(proceed: string): boolean {
    return proceed === 'true';
  }

  static getConfirmationFlags({ proceed, edit_more }: { proceed: string; edit_more: string }) {
    const isProceed = this.isProceed(proceed);
    const isEditMore = this.isEditMore(edit_more);
    const shouldConfirmDraft = isProceed || isEditMore;

    return { isProceed, isEditMore, shouldConfirmDraft };
  }

  static async getCourseDraft({ courseId, course }: { courseId: string; course: CourseInfo | null }) {
    const isPublished = Boolean(course?.publish);
    const isApproved = Boolean(course?.active);
    const courseApproved = course?.status === CourseStatusEnum.APPROVED;

    const shouldCreateDraft = isPublished && isApproved && courseApproved;
    if (!shouldCreateDraft) return null;

    const [error, courseDraft] = await handleAsync(this.createCourseDraftService({ courseId }));

    if (error) return null;

    if (courseDraft) return courseDraft;
  }

  static async handleConfirmDraftChanges({
    courseId,
    confirmState,
  }: {
    courseId: string;
    confirmState: 'CONFIRMED' | 'REJECTED' | null;
  }) {
    if (!confirmState) return null;

    const response = await this.confirmCourseDraftService({ courseId, state: confirmState });
    return response;
  }
}

export default CourseDraftService;
