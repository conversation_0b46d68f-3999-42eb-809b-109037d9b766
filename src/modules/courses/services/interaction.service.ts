import { API_ENDPOINTS, HttpMethod } from '@/constants/api';
import { fetcher } from '@/lib/fetcher';
import { formatApiUrl } from '@/utils/url.util';
import {
  CreateInteractionRequest,
  DeleteInteractionRequest,
  GetInteractionDetailRequest,
  UpdateInteractionContentRequest,
  UpdateInteractionRequest,
} from '../types/course-request.type';

const createInteractionService = async (payload: CreateInteractionRequest) => {
  const { courseId, sectionId, lectureId, interactionType, startAt, duration } = payload;
  const body = {
    interact_type: interactionType,
    name: '', // remove this field after BE update
    start_at: startAt,
    duration: duration,
  };

  const url = formatApiUrl(API_ENDPOINTS.COURSES.POST.INTERACTION, { courseId, sectionId, lectureId });
  const res = await fetcher<{ id: string }>(url, { method: HttpMethod.POST, body: JSON.stringify(body) });
  return res.data;
};

const updateInteractionService = async (payload: UpdateInteractionRequest) => {
  const { courseId, sectionId, lectureId, interactId, startAt, duration } = payload;
  const body = {
    name: '', // remove this field after BE update
    start_at: startAt,
    duration: duration,
  };

  const url = formatApiUrl(API_ENDPOINTS.COURSES.PUT.INTERACTION_DETAIL, {
    courseId,
    sectionId,
    lectureId,
    interactId,
  });

  const res = await fetcher<{ id: string }>(url, { method: HttpMethod.PUT, body: JSON.stringify(body) });
  return res.data;
};

const updateInteractionContentService = async (payload: UpdateInteractionContentRequest) => {
  const { courseId, sectionId, lectureId, interactId, content } = payload;

  const url = formatApiUrl(API_ENDPOINTS.COURSES.PUT.INTERACTION, { courseId, sectionId, lectureId, interactId });

  const res = await fetcher<{ id: string }>(url, { method: HttpMethod.PUT, body: JSON.stringify({ content }) });
  return res.data;
};

const deleteInteractionService = async (payload: DeleteInteractionRequest) => {
  const { courseId, sectionId, lectureId, interactId } = payload;
  const url = formatApiUrl(API_ENDPOINTS.COURSES.DELETE.INTERACTION, { courseId, sectionId, lectureId, interactId });

  const res = await fetcher<{ id: string }>(url, { method: HttpMethod.DELETE });
  return res.data;
};

const getInteractionDetailService = async (payload: GetInteractionDetailRequest) => {
  const { courseId, sectionId, lectureId, interactId } = payload;
  const url = formatApiUrl(API_ENDPOINTS.COURSES.PUT.INTERACTION, { courseId, sectionId, lectureId, interactId });

  const res = await fetcher(url, { method: HttpMethod.GET });
  return res.data;
};

export {
  createInteractionService,
  deleteInteractionService,
  getInteractionDetailService,
  updateInteractionContentService,
  updateInteractionService,
};
