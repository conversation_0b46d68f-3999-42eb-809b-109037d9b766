import { API_ENDPOINTS } from '@/constants/api';
import { fetcher } from '@/lib/fetcher';
import { CourseListBase } from '@/modules/courses/services/course.service';
import { UserInfo } from '@/type';
import { formatApiUrl } from '@/utils/url.util';
import queryString from 'query-string';

export const updateFavoriteCreatorService = async (request: { authorId: string; isFavorite: boolean }) => {
  const url = formatApiUrl(API_ENDPOINTS.COURSES.POST.FAVORITE_CREATOR, { authorId: request.authorId });

  const query = queryString.stringifyUrl({ url });

  const favorite = request.isFavorite;

  const { data } = await fetcher<CourseListBase<UserInfo>>(query, { body: JSON.stringify({ favorite }) });
  return data;
};
