import { API_ENDPOINTS, HttpMethod } from '@/constants/api';
import { fetcher } from '@/lib/fetcher';
import { mapQuizResponse } from '@/modules/courses/features/create-course/design-step/utils/test.util';
import { Test, UserTestResult } from '@/modules/courses/types/test.type';
import { formatApiUrl } from '@/utils/url.util';
import queryString from 'query-string';

export type QuestionPayloadRequest = {
  question_name: string;
  question_image_file?: string;
  video_file?: string;
  question_options: Array<{
    option_index: number;
    option_name: string;
    option_thumbnail_image_file?: string;
  }>;
  correct_answer: number[];
  sort_index: number;
};

export type TestPayloadRequest =
  | {
      test_type: 'QUIZ';
      content: {
        lecture_id: string;
        test_name: string;
        has_limit_time: number;
        limit_time: number;
        questions: QuestionPayloadRequest[];
      };
    }
  | {
      test_type: 'FINAL_TEST';
      content: {
        test_name: string;
        has_limit_time: number;
        limit_time: number;
        questions: QuestionPayloadRequest[];
        min_correct_answer: number;
      };
    };

export type UpdateTestRequest = {
  courseId: string;
  sectionId: string;
  testId: string;
  payload: TestPayloadRequest;
};

export type FileResponse = {
  id: string;
  fileName: string;
  fileUrl: string;
  fileSize: number;
};

export type OptionResponse = {
  option_index: number;
  option_name: string;
  option_thumbnail_image_file?: FileResponse;
};

export type QuestionResponse = {
  id: string;
  question_name: string;
  question_image: string;
  question_image_file?: FileResponse;
  question_type_id: number;
  video_file?: FileResponse;
  question_options: OptionResponse[];
  correct_answer: number[];
  sort_index: number;
};

export type GetTestResponse = {
  id: string;
  test_name: string;
  min_correct_answer: number;
  has_limit_time: number;
  limit_time: number;
  questions: QuestionResponse[];
  created_at: string;
  updated_at: string;
};

export type TestAnswerRequest = {
  answer: number[];
  question_id: string;
};

export type UserTestAnswerRequest = {
  courseId: string;
  sectionId: string;
  testId: string;
  data: TestAnswerRequest[];
};

export const getTestByIdService = async (payload: {
  courseId: string;
  sectionId: string;
  testId: string;
}): Promise<Test | null> => {
  const url = formatApiUrl(API_ENDPOINTS.COURSES.GET.TEST_DETAIL, {
    courseId: payload.courseId,
    sectionId: payload.sectionId,
    testId: payload.testId,
  });
  const response = await fetcher<GetTestResponse>(url);

  const mappedResponse = response.data ? mapQuizResponse(response.data) : null;
  return mappedResponse;
};

export const updateTestService = async (payload: UpdateTestRequest) => {
  const url = formatApiUrl(API_ENDPOINTS.COURSES.PUT.TEST, {
    courseId: payload.courseId,
    sectionId: payload.sectionId,
    testId: payload.testId,
  });

  const res = await fetcher(url, { method: HttpMethod.PUT, body: JSON.stringify(payload.payload) });
  return res.data;
};

export const submitTestQuestion = async (request: UserTestAnswerRequest) => {
  const { courseId, sectionId, testId, data } = request;

  const url = formatApiUrl(API_ENDPOINTS.COURSES.POST.LEANER_TEST_ANSWER, {
    courseId,
    sectionId,
    testId,
  });

  const payload = JSON.stringify({ data });
  const res = await fetcher(url, { body: payload, method: HttpMethod.POST });
  return res?.data;
};

export const getTestAnswers = async (payload: {
  courseId: string;
  sectionId: string;
  testId: string;
  times?: number;
}) => {
  const { courseId, sectionId, testId, times } = payload;

  const url = queryString.stringifyUrl({
    url: formatApiUrl(API_ENDPOINTS.COURSES.GET.LEANER_TEST_ANSWER, { courseId, sectionId, testId }),
    query: { times },
  });

  const res = await fetcher<UserTestResult[]>(url);
  return res?.data;
};
