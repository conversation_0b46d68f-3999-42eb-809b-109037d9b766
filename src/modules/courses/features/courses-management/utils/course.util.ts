import { Lecture, UserCourse } from '@/modules/courses/types/course.type';

export const getLatestCourse = (userCourses: UserCourse[]) => {
  return userCourses?.[0] ?? null;
};

export const getLatestSection = ({
  latestCourse,
  latestSectionId,
}: {
  latestSectionId: string;
  latestCourse: UserCourse;
}) => {
  if (!latestCourse) return null;

  return latestCourse?.course.sections.find((section) => section.id === latestSectionId);
};

export const getLatestLecture = (lectures: Lecture[], latestCourse: UserCourse) => {
  if (!latestCourse) return null;

  return lectures?.find((lecture) => lecture.id === latestCourse.lastViewLecture.id);
};

export const calculateLearningPercent = (latestCourse: UserCourse) => {
  return Math.floor((latestCourse?.countCompletedLectures / latestCourse?.totalLectures) * 100);
};
