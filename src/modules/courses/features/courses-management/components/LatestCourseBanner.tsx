'use client';

import { Button, Typography } from '@/components/ui';
import { routePaths } from '@/config';
import { palette } from '@/config/theme';
import { StarIcon } from '@/icons';
import { timestampToSeconds } from '@/lib/dateTime';
import { LECTURE_TYPE_MAP } from '@/modules/courses/constants/course.const';
import { LessonType } from '@/modules/courses/types/chapter.type';
import { UserCourse } from '@/modules/courses/types/course.type';
import { formatApiUrl } from '@/utils/url.util';
import { ArrowRightIcon } from '@heroicons/react/24/outline';
import { Progress } from 'antd';
import { useRouter } from 'next/navigation';
import { calculateLearningPercent, getLatestLecture, getLatestSection } from '../utils/course.util';

const LatestCourseBanner = ({ latestCourse }: { latestCourse: UserCourse }) => {
  const router = useRouter();
  const latestSection = getLatestSection({ latestCourse, latestSectionId: latestCourse?.lastViewLecture.sectionId });
  const latestLecture = getLatestLecture(latestSection?.lectures ?? [], latestCourse);
  const learningPercent = calculateLearningPercent(latestCourse);

  const lectureType = latestCourse.lastViewLecture?.lectureType as LessonType;
  const lectureTypeMapped = lectureType ? LECTURE_TYPE_MAP[lectureType] : '';

  const handleContinueLearning = () => {
    const courseId = latestCourse?.course.id?.toString();
    const url = formatApiUrl(routePaths.profile.children.course.children.detail.path, { id: courseId });
    router.push(url);
  };

  return (
    <div
      style={{
        backgroundImage: `linear-gradient(180deg, rgba(9, 2, 44, 0) 0%, #09022B 100%), linear-gradient(270deg, rgba(9, 2, 44, 0) 0%, #09022B 100%), url(${latestCourse?.course?.courseThumbnailImage ?? '/images/background_my_course_example.png'})`,
        backgroundRepeat: 'no-repeat',
        backgroundSize: 'cover',
      }}
      className="min-h-[500px] rounded-xl px-12 py-8"
    >
      <div className="flex w-5/12 flex-col gap-8">
        <div className="flex flex-col gap-2">
          <Typography variant="labelLg" className="uppercase text-white">
            {latestCourse?.course.topic.topicName}
          </Typography>
          <Typography variant="headlineSm" className="uppercase text-white">
            {latestCourse?.course.courseName}
          </Typography>
          <Typography variant="labelLg" className="text-white">
            {latestCourse?.course.createdBy.name}
          </Typography>
        </div>
        <div className="flex flex-col gap-2">
          <Typography variant="labelMd" className="text-secondary-500">
            Bạn đang học đến
          </Typography>
          <Typography variant="headlineXs" className="uppercase text-white">
            {latestSection?.sectionName}
          </Typography>

          <div className="flex flex-col gap-1 rounded-xl bg-base-white-10 p-4">
            <Typography variant="labelLg" className="w-auto uppercase text-white">
              {latestCourse?.lastViewLecture.lectureName}
            </Typography>

            <div className="flex items-center gap-2">
              <Typography variant="labelSm" className="text-ink-200">
                {lectureTypeMapped}
              </Typography>

              <StarIcon />

              <Typography variant="labelSm" className="text-ink-200">
                {timestampToSeconds(latestLecture?.duration ?? 0)} phút
              </Typography>
            </div>
          </div>
        </div>

        <div className="flex flex-col gap-2">
          <Progress
            percent={learningPercent}
            showInfo={false}
            strokeColor={palette.primary[100]}
            trailColor={palette.base['white-25']}
          />
          <Typography variant="labelMd" className="text-white">
            {learningPercent}% hoàn thành
          </Typography>
        </div>

        <div className="w-fit">
          <Button
            size="large"
            className="border border-secondary-500 bg-secondary-500 hover:bg-secondary-400 active:bg-secondary-600"
            endIcon={<ArrowRightIcon className="text-ink-black" />}
            onClick={handleContinueLearning}
          >
            <Typography className="text-ink-black">Tiếp tục khóa học</Typography>
          </Button>
        </div>
      </div>
    </div>
  );
};

export default LatestCourseBanner;
