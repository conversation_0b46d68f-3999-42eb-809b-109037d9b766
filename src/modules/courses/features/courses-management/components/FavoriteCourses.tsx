'use client';

import { Typography } from '@/components/ui';
import CourseItem from '@/modules/courses/components/course-item/CourseItem';
import CourseOverview from '@/modules/courses/components/course-item/CourseOverview';
import CourseRating from '@/modules/courses/components/course-item/CourseRating';
import { useFavoriteCourse } from '@/modules/courses/hooks';
import { CourseInfo } from '@/modules/courses/types/course.type';

function FavoriteCourses({ courses }: { courses: CourseInfo[] }) {
  const { onFavorite } = useFavoriteCourse();

  return (
    <div className="flex flex-col gap-4">
      <Typography variant="headlineSm">Kh<PERSON>a học yêu thích</Typography>

      <div className="grid grid-cols-2 gap-6 sm:grid-cols-3 md:grid-cols-4">
        {courses.map((course) => {
          const topicName = course.topic.topicName;

          return (
            <CourseItem
              onFavorite={(isFavorite) => onFavorite({ courseId: course.id, isFavorite })}
              isFavorite={course.isFavorite}
              creatorName={course.createdBy.name}
              courseData={{
                courseId: course.id,
                courseName: course.courseName,
                courseThumbnail: course.courseThumbnailImage,
              }}
              key={course.id}
              topicName={topicName}
              footer={
                <div className="flex flex-col gap-3">
                  <CourseRating rating={Number(course.avgRating ?? 0)} reviewCount={Number(course.totalRating ?? 0)} />
                  <CourseOverview
                    sectionsCount={Number(course.totalSections) ?? 0}
                    duration={Number(course.duration) ?? 0}
                  />
                </div>
              }
            />
          );
        })}
      </div>
    </div>
  );
}

export default FavoriteCourses;
