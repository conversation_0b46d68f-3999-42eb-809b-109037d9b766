'use client';

import { ChapterSidebarProvider } from '@/context/ChapterSidebarProvider';
import { ChapterType, LessonType } from '@/modules/courses/constants/course.const';
import LearnCourseHeader from '@/modules/courses/features/learn-course/components/LearnCourseHeader';
import ChapterDrawer from '@/modules/courses/features/learn-course/components/SectionsDrawer';
import TestContainer from '@/modules/courses/features/learn-course/components/test/TestContainer';
import VideoContainer from '@/modules/courses/features/learn-course/components/video/VideoContainer';
import { useCourseDetail } from '@/modules/courses/hooks';
import { CourseInfo, Lecture } from '@/modules/courses/types/course.type';
import { Test } from '@/modules/courses/types/test.type';
import { useParams } from 'next/navigation';

const LearnCoursePage = ({
  courseInfo,
  lectureInfo,
  testDetail,
}: {
  courseInfo: CourseInfo;
  lectureInfo?: Lecture;
  testDetail: Test | null;
}) => {
  const { courseDetailData } = useCourseDetail({ courseId: courseInfo.id, initialData: courseInfo });

  const params = useParams<{ courseId: string; sectionId: string; lectureId: string }>();

  const currentSection = courseDetailData?.sections?.find((section) => section.id === params.sectionId);
  const isFinalTest = currentSection?.sectionType === ChapterType.Test;

  const renderCourseLearning = () => {
    if (!courseDetailData) return null;

    if (isFinalTest) {
      return (
        <TestContainer key={testDetail?.id} testType="finalTest" courseInfo={courseDetailData} testInfo={testDetail} />
      );
    }

    if (lectureInfo?.lectureType === LessonType.Video) {
      return <VideoContainer courseInfo={courseDetailData} lectureData={lectureInfo} />;
    }

    if (lectureInfo?.lectureType === LessonType.Test) {
      return <TestContainer key={testDetail?.id} testType="quiz" courseInfo={courseDetailData} testInfo={testDetail} />;
    }

    return null;
  };

  return (
    <div className="h-screen w-full">
      <ChapterSidebarProvider>
        <div className="flex h-screen w-full flex-col">
          <LearnCourseHeader courseInfo={courseInfo} />
          {renderCourseLearning()}
        </div>

        <ChapterDrawer courseInfo={courseInfo} />
      </ChapterSidebarProvider>
    </div>
  );
};

export default LearnCoursePage;
