import { ChapterType } from '@/modules/courses/constants/course.const';
import { Lecture, Section } from '@/modules/courses/types/course.type';

export const getNextLecture = ({
  sections,
  params,
}: {
  sections: Section[];
  params: { sectionId: string; lectureId: string };
}): { section: Section; lecture: Lecture | null } | null => {
  const currentSectionIndex = sections.findIndex((section) => section.id === params.sectionId);
  const currentSection = sections[currentSectionIndex];

  if (currentSection.sectionType === ChapterType.Test)
    return {
      section: currentSection,
      lecture: null,
    };

  if (!currentSection) return null;

  const lectures = currentSection.lectures || [];
  const currentLectureIndex = lectures.findIndex((lecture) => lecture.id === params.lectureId);

  if (currentLectureIndex < lectures.length - 1) {
    return { section: currentSection, lecture: lectures[currentLectureIndex + 1] };
  }

  const nextSection = sections[currentSectionIndex + 1];
  if (nextSection?.lectures?.length > 0) {
    return { section: nextSection, lecture: nextSection.lectures[0] };
  }

  return null;
};
