import { reviewCourse } from '@/features/courses';
import { useNotification } from '@/hooks';
import { BaseError } from '@/type';
import { useMutation } from 'react-query';

const useCourseReview = () => {
  const notification = useNotification();

  const { isLoading, mutate: reviewCourseMutation } = useMutation({
    mutationFn: (variables: { courseId: string; data: { rating: number; comment?: string; feelings?: string[] } }) =>
      reviewCourse(variables),
    onSuccess: () => {
      notification.success({ message: 'Đ<PERSON>h giá khóa học thành công' });
    },
    onError: (error: BaseError['response']['data']) => {
      notification.error({
        message: error.message,
      });
    },
  });

  const handleReviewCourse = ({
    courseId,
    data,
    onSuccess,
  }: {
    courseId: string;
    data: { rating: number; comment?: string; feelings?: string[] };
    onSuccess?: () => void;
  }) => {
    reviewCourseMutation(
      { courseId, data },
      {
        onSuccess: () => {
          onSuccess?.();
        },
      },
    );
  };

  return { isReviewLoading: isLoading, onReviewCourse: handleReviewCourse };
};

export default useCourseReview;
