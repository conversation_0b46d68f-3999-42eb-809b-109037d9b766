import { useMutation } from 'react-query';

import { getTestAnswers, submitTestQuestion, UserTestAnswerRequest } from '@/modules/courses/services/test.service';
import { UserTestResult } from '@/modules/courses/types/test.type';

const useTestMutation = () => {
  const { mutate: submitTestQuestionMutation } = useMutation({
    mutationFn: submitTestQuestion,
  });

  const handleSubmitTestQuestion = (
    payload: UserTestAnswerRequest,
    mutateOptions?: { onSuccess?: () => void; onError?: () => void },
  ) => {
    submitTestQuestionMutation(payload, mutateOptions);
  };

  return {
    onSubmitTestQuestion: handleSubmitTestQuestion,
  };
};

const useTestAnswerMutation = () => {
  const { mutate: getTestAnswersMutation } = useMutation({
    mutationFn: getTestAnswers,
  });

  const handleGetTestAnswers = (
    payload: { courseId: string; sectionId: string; testId: string; times?: number },
    mutateOptions?: { onSuccess?: (data: UserTestResult[] | null) => void; onError?: () => void },
  ) => {
    getTestAnswersMutation(payload, {
      onSuccess: (data) => {
        mutateOptions?.onSuccess?.(data);
      },
      onError: () => {
        mutateOptions?.onError?.();
      },
    });
  };

  return {
    onGetTestAnswers: handleGetTestAnswers,
  };
};

const useTestActions = () => {
  const { onSubmitTestQuestion } = useTestMutation();
  const { onGetTestAnswers } = useTestAnswerMutation();

  return {
    onSubmitTestQuestion,
    onGetTestAnswers,
  };
};

export default useTestActions;
