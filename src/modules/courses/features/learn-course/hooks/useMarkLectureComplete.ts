import { useMutation } from 'react-query';

import { markLectureCompleteService } from '@/modules/courses/services/course.service';

const useMarkLectureComplete = () => {
  const { mutate: markLectureCompleteMutation, isLoading } = useMutation({
    mutationFn: markLectureCompleteService,
  });

  const handleMarkLectureComplete = (
    payload: {
      courseId: string;
      sectionId: string;
      lectureId: string;
      isCompleted: boolean;
    },
    mutateOptions?: {
      onSuccess?: () => void;
      onError?: () => void;
    },
  ) => {
    markLectureCompleteMutation(payload, mutateOptions);
  };

  return {
    isLoading,
    onMarkCompleteLecture: handleMarkLectureComplete,
  };
};

export default useMarkLectureComplete;
