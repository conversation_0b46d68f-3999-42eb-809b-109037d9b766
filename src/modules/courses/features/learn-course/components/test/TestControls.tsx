import { Button, Typography } from '@/components/ui';
import { useChapterDrawer } from '@/hooks/useChapterDrawer';

import { ChaptersIcon } from '@/icons';
import { TestViewMode } from '@/modules/courses/features/learn-course/components/test/test.type';
import TestConfirmSubmitModal from '@/modules/courses/features/learn-course/components/test/TestConfirmSubmitModal';
import TestQuestionListDrawer from '@/modules/courses/features/learn-course/components/test/TestQuestionListDrawer';
import { Question, UserTestResult } from '@/modules/courses/types/test.type';
import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/outline';
import React from 'react';

type TestControlsProps = {
  currentQuestionIndex: number;
  questions: Question[];
  selectedAnswers: { questionIndex: number; answers: number[] }[];
  userAnswers: UserTestResult[] | null;
  viewMode: TestViewMode;
  isMarkCompleteLoading: boolean;
  openConfirmSubmitModal: boolean;

  setOpenConfirmSubmitModal: React.Dispatch<React.SetStateAction<boolean>>;
  onSelectQuestion: (index: number) => void;
  setCurrentQuestionIndex: React.Dispatch<React.SetStateAction<number>>;
  onSubmitTest: () => void;
};

function TestControls(props: TestControlsProps) {
  const {
    currentQuestionIndex,
    questions,
    selectedAnswers,
    userAnswers,
    viewMode,
    isMarkCompleteLoading,
    openConfirmSubmitModal,

    onSelectQuestion,
    setOpenConfirmSubmitModal,
    setCurrentQuestionIndex,
    onSubmitTest,
  } = props;

  const { handleOpenChapterDrawer } = useChapterDrawer();

  const [openQuestionListDrawer, setOpenQuestionListDrawer] = React.useState(false);

  return (
    <React.Fragment>
      <div className="grid grid-cols-3 items-center gap-4 bg-black px-20 py-4">
        <div className="flex justify-center">
          <Typography className="text-white">
            Câu hỏi {currentQuestionIndex + 1}/{questions.length}
          </Typography>
        </div>

        <div className="flex items-center gap-10">
          <Button
            variant="ghost-reversed"
            className="flex items-center gap-1"
            disabled={currentQuestionIndex === 0}
            size="small"
            startIcon={<ChevronLeftIcon />}
            onClick={() => setCurrentQuestionIndex(currentQuestionIndex - 1)}
          >
            Câu hỏi trước
          </Button>

          <Button size="small" variant="ghost" onClick={() => setOpenQuestionListDrawer(true)}>
            <Typography className="">Danh sách câu hỏi</Typography>
          </Button>

          <Button
            variant="ghost-reversed"
            className="flex items-center gap-1"
            disabled={currentQuestionIndex === questions.length - 1}
            size="small"
            endIcon={<ChevronRightIcon />}
            onClick={() => setCurrentQuestionIndex(currentQuestionIndex + 1)}
          >
            Câu hỏi sau
          </Button>
        </div>

        <div className="flex justify-end gap-4">
          <Button size="small" variant="ghost" onClick={() => setOpenConfirmSubmitModal(true)}>
            <Typography>Nộp bài</Typography>
          </Button>

          <Button
            size="small"
            variant="ghost-reversed"
            title="Xem danh sách chương"
            className={'flex cursor-pointer text-white'}
            onClick={handleOpenChapterDrawer}
          >
            <ChaptersIcon />
          </Button>
        </div>
      </div>

      <TestConfirmSubmitModal
        onSubmitTest={onSubmitTest}
        onClose={() => setOpenConfirmSubmitModal(false)}
        isOpen={openConfirmSubmitModal}
        isMarkCompleteLoading={isMarkCompleteLoading}
      />

      <TestQuestionListDrawer
        isOpen={openQuestionListDrawer}
        questions={questions}
        currentQuestionIndex={currentQuestionIndex}
        selectedAnswers={selectedAnswers}
        userAnswers={userAnswers}
        viewMode={viewMode}
        onSelectQuestion={(index: number) => {
          onSelectQuestion(index);
        }}
        onOpen={setOpenQuestionListDrawer}
      />
    </React.Fragment>
  );
}

export default TestControls;
