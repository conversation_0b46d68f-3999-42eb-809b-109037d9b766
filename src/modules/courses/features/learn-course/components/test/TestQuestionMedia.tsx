import { Image as AntImage } from 'antd';

type TestQuestionMediaProps = {
  questionImageFile: { fileUrl: string } | null;
  questionVideoFile: { fileUrl: string } | null;
};

const TestQuestionMedia = (props: TestQuestionMediaProps) => {
  const { questionImageFile, questionVideoFile } = props;

  if (!questionImageFile && !questionVideoFile) return null;

  return (
    <div className="grid h-full grid-cols-2 gap-6">
      {questionImageFile && (
        <AntImage.PreviewGroup items={[questionImageFile.fileUrl]}>
          <AntImage
            src={questionImageFile.fileUrl}
            alt={'question'}
            className={'h-60 w-full rounded-lg border border-neutral-200 object-center'}
          />
        </AntImage.PreviewGroup>
      )}

      {questionVideoFile && (
        <video
          src={questionVideoFile.fileUrl}
          preload="metadata"
          controlsList={'nodownload noplaybackrate nopictureinpicture'}
          controls
          className={'h-60 w-full rounded-lg object-center'}
        />
      )}
    </div>
  );
};

export default TestQuestionMedia;
