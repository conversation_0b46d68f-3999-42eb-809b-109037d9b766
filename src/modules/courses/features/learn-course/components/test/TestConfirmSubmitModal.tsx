import { Button, Modal, Typography } from '@/components/ui';

type TestConfirmSubmitModalProps = {
  isOpen: boolean;
  isMarkCompleteLoading: boolean;

  onSubmitTest: () => void;
  onClose: () => void;
};

function TestConfirmSubmitModal(props: TestConfirmSubmitModalProps) {
  const { isOpen, isMarkCompleteLoading, onSubmitTest, onClose } = props;

  if (!isOpen) return null;

  return (
    <Modal
      open={isOpen}
      onClose={onClose}
      type="confirmation"
      title="Xác nhận nộp bài"
      centered={false}
      footer={
        <div className="flex items-center justify-end gap-3 px-6 py-4">
          <Button variant="tertiary" onClick={onClose}>
            Kiểm tra lại đáp án
          </Button>

          <Button variant="primary" onClick={onSubmitTest} loading={isMarkCompleteLoading}>
            Nộp bài
          </Button>
        </div>
      }
    >
      <div className="p-4">
        <Typography>Bạn có chắc chắn muốn nộp đáp án kiểm tra không?</Typography>
        <br />
        <Typography>
          Thời gian hoàn thành bài kiểm tra vẫn còn, bạn có thể quay lại kiểm tra các đáp án đã chọn bằng cách nhấn vào
          nút “Danh sách câu hỏi”
        </Typography>
      </div>
    </Modal>
  );
}

export default TestConfirmSubmitModal;
