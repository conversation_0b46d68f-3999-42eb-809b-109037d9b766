import { StarIcon } from '@heroicons/react/24/outline';

import { StarIcon as FilledStarIcon } from '@heroicons/react/24/solid';

interface SelectStarProps {
  amountStar: number;
  setAmountStar: (val: number) => void;
}

const TOTAL_STARS = 5;

const SelectViewStar = ({ amountStar, setAmountStar }: SelectStarProps) => {
  const handleStarClick = (starIndex: number) => {
    setAmountStar(starIndex + 1);
  };

  const renderStar = (position: number, index: number) => {
    const StarComponent = position <= amountStar ? FilledStarIcon : StarIcon;

    return (
      <div
        className="center-x flex w-full cursor-pointer items-center gap-2"
        key={position}
        onClick={() => handleStarClick(index)}
      >
        <StarComponent className="size-16 text-yellow-500" />
      </div>
    );
  };

  return (
    <div className="flex gap-2">{Array.from({ length: TOTAL_STARS }, (_, index) => renderStar(index + 1, index))}</div>
  );
};

export default SelectViewStar;
