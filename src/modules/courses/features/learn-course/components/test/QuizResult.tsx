'use client';

import { Button, Typography } from '@/components/ui';
import { Test, UserTestResult } from '@/modules/courses/types/test.type';

type QuizResultProps = {
  test?: Test;
  testResults?: UserTestResult[];
  onViewAnswer?: () => void;
  onNext?: () => void;
};

const getNumberOfCorrectAnswers = (testResults: UserTestResult[]) => {
  return testResults.filter((result) => result.point > 0).length;
};

const getIsPassed = (test: Test | undefined, testResults: UserTestResult[]) => {
  const numberOfCorrectAnswers = getNumberOfCorrectAnswers(testResults);
  return numberOfCorrectAnswers >= (test?.minCorrectAnswer || 0);
};

export const QuizResult = ({ test, testResults, onViewAnswer, onNext }: QuizResultProps) => {
  const numberOfCorrectAnswers = getNumberOfCorrectAnswers(testResults || []);

  const isPassed = getIsPassed(test, testResults || []);

  return (
    <div className="flex h-full flex-col items-center justify-center bg-white px-6 py-8">
      <Typography variant="headlineLg" className="mb-2">
        {isPassed ? 'Chúc mừng bạn!' : 'Rất tiếc!'}
      </Typography>

      <Typography variant="bodyMd" className="mb-8">
        Bạn trả lời đúng {numberOfCorrectAnswers}/{test?.questions?.length ?? 0} câu
      </Typography>

      <div className="flex gap-4">
        <Button onClick={onViewAnswer} variant="secondary">
          Xem lại đáp án
        </Button>

        <Button onClick={onNext}>Chuyển tới bài học tiếp theo</Button>
      </div>
    </div>
  );
};
