import { Drawer, Typography } from '@/components/ui';

import { LearnerStudyingIcon } from '@/icons';
import IconCollapsePopup from '@/icons/IconCollapsePopup';
import IconQuestionDone from '@/icons/IconQuestionDone';
import IconQuestionNotDone from '@/icons/IconQuestionNotDone';
import { Question, UserTestResult } from '@/modules/courses/types/test.type';
import { CheckCircleIcon, XCircleIcon } from '@heroicons/react/24/solid';
import { TestViewMode } from './test.type';

type TestQuestionListProps = {
  isOpen: boolean;
  questions: Question[];
  currentQuestionIndex: number;

  selectedAnswers: { questionIndex: number; answers: number[] }[];
  userAnswers: UserTestResult[] | null;
  viewMode: TestViewMode;

  onOpen: (isOpen: boolean) => void;
  onSelectQuestion: (index: number) => void;
};

const renderStatusQuestion = ({
  currentQuestionIndex,
  questionIndex,
  selectedAnswers,
  userAnswers,
  viewMode,
}: {
  currentQuestionIndex: number;
  questionIndex: number;
  selectedAnswers: { questionIndex: number; answers: number[] }[];
  userAnswers: UserTestResult[] | null;
  viewMode: TestViewMode;
}) => {
  const isLearning = currentQuestionIndex === questionIndex;

  if (viewMode === 'viewAnswer') {
    if (userAnswers ? userAnswers?.[questionIndex]?.point > 0 : false) {
      return <CheckCircleIcon className="size-6 text-green-600" />;
    }
    return <XCircleIcon className="size-6 text-red-500" />;
  }

  if (isLearning) {
    return <LearnerStudyingIcon />;
  }

  const selectedAnswer = selectedAnswers.find((item) => item.questionIndex === questionIndex)?.answers || [];

  if (selectedAnswer.length > 0) {
    return <IconQuestionDone />;
  }

  return <IconQuestionNotDone />;
};

function TestQuestionListDrawer(props: TestQuestionListProps) {
  const {
    isOpen,
    questions,
    currentQuestionIndex,
    selectedAnswers,
    userAnswers,
    viewMode,

    onSelectQuestion,
    onOpen,
  } = props;

  if (!isOpen) return;

  return (
    <Drawer
      open={isOpen}
      onClose={() => onOpen(false)}
      closable
      closeIcon={<IconCollapsePopup />}
      title={'Danh sách câu hỏi'}
      placement="right"
      width={400}
    >
      <div className="flex flex-col gap-4">
        {questions.map((question, index) => (
          <div
            key={question.id}
            className="flex cursor-pointer items-center gap-2 rounded-md p-2 hover:bg-neutral-50"
            onClick={() => onSelectQuestion(index)}
          >
            {renderStatusQuestion({
              currentQuestionIndex,
              questionIndex: index,
              selectedAnswers,
              userAnswers,
              viewMode,
            })}
            <Typography>{question.questionName}</Typography>
          </div>
        ))}
      </div>
    </Drawer>
  );
}

export default TestQuestionListDrawer;
