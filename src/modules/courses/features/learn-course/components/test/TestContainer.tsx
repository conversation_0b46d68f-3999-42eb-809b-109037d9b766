'use client';

import { Typography } from '@/components/ui';
import { routePaths } from '@/config';
import { QUERY_KEYS } from '@/constants/query-keys';
import { useNotification } from '@/hooks';
import { ChapterType } from '@/modules/courses/constants/course.const';
import { FinalTestResult } from '@/modules/courses/features/learn-course/components/test/FinalTestResult';
import { QuizResult } from '@/modules/courses/features/learn-course/components/test/QuizResult';
import { TestViewMode } from '@/modules/courses/features/learn-course/components/test/test.type';
import TestControls from '@/modules/courses/features/learn-course/components/test/TestControls';
import useMarkLectureComplete from '@/modules/courses/features/learn-course/hooks/useMarkLectureComplete';
import useTestActions from '@/modules/courses/features/learn-course/hooks/useTestActions';
import { getNextLecture } from '@/modules/courses/features/learn-course/utils/lecture.util';
import { CourseInfo, Section } from '@/modules/courses/types/course.type';
import { Test, UserTestResult } from '@/modules/courses/types/test.type';
import { formatApiUrl } from '@/utils/url.util';
import { useParams, useRouter } from 'next/navigation';
import React from 'react';
import { useQueryClient } from 'react-query';
import TestQuestionContent from './TestQuestionContent';

type TestContainerProps = {
  testInfo?: Test | null;
  courseInfo: CourseInfo;
  testType?: 'quiz' | 'finalTest';
};

const getLatestSubmitTime = (userAnswers: UserTestResult[] | null) => {
  if (!userAnswers) return 0;
  return Math.max(...userAnswers.map((item) => item.times ?? 0));
};

const filterUserAnswers = (userAnswers: UserTestResult[] | null, latestSubmitTime: number) => {
  if (!userAnswers) return [];
  return userAnswers.filter((item) => item.times === latestSubmitTime);
};

const getPreviousLecture = (sections: Section[], params: { sectionId: string }) => {
  const currentSectionIndex = sections.findIndex((section) => section.id === params.sectionId);

  if (currentSectionIndex === 0) return null;

  const previousSection = sections[currentSectionIndex - 1];
  const lectures = previousSection?.lectures || [];
  return lectures[lectures.length - 1];
};

const TestContainer = (props: TestContainerProps) => {
  const { testInfo, courseInfo, testType = 'quiz' } = props;

  const params = useParams<{ courseId: string; sectionId: string; lectureId: string }>();

  const router = useRouter();

  const notification = useNotification();

  const queryClient = useQueryClient();

  const { onMarkCompleteLecture, isLoading: isMarkCompleteLoading } = useMarkLectureComplete();

  const { onSubmitTestQuestion, onGetTestAnswers: onGetTestAnswersMutation } = useTestActions();

  const [currentQuestionIndex, setCurrentQuestionIndex] = React.useState(0);

  const [openConfirmSubmitModal, setOpenConfirmSubmitModal] = React.useState(false);

  const [selectedAnswers, setSelectedAnswers] = React.useState<{ questionIndex: number; answers: number[] }[]>([]);

  const [userAnswers, setUserAnswers] = React.useState<UserTestResult[] | null>(null);

  const [viewMode, setViewMode] = React.useState<TestViewMode>('default');

  const questions = testInfo?.questions || [];

  const isLastQuestion = currentQuestionIndex === questions.length - 1;

  const previousLecture = getPreviousLecture(courseInfo?.sections || [], params);

  const handleSelectQuestion = (index: number) => {
    setCurrentQuestionIndex(index);
  };

  const handleConfirmAnswer = () => {
    if (isLastQuestion) {
      setOpenConfirmSubmitModal(true);
      return;
    }

    setCurrentQuestionIndex(currentQuestionIndex + 1);
  };

  const handleSubmitTest = () => {
    const uncompletedQuestions = questions.filter((_, index) => {
      return !selectedAnswers.some((answer) => answer.questionIndex === index);
    });
    if (uncompletedQuestions.length > 0) {
      notification.error({
        message: 'Vui lòng hoàn thành tất cả câu hỏi trước khi nộp bài',
      });
      setOpenConfirmSubmitModal(false);
      return;
    }

    const submitTestPayload = {
      courseId: params?.courseId || '',
      sectionId: params?.sectionId || '',
      testId: testInfo?.id || '',
      data: selectedAnswers.map((item) => ({
        question_id: questions[item.questionIndex].id,
        answer: item.answers,
      })),
    };

    onSubmitTestQuestion(submitTestPayload, {
      onSuccess: () => {
        onMarkCompleteLecture({ ...params, isCompleted: true });

        queryClient.invalidateQueries({
          queryKey: [QUERY_KEYS.COURSE_DETAIL, params?.courseId || ''],
        });

        onGetTestAnswersMutation(
          {
            courseId: params?.courseId || '',
            sectionId: params?.sectionId || '',
            testId: testInfo?.id || '',
          },
          {
            onSuccess: (data) => {
              setOpenConfirmSubmitModal(false);
              const latestSubmitTime = getLatestSubmitTime(data);
              const filteredData = filterUserAnswers(data, latestSubmitTime);
              setUserAnswers(filteredData);
              setViewMode('viewResult');
            },
          },
        );
      },
    });
  };

  const resetData = () => {
    setViewMode('default');
    setCurrentQuestionIndex(0);
    setSelectedAnswers([]);
    setUserAnswers(null);
  };

  const handleNextToLecture = () => {
    const nextLectureData = getNextLecture({ sections: courseInfo?.sections || [], params });
    if (!nextLectureData) return;

    if (nextLectureData?.section.sectionType === ChapterType.Test) {
      const nextLectureUrl = formatApiUrl(routePaths.learner.children.finalTest.path, {
        courseId: params?.courseId || '',
        sectionId: nextLectureData?.section.id,
        testId: nextLectureData?.section.test?.id || '',
      });
      resetData();
      router.push(nextLectureUrl);
      return;
    }

    const nextLectureUrl = formatApiUrl(routePaths.learner.children.lecture.path, {
      courseId: params?.courseId || '',
      sectionId: nextLectureData?.section.id,
      lectureId: nextLectureData?.lecture?.id || '',
    });

    resetData();
    router.push(nextLectureUrl);
  };

  const renderContent = () => {
    if (viewMode === 'viewResult' && testType === 'finalTest') {
      return (
        <FinalTestResult
          test={testInfo!}
          testResults={userAnswers || []}
          onRetry={() => resetData()}
          onViewAnswer={() => {
            setViewMode('viewAnswer');
            setCurrentQuestionIndex(0);
          }}
          onViewPreviousLecture={() => {
            if (!previousLecture) return;
            const previousLectureUrl = formatApiUrl(routePaths.learner.children.lecture.path, {
              courseId: params?.courseId || '',
              sectionId: params?.sectionId || '',
              lectureId: previousLecture?.id || '',
            });
            resetData();
            router.push(previousLectureUrl);
          }}
        />
      );
    }

    if (viewMode === 'viewResult' && testType === 'quiz') {
      return (
        <QuizResult
          test={testInfo!}
          testResults={userAnswers || []}
          onViewAnswer={() => {
            setViewMode('viewAnswer');
            setCurrentQuestionIndex(0);
          }}
          onNext={handleNextToLecture}
        />
      );
    }

    return (
      <TestQuestionContent
        key={currentQuestionIndex}
        isViewAnswer={viewMode === 'viewAnswer'}
        testInfo={testInfo || null}
        selectedAnswers={selectedAnswers}
        currentQuestionIndex={currentQuestionIndex}
        userAnswers={userAnswers}
        setSelectedAnswers={setSelectedAnswers}
        onNext={() => {
          if (isLastQuestion) {
            handleNextToLecture();
            return;
          }
          setCurrentQuestionIndex(currentQuestionIndex + 1);
        }}
        onConfirmAnswer={handleConfirmAnswer}
      />
    );
  };

  return (
    <React.Fragment>
      <div className="flex h-screen flex-col bg-white">
        <div className="bg-black p-4">
          <Typography className="text-white">{testInfo?.testName}</Typography>
        </div>

        <div className="flex size-full items-center justify-center py-10">{renderContent()}</div>

        <TestControls
          currentQuestionIndex={currentQuestionIndex}
          questions={questions}
          isMarkCompleteLoading={isMarkCompleteLoading}
          selectedAnswers={selectedAnswers}
          userAnswers={userAnswers}
          viewMode={viewMode}
          openConfirmSubmitModal={openConfirmSubmitModal}
          onSelectQuestion={handleSelectQuestion}
          setCurrentQuestionIndex={setCurrentQuestionIndex}
          onSubmitTest={handleSubmitTest}
          setOpenConfirmSubmitModal={setOpenConfirmSubmitModal}
        />
      </div>
    </React.Fragment>
  );
};

export default TestContainer;
