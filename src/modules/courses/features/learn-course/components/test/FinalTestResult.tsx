'use client';

import { Button, Typography } from '@/components/ui';
import { routePaths } from '@/config';
import { UserTestResult } from '@/features/courses';
import { useClient } from '@/hooks/useClient';
import { Test } from '@/modules/courses/types/test.type';
import { formatApiUrl } from '@/utils/url.util';
import { useParams, useRouter } from 'next/navigation';
import { useMemo } from 'react';
import React<PERSON>on<PERSON>tti from 'react-confetti';

type TestResultProps = {
  test?: Test;
  testResults?: UserTestResult[];
  onRetry?: () => void;
  onViewAnswer?: () => void;
  onViewPreviousLecture?: () => void;
};

export const FinalTestResult = (props: TestResultProps) => {
  const { test, testResults, onRetry, onViewAnswer, onViewPreviousLecture } = props;
  const router = useRouter();
  const params = useParams<{ courseId: string; sectionId: string; testId: string }>();

  const isClient = useClient();

  const numberOfCorrectAnswers = useMemo(() => {
    if (!testResults || !test?.questions) return 0;
    return testResults.filter((result) => result.point > 0).length;
  }, [testResults, test]);

  const isPassed = numberOfCorrectAnswers >= (test?.minCorrectAnswer || 0);
  console.log('test: ', test);
  console.log('numberOfCorrectAnswers: ', numberOfCorrectAnswers);
  console.log('isPassed: ', isPassed);

  const handleCompleteCourse = () => {
    const url = formatApiUrl(routePaths.learner.children.finalTest.children.review.path, {
      courseId: params?.courseId || '',
      sectionId: params?.sectionId || '',
      testId: params?.testId || '',
    });
    router.push(url);
  };

  return (
    <div className="flex size-full w-1/3 flex-col items-center justify-center gap-4 bg-white px-6 py-8">
      {isClient && isPassed && <ReactConfetti width={window.innerWidth} height={window.innerHeight} />}

      <Typography variant="headlineLg">{isPassed ? 'Chúc mừng!' : 'Rất tiếc!'}</Typography>

      <div className="flex flex-col items-center justify-center gap-1">
        <Typography variant="bodyLg">
          {isPassed ? 'Bạn đã đạt đủ điểm trong bài kiểm tra' : 'Bạn chưa đạt đủ điểm trong bài kiểm tra'}
        </Typography>

        {!isPassed && (
          <Typography variant="bodyLg">
            Cần ít nhất {test?.minCorrectAnswer} câu trả lời đúng để hoàn thành bài kiểm tra
          </Typography>
        )}

        <Typography variant="bodyMd">
          (Bạn đã trả lời đúng {numberOfCorrectAnswers}/{test?.questions?.length ?? 0} câu)
        </Typography>
      </div>

      <div className="flex w-full gap-4">
        <Button onClick={isPassed ? onViewAnswer : onViewPreviousLecture} variant="secondary" className="w-full">
          {isPassed ? 'Xem lại đáp án' : 'Xem lại bài học trước'}
        </Button>

        <Button className="w-full" onClick={isPassed ? handleCompleteCourse : onRetry}>
          {isPassed ? 'Tiếp tục' : 'Làm lại bài kiểm tra'}
        </Button>
      </div>
    </div>
  );
};
