'use client';

import { Button, Checkbox, Radio, Typography } from '@/components/ui';
import { cn } from '@/lib/utils';
import { Test, UserTestResult } from '@/modules/courses/types/test.type';
import { CheckCircleIcon, XCircleIcon } from '@heroicons/react/24/solid';
import React from 'react';
import TestQuestionMedia from './TestQuestionMedia';

const convertNumberToOrderLetter = (index: number) => {
  const orderLetter = String.fromCharCode(65 + index);
  return orderLetter;
};

const TestQuestionContent = (props: {
  testInfo: Test | null;
  selectedAnswers: { questionIndex: number; answers: number[] }[];
  currentQuestionIndex: number;

  userAnswers: UserTestResult[] | null;

  isViewAnswer?: boolean;

  onNext: () => void;

  setSelectedAnswers: React.Dispatch<React.SetStateAction<{ questionIndex: number; answers: number[] }[]>>;
  onConfirmAnswer: () => void;
}) => {
  const {
    testInfo,
    currentQuestionIndex,
    selectedAnswers,
    userAnswers,
    isViewAnswer = false,
    onNext,
    onConfirmAnswer,
    setSelectedAnswers,
  } = props;

  const selectedAnswer = selectedAnswers.find((item) => item.questionIndex === currentQuestionIndex)?.answers || [];

  const questions = testInfo?.questions || [];

  const questionName = questions[currentQuestionIndex]?.questionName || '';
  const questionOptions = questions[currentQuestionIndex]?.questionOptions || [];
  const correctAnswer = questions[currentQuestionIndex]?.correctAnswer || [];
  const questionImageFile = questions[currentQuestionIndex]?.questionImageFile || null;
  const questionVideoFile = questions[currentQuestionIndex]?.videoFile || null;

  const isMultipleAnswer = correctAnswer.length > 1;
  const isAnswerCorrect = userAnswers ? userAnswers?.[currentQuestionIndex]?.point > 0 : false;

  const isLastQuestion = currentQuestionIndex === questions.length - 1;

  const handleMultipleChoiceAnswer = (
    answerIndex: number,
    existing: { questionIndex: number; answers: number[] },
    prev: { questionIndex: number; answers: number[] }[],
  ) => {
    if (existing.answers.includes(answerIndex)) {
      return prev.map((item) =>
        item.questionIndex === currentQuestionIndex
          ? { ...item, answers: item.answers.filter((index) => index !== answerIndex) }
          : item,
      );
    }

    return prev.map((item) =>
      item.questionIndex === currentQuestionIndex ? { ...item, answers: [...item.answers, answerIndex] } : item,
    );
  };

  const handleSingleChoiceAnswer = (answerIndex: number, prev: { questionIndex: number; answers: number[] }[]) => {
    return prev.map((item) =>
      item.questionIndex === currentQuestionIndex ? { ...item, answers: [answerIndex] } : item,
    );
  };

  const handleSelectAnswer = (answerIndex: number) => {
    setSelectedAnswers((prev) => {
      const existing = prev.find((item) => item.questionIndex === currentQuestionIndex);

      if (!existing) {
        return [...prev, { questionIndex: currentQuestionIndex, answers: [answerIndex] }];
      }

      if (isMultipleAnswer) {
        return handleMultipleChoiceAnswer(answerIndex, existing, prev);
      }

      return handleSingleChoiceAnswer(answerIndex, prev);
    });
  };

  const handleConfirmAnswer = () => {
    onConfirmAnswer();
  };

  const renderSubmitButton = () => {
    if (isViewAnswer) {
      return (
        <Button variant="primary" onClick={onNext}>
          {isLastQuestion ? 'Chuyển tới bài học tiếp theo' : 'Tiếp tục'}
        </Button>
      );
    }

    return (
      <Button variant="primary" onClick={handleConfirmAnswer} disabled={!selectedAnswer.length}>
        Xác nhận đáp án
      </Button>
    );
  };

  if (!testInfo) return null;

  return (
    <div className="flex w-1/2 flex-col justify-center gap-8">
      <Typography variant="headlineMd" className="text-center">
        {questionName}
      </Typography>

      <TestQuestionMedia questionImageFile={questionImageFile} questionVideoFile={questionVideoFile} />

      <Typography variant="bodyLg" className="text-center">
        {isMultipleAnswer ? 'Chọn tất cả đáp án đúng' : 'Chọn đáp án đúng'}
      </Typography>

      <div className="grid grid-cols-2 gap-4">
        {questionOptions.map((answer, index) => {
          const isChecked = selectedAnswer.includes(answer.optionIndex);
          const orderLetter = convertNumberToOrderLetter(index);

          return (
            <div key={answer.optionIndex} className="flex items-center">
              <div className="mr-2">{orderLetter}.</div>
              <div
                key={answer.optionIndex}
                onClick={() => {
                  if (isViewAnswer) return;
                  handleSelectAnswer(answer.optionIndex);
                }}
                className={cn('flex-1 cursor-pointer rounded-md border border-neutral-200 p-4 hover:border-primary', {
                  'bg-primary-50': isChecked,
                  'cursor-not-allowed hover:border-neutral-200': isViewAnswer,
                })}
              >
                <div className="flex items-center gap-3">
                  {isMultipleAnswer ? (
                    <Checkbox
                      checked={isChecked}
                      disabled={isViewAnswer}
                      onClick={() => handleSelectAnswer(answer.optionIndex)}
                    />
                  ) : (
                    <Radio
                      checked={isChecked}
                      disabled={isViewAnswer}
                      onClick={() => handleSelectAnswer(answer.optionIndex)}
                    />
                  )}

                  <div dangerouslySetInnerHTML={{ __html: answer.optionName }} />
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {isViewAnswer && (
        <div className="flex items-center justify-center gap-2">
          {isAnswerCorrect ? (
            <div className="flex items-center gap-2">
              <CheckCircleIcon className="size-6 text-green-600" />
              <Typography variant="bodyLg" className="text-green-600">
                Đáp án chính xác
              </Typography>
            </div>
          ) : (
            <div className="flex items-center gap-2">
              <XCircleIcon className="size-6 text-red-500" />
              <div className="flex gap-2">
                <Typography variant="bodyLg" className="text-red-600">
                  Đáp án không chính xác.
                </Typography>
                <Typography variant="bodyLg" className="text-red-600">
                  Đáp án đúng là {correctAnswer.map((index) => convertNumberToOrderLetter(index)).join(', ')}
                </Typography>
              </div>
            </div>
          )}
        </div>
      )}

      <div className="flex justify-center">{renderSubmitButton()}</div>
    </div>
  );
};

export default TestQuestionContent;
