'use client';

import { Icon } from '@/components/client';
import { Button, Typography } from '@/components/ui';
import { CourseInfo } from '@/modules/courses/types/course.type';
import { XMarkIcon } from '@heroicons/react/24/solid';
import { routePaths } from 'config';
import { ImageLogo } from 'images';
import Image from 'next/image';
import Link from 'next/link';
import LearnCourseProgress from './LearnCourseProgress';

type LearnCourseHeaderProps = {
  courseInfo: CourseInfo;
  onClose?: () => void;
};
const LearnCourseHeader = (props: LearnCourseHeaderProps) => {
  const { courseInfo, onClose } = props;

  return (
    <div className="h-[72px] px-6 py-4">
      <div className="flex items-center justify-between">
        <div>
          <div className="flex w-full items-center gap-6">
            <Link href={routePaths.profile.path}>
              <Image width={130} height={32} src={ImageLogo} alt="logo" />
            </Link>

            <div className="h-[16px] w-px bg-black" />

            <Link href={`${routePaths.profile.children.course.path}/${courseInfo?.id}`}>
              <Typography variant="labelLg">{courseInfo?.courseName}</Typography>
            </Link>
          </div>
        </div>
        <div className="flex items-center gap-4">
          <LearnCourseProgress courseInfo={courseInfo} />

          {onClose && (
            <Button variant="ghost" className="text-black hover:bg-neutral-100 active:bg-neutral-50" onClick={onClose}>
              <Icon icon={<XMarkIcon />} />
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};

export default LearnCourseHeader;
