'use client';

import { Typography } from '@/components/ui';
import { CourseInfo } from '@/modules/courses/types/course.type';
import { useChapterDrawer } from 'hooks/useChapterDrawer';
import CollapseLearnerIcon from 'icons/CollapseLearnerIcon';
import dynamic from 'next/dynamic';
import React from 'react';

type ChapterDrawerProps = {
  courseInfo: CourseInfo;
};

const ChapterCollapse = dynamic(() => import('./SectionListItem'), {
  loading: () => <div className={'text-center'}><PERSON><PERSON> tải chương học...</div>,
  ssr: false,
});

const ChapterDrawer: React.FC<ChapterDrawerProps> = ({ courseInfo }) => {
  const { handleCloseChapterDrawer, open } = useChapterDrawer();

  if (!open) return null;

  return (
    <div onClick={handleCloseChapterDrawer} className={'pointer-events-none fixed inset-0 z-[1002]'}>
      <div className={'pointer-events-auto absolute inset-0 z-[1002]'} style={{ background: 'rgba(0, 0, 0, 0.45)' }}>
        <div
          onClick={(e) => e.stopPropagation()}
          className={'absolute inset-y-0 right-0 z-[1000] h-full w-[378px] max-w-full drop-shadow transition-all'}
        >
          <div className={'flex size-full flex-col bg-white'}>
            <div className={'border-b border-b-neutral-200 p-4'}>
              <div className="flex justify-between">
                <Typography variant="titleMd">Nội dung khóa học</Typography>

                <span className="cursor-pointer" onClick={handleCloseChapterDrawer}>
                  <CollapseLearnerIcon />
                </span>
              </div>
            </div>

            <ChapterCollapse courseInfo={courseInfo} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChapterDrawer;
