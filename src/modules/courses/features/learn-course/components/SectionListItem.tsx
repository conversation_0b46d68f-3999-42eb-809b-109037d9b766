'use client';

import { Typography } from '@/components/ui';
import { routePaths } from '@/config';
import { cn } from '@/lib/utils';
import { ChapterType } from '@/modules/courses/constants/course.const';
import { CourseInfo, Lecture, Section } from '@/modules/courses/types/course.type';
import { formatApiUrl } from '@/utils/url.util';
import { ChevronRightIcon } from '@heroicons/react/24/outline';
import { Collapse } from 'antd';
import { ChapterStatus } from 'components/learner/type';
import { useChapterDrawer } from 'hooks/useChapterDrawer';
import FinalChapterIcon from 'icons/FinalChapterIcon';
import LearnerStudyingIcon from 'icons/LearnerStudyingIcon';
import LearnerUnCheckIcon from 'icons/LearnerUnCheckIcon';
import LearningChapterIcon from 'icons/LearningChapterIcon';
import LectureCheckedIcon from 'icons/LectureCheckedIcon';
import { useRouter } from 'next-nprogress-bar';
import { useParams } from 'next/navigation';

type ChapterCollapseProps = {
  courseInfo: CourseInfo;
};

export const returnStatusLecture = (lecture: Lecture, courseInfo: CourseInfo, lectureId: string) => {
  const completedLectures = courseInfo?.userCompletedLecture ?? [];
  const completedLectureIds = completedLectures.map((item) => item.lecture.id);
  const isCompletedLecture = completedLectureIds.includes(lecture.id);
  if (lecture.id === lectureId && !isCompletedLecture) {
    return ChapterStatus.Studying;
  }
  return isCompletedLecture ? ChapterStatus.Checked : ChapterStatus.Uncheck;
};

export const returnStatusChapter = (preSection: Section, section: Section, courseInfo: CourseInfo) => {
  const completedLectures = courseInfo?.userCompletedLecture ?? [];
  const completedLectureIds = completedLectures?.map((item) => item.lecture.id) || [];
  const isCompletedAllLectureInPreSection = preSection?.lectures.every((lecture) =>
    completedLectureIds.includes(lecture.id),
  );
  if (completedLectures?.length === 0) {
    // check lock/ unlock section
    return section.publish === 1 ? ChapterStatus.Locked : ChapterStatus.Uncheck;
  }

  const completedLecturesFiltered = section.lectures.filter((lecture) => completedLectureIds?.includes(lecture.id));
  if (completedLecturesFiltered.length === 0 || section.lectures.length === 0) {
    return section.publish === 1 && !isCompletedAllLectureInPreSection ? ChapterStatus.Locked : ChapterStatus.Uncheck;
  }
  if (completedLecturesFiltered.length === section.lectures.length) {
    return ChapterStatus.Checked;
  }
  return ChapterStatus.Studying;
};

export const checkIsLockedSection = (preSection: Section | undefined, courseInfo: CourseInfo) => {
  const userCompletedLecture = courseInfo?.userCompletedLecture ?? [];
  const completedLectureIds = userCompletedLecture?.map((lecture) => lecture.lecture.id);
  const isCompletedAllLectureInPreSection = preSection?.lectures.every((lecture) =>
    completedLectureIds?.includes(lecture.id),
  );
  if (!preSection) return false;
  return courseInfo.isSequential === 1 && !isCompletedAllLectureInPreSection;
};

const getSections = (courseInfo: CourseInfo) => {
  const sections = courseInfo?.sections?.filter((section) => section.sectionType === ChapterType.Default) ?? [];
  const sectionTest = courseInfo?.sections?.filter((section) => section.sectionType === ChapterType.Test) ?? [];

  return [...sections, ...sectionTest];
};

export const SectionListItem = (props: ChapterCollapseProps) => {
  const { courseInfo } = props;
  const { handleCloseChapterDrawer } = useChapterDrawer();

  const params = useParams<{ courseId: string; sectionId: string; lectureId: string; testId: string }>();

  const { courseId = '', lectureId = '', sectionId = '', testId = '' } = params;

  const router = useRouter();

  const sections = getSections(courseInfo);

  const redirectAndCloseDrawer = (link: string) => {
    router.push(link);
    handleCloseChapterDrawer();
  };

  const chapterIcons = [
    {
      key: ChapterStatus.Checked,
      icon: <FinalChapterIcon />,
    },
    {
      key: ChapterStatus.Studying,
      icon: <LearningChapterIcon />,
    },
    {
      key: ChapterStatus.Studying,
      icon: <LearningChapterIcon />,
    },
    {
      key: ChapterStatus.Studying,
      icon: <LearningChapterIcon />,
    },
  ];

  const getIconChapter = (status: ChapterStatus) =>
    chapterIcons.find((chapterIcon) => chapterIcon.key === status)?.icon ?? <></>;

  const getLectureIcon = (status: ChapterStatus) => {
    const lectureICons = [
      {
        key: ChapterStatus.Checked,
        component: <LectureCheckedIcon />,
      },
      {
        key: ChapterStatus.Studying,
        component: <LearnerStudyingIcon />,
      },
      {
        key: ChapterStatus.Uncheck,
        component: <LearnerUnCheckIcon />,
      },
    ];
    return lectureICons.find((lectureICon) => lectureICon.key === status)?.component;
  };

  const getHeaderSection = (section: Section, courseInfo: CourseInfo, index: number) => {
    const preSection = courseInfo?.sections?.[index - 1];
    const className = checkIsLockedSection(preSection, courseInfo) ? 'opacity-50' : '';

    if (section.sectionType === ChapterType.Test) {
      const finalTestUrl = formatApiUrl(routePaths.learner.children.finalTest.path, {
        courseId,
        sectionId: section.id?.toString(),
        testId: section.test?.id?.toString() || '',
      });

      return (
        <div
          className={cn(
            'w-full',
            testId === section.test?.id.toString() ? 'text-primary' : 'text-black hover:text-primary',
          )}
          onClick={() => redirectAndCloseDrawer(finalTestUrl)}
        >
          <div className={'flex items-center gap-1.5'}>
            {getIconChapter(returnStatusChapter(preSection!, section, courseInfo))}

            <Typography className={cn(sectionId === section.id ? 'text-primary' : '')}>
              {section.sectionName}
            </Typography>
          </div>
        </div>
      );
    }

    return (
      <div className={`${className} flex w-full gap-2`}>
        {getIconChapter(returnStatusChapter(preSection!, section, courseInfo))}

        <Typography variant="titleSm" className={cn(sectionId === section.id ? 'text-primary' : '')}>
          {section.sectionName}
        </Typography>
      </div>
    );
  };

  const getHeaderLecture = (lecture: Lecture, section: Section, courseInfo: CourseInfo) => {
    const lectureUrl = formatApiUrl(routePaths.learner.children.lecture.path, {
      courseId,
      sectionId: section.id?.toString() as string,
      lectureId: lecture.id?.toString() as string,
    });

    const lectureStatus = returnStatusLecture(lecture, courseInfo, lectureId!);
    const lectureIcon = getLectureIcon(lectureStatus);

    return (
      <div
        onClick={() => redirectAndCloseDrawer(lectureUrl)}
        className={`${
          lecture.id === lectureId
            ? 'bg-primary-50 font-semibold text-primary'
            : 'hover:bg-neutral-50 hover:text-primary'
        } flex items-center truncate border-neutral-100 text-sm text-black`}
      >
        {lecture.lectureName && (
          <div className={`flex w-full cursor-pointer items-center gap-3 px-3`}>
            <div>{lectureIcon}</div>
            <Typography>{lecture.lectureName}</Typography>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="overflow-auto">
      <Collapse
        defaultActiveKey={[sectionId!]}
        expandIconPosition={'end'}
        expandIcon={({ isActive }) => <ChevronRightIcon className={`size-5 ${isActive ? 'rotate-90' : ''}`} />}
        bordered={false}
        items={sections?.map((section, index) => ({
          label: getHeaderSection(section, courseInfo, index),
          key: section.id?.toString(),
          showArrow: section.sectionType !== ChapterType.Test,
          children: section.lectures.map((lecture) => {
            return <div key={lecture.id}>{getHeaderLecture(lecture, section, courseInfo)}</div>;
          }),
        }))}
      />
    </div>
  );
};

export default SectionListItem;
