'use client';

import { Icon } from '@/components/client';
import { Typography } from '@/components/ui';
import { CourseInfo } from '@/modules/courses/types/course.type';
import { ChevronDownIcon } from '@heroicons/react/20/solid';
import { Dropdown, MenuProps, Progress } from 'antd';
import Goal from 'icons/Goal';
import LargeStarIcon from 'icons/LargeStarIcon';

function LearnCourseProgress({ courseInfo }: Readonly<{ courseInfo: CourseInfo }>) {
  const totalLectures = courseInfo?.totalLectures ?? 0;

  const countCompletedLectures = courseInfo?.countCompletedLectures ?? 0;

  const percentage = Math.round((countCompletedLectures * 100) / totalLectures);
  const onViewResultCourse = () => {};

  const getContentDropdown = () => {
    return (
      <div className={'flex w-[382px] flex-col gap-4 rounded-lg'}>
        <div className={'flex items-center gap-4'}>
          <div className={'flex items-center'}>
            <Progress
              type="circle"
              percent={percentage}
              size={40}
              format={() => (
                <div className={'flex items-center justify-center'}>
                  <Goal />
                </div>
              )}
            />
          </div>
          {percentage === 100 ? (
            <Typography>
              Bạn đã hoàn thành khóa học. Xem thành tích của bạn{' '}
              <Typography className={'cursor-pointer text-primary underline'} onClick={onViewResultCourse}>
                {' '}
                tại đây
              </Typography>
            </Typography>
          ) : (
            <Typography>
              Bạn đã hoàn thành <span className={'font-bold'}>{isNaN(percentage) ? 0 : percentage}%</span> khóa học
            </Typography>
          )}
        </div>

        {percentage !== 100 && (
          <div className={'flex flex-col gap-2 rounded-lg bg-neutral-50 p-4'}>
            <div className={'flex gap-2'}>
              <Icon icon={<LargeStarIcon />} className="pt-2" />
              <Typography>
                Hoàn thành khóa học để nhận: chứng chỉ{' '}
                <Typography className={'font-bold'}>Sơ cấp Lịch sử từ</Typography> ABC Academy
              </Typography>
            </div>
            <div className={'flex gap-2'}>
              <Icon icon={<LargeStarIcon />} className="pt-2" />

              <Typography>40 điểm kinh nghiệm tài khoản (cần 30 điểm nữa để lên cấp 12)</Typography>
            </div>
            <div className={'flex gap-2'}>
              <Icon icon={<LargeStarIcon />} className="pt-2" />
              <Typography>Hoàn thành 2 khóa học cùng trình độ Cơ bản để mở khóa bản đồ mới</Typography>
            </div>
          </div>
        )}
      </div>
    );
  };

  const menuDropdown: MenuProps['items'] = [
    {
      label: getContentDropdown(),
      key: '0',
    },
  ];
  return (
    <div className={'flex items-center gap-5'}>
      <Dropdown menu={{ items: menuDropdown }} trigger={['click']} className={'cursor-pointer'}>
        <div className="flex items-center gap-2">
          <Progress
            type="circle"
            percent={percentage}
            size={40}
            format={() => (
              <div className="flex items-center justify-center">
                <Goal />
              </div>
            )}
          />
          <Typography variant="labelLg">Tiến trình học của bạn</Typography>
          <Icon icon={<ChevronDownIcon />} />
        </div>
      </Dropdown>
    </div>
  );
}

export default LearnCourseProgress;
