'use client';

import { routePaths } from '@/config';
import InteractionVideo from '@/modules/courses/features/learn-course/components/video/InteractionVideo';
import useMarkLectureComplete from '@/modules/courses/features/learn-course/hooks/useMarkLectureComplete';
import { getNextLecture } from '@/modules/courses/features/learn-course/utils/lecture.util';
import { CourseInfo, Lecture } from '@/modules/courses/types/course.type';
import { formatApiUrl } from '@/utils/url.util';
import { useParams, useRouter } from 'next/navigation';

const useNextLecture = ({ courseInfo }: { courseInfo: CourseInfo }) => {
  const router = useRouter();
  const params = useParams<{ courseId: string; sectionId: string; lectureId: string }>();

  const handleNextLecture = () => {
    const nextVideoData = getNextLecture({ sections: courseInfo?.sections || [], params });
    if (!nextVideoData) return;

    const lectureId = nextVideoData?.lecture?.id;
    const sectionId = nextVideoData?.section?.id;

    if (lectureId && sectionId) {
      const nextLectureUrl = formatApiUrl(routePaths.learner.children.lecture.path, {
        courseId: params.courseId,
        sectionId: sectionId,
        lectureId: lectureId,
      });

      router.push(nextLectureUrl);
    }
  };

  return { onNextLecture: handleNextLecture };
};

const VideoContainer = (props: { lectureData: Lecture; courseInfo: CourseInfo }) => {
  const { lectureData, courseInfo } = props;

  const params = useParams<{ courseId: string; sectionId: string; lectureId: string }>();

  const { onMarkCompleteLecture } = useMarkLectureComplete();
  const { onNextLecture } = useNextLecture({ courseInfo });

  const videoUrl = lectureData?.videoId?.fileUrl;
  const isPreviewMode = false;

  if (!courseInfo) return null;

  return (
    <InteractionVideo
      reactPlayerProps={{
        url: videoUrl,
        onEnded: () => {
          if (!isPreviewMode) {
            onMarkCompleteLecture({ ...params, isCompleted: true });
          }

          onNextLecture();
        },
      }}
      selectedLecture={lectureData}
    />
  );
};

export default VideoContainer;
