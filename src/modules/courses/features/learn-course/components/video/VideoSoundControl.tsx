'use client';

import { SpeakerWaveIcon, SpeakerXMarkIcon } from '@heroicons/react/24/outline';
import { Popover, Slider } from 'antd';

type VideoSoundControlProps = {
  setMuted: (isMuted: boolean) => void;
  setVolume: (value: number) => void;

  muted: boolean;
  volume: number;
};

const VideoSoundControl = ({ setMuted, setVolume, volume, muted }: VideoSoundControlProps) => {
  return (
    <div className="relative z-50 flex">
      <Popover
        overlayInnerStyle={{
          background: '#0F1013BF',
          borderRadius: '4px',
        }}
        trigger={['hover', 'click']}
        arrow={false}
        content={() => (
          <div className={'relative flex h-24 w-[24px] items-center justify-center pt-2'}>
            <Slider
              min={0}
              max={100}
              vertical
              value={volume}
              onChange={(value: number) => {
                setVolume(value);
              }}
            />
          </div>
        )}
      >
        <button
          onClick={() => {
            setMuted(!muted);
          }}
          className="text-white"
        >
          {muted ? <SpeakerXMarkIcon className="size-6" /> : <SpeakerWaveIcon className="size-6" />}
        </button>
      </Popover>
    </div>
  );
};

export default VideoSoundControl;
