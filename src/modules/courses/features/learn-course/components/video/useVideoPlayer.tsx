import { useCallback, useRef, useState } from 'react';
import { OnProgressProps } from 'react-player/base';
import ReactPlayer from 'react-player/lazy';
import { PlayerProgressProps } from 'type/playerProgressProps';

let count = 0;

export function useVideoPlayer() {
  const videoRef = useRef<ReactPlayer>(null);
  const controlVideoRef = useRef<HTMLDivElement>(null);
  const backgroundControlRef = useRef<HTMLButtonElement>(null);
  const [playerProgress, setPlayerProgress] = useState<PlayerProgressProps>({
    playing: false,
    currentTime: 0,
    duration: 0,
    ready: false,
    volume: 100,
    muted: false,
    secondsLoaded: 0,
    seeking: false,
    buffering: false,
  });

  const handleTimeChange = useCallback((time: number, playingValue?: boolean) => {
    count = 0;
    if (!videoRef.current?.getCurrentTime()) {
      return;
    }
    videoRef.current.seekTo(time);

    setPlayerProgress((prevState) => ({
      ...prevState,
      currentTime: time,
      playing: playingValue || prevState.playing,
    }));
  }, []);

  const handleChangeProgress = (progress: OnProgressProps) => {
    if (!playerProgress.seeking) {
      const newProgress = {
        ...progress,
        currentTime: progress.playedSeconds,
        secondsLoaded: progress.loadedSeconds,
      };
      setPlayerProgress({
        ...playerProgress,
        ...newProgress,
      });
    }
    if (!playerProgress.playing) {
      count = 0;
      return;
    }
    if (!controlVideoRef.current || !backgroundControlRef.current) return;
    if (count > 3) {
      controlVideoRef.current.style.opacity = '0';
      backgroundControlRef.current.style.opacity = '0';
      count = 0;
    }
    if (controlVideoRef.current.style.opacity === '1') {
      count += 1;
    }
  };

  const handleVideoReady = (player: ReactPlayer) => {
    setPlayerProgress((prevState) => ({
      ...prevState,
      duration: player.getDuration(),
      ready: true,
    }));
  };

  const onBuffer = () =>
    setPlayerProgress((prevState) => ({
      ...prevState,
      buffering: true,
    }));

  const onBufferEnd = () =>
    setPlayerProgress((prevState) => ({
      ...prevState,
      buffering: false,
    }));
  const onHoverVideo = () => {
    count = 0;
    if (controlVideoRef.current) {
      controlVideoRef.current.style.opacity = '1';
    }
    if (backgroundControlRef.current) {
      backgroundControlRef.current.style.opacity = '1';
    }
  };

  const onFocusOutVideo = () => {
    if (!playerProgress.playing) return;
    if (controlVideoRef.current) {
      controlVideoRef.current.style.opacity = '0';
    }
    if (backgroundControlRef.current) {
      backgroundControlRef.current.style.opacity = '0';
    }
  };

  return {
    videoRef,
    playerProgress,
    controlVideoRef,
    backgroundControlRef,

    handleChangeProgress,
    handleTimeChange,
    setPlayerProgress,
    handleVideoReady,
    onBufferEnd,
    onBuffer,
    onFocusOutVideo,
    onHoverVideo,
  };
}
