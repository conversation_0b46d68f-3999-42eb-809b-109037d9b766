import { Icon, VideoProgressSlider } from '@/components/client';
import { But<PERSON> } from '@/components/ui';
import { secondToHHMMSS } from '@/lib/dateTime';
import VideoSoundControl from '@/modules/courses/features/learn-course/components/video/VideoSoundControl';
import { Lecture } from '@/modules/courses/types/course.type';
import { PlayerProgressProps } from '@/type/playerProgressProps';
import { PauseIcon, PlayIcon } from '@heroicons/react/24/solid';
import { useChapterDrawer } from 'hooks/useChapterDrawer';
import ChaptersIcon from 'icons/ChaptersIcon';
import CollapseIcon from 'icons/CollpaseIcon';
import ExpandIcon from 'icons/ExpendIcon';
import { RefObject } from 'react';
import 'react-video-seek-slider/styles.css';
import screenfull from 'screenfull';

type VideoControlsProps = {
  fullScreenRef: any;
  isFullScreen: boolean;
  isPreviewMode: boolean;
  selectedLecture: Lecture | undefined;
  isLearnerPreview?: boolean;

  setPlayerProgress: (
    playerProgress: PlayerProgressProps | ((prev: PlayerProgressProps) => PlayerProgressProps),
  ) => void;
  onHoverVideo: () => void;
  onFocusOutVideo: () => void;

  onProgressChanges: (currentTime: number) => void;

  playerProgress: PlayerProgressProps;

  controlVideoRef: RefObject<HTMLDivElement | null>;
  backgroundControlRef: RefObject<HTMLButtonElement | null>;
};

const VideoControls = (props: VideoControlsProps) => {
  const {
    isFullScreen,
    fullScreenRef,
    isPreviewMode,
    isLearnerPreview,
    playerProgress,
    backgroundControlRef,
    controlVideoRef,

    setPlayerProgress,
    onProgressChanges,
    onHoverVideo,
    onFocusOutVideo,
  } = props;

  const { currentTime, duration, volume, muted, playing, secondsLoaded } = playerProgress ?? {};
  const { handleOpenChapterDrawer } = useChapterDrawer();

  return (
    <>
      {/* <button
        ref={backgroundControlRef}
        onMouseLeave={onFocusOutVideo}
        onMouseEnter={onHoverVideo}
        onClick={() => setPlayerProgress({ ...playerProgress, playing: !playing })}
        className={'w-full rounded-b-xl transition-all'}
      /> */}

      <div
        ref={controlVideoRef}
        onMouseLeave={onFocusOutVideo}
        onMouseEnter={onHoverVideo}
        style={{ opacity: '1' }}
        className={'h-fit w-full bg-black transition-all'}
      >
        <div className={'h-4 w-full'}>
          <VideoProgressSlider
            duration={duration}
            currentTime={currentTime}
            bufferTime={secondsLoaded}
            onChange={(time) => {
              onProgressChanges(time);
            }}
          />
        </div>

        {/* {!isLearnerPreview && (
          <InteractionPoint
            totalTime={duration}
            selectedLecture={selectedLecture}
            setSelectedInteractionVideo={(value) => {
              setPlayerProgress({ ...playerProgress, playing: false });
              setSelectedInteractionVideo(value);
            }}
            currentTime={currentTime}
          />
        )} */}

        <div className={'flex h-full items-center justify-between px-4'} onClick={(e) => e.stopPropagation()}>
          <div className={'flex h-full w-4/5 items-center gap-8'}>
            <div className={'flex'}>
              <button
                onClick={() => {
                  setPlayerProgress({ ...playerProgress, playing: !playing });
                }}
              >
                {playing ? (
                  <Icon icon={<PauseIcon className="text-white" />} />
                ) : (
                  <Icon icon={<PlayIcon className="text-white" />} />
                )}
              </button>
            </div>

            <VideoSoundControl
              volume={volume}
              setVolume={(volumeChange) => {
                setPlayerProgress({ ...playerProgress, volume: volumeChange });
              }}
              muted={muted}
              setMuted={(mutedValue) => {
                setPlayerProgress({ ...playerProgress, muted: mutedValue });
              }}
            />

            <span
              className={'inline-block min-w-[100px] text-white'}
            >{`${secondToHHMMSS(currentTime)} / ${secondToHHMMSS(duration)}`}</span>

            {/* {currentSegmentName && (
              <div className={'flex items-center gap-1'}>
                <p className={'truncate text-sm text-white'}>
                  {currentSegmentName.length > 100 ? `${currentSegmentName.slice(0, 100)}...` : currentSegmentName}
                </p>
                <RightArrowIcon />
              </div>
            )} */}
          </div>

          <div className={'flex items-center gap-6'}>
            {!isFullScreen && (
              <Button
                variant="ghost-reversed"
                size="small"
                title="Xem danh sách chương"
                icon={<ChaptersIcon />}
                onClick={handleOpenChapterDrawer}
              >
                <ChaptersIcon />
              </Button>
            )}

            <button
              onClick={() => {
                screenfull.toggle(fullScreenRef.current);
              }}
            >
              {isFullScreen ? <CollapseIcon /> : <ExpandIcon />}
            </button>
          </div>
        </div>
      </div>
    </>
  );
};

export default VideoControls;
