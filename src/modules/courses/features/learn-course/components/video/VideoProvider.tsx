'use client';

import { useVideoPlayer } from '@/modules/courses/features/learn-course/components/video/useVideoPlayer';
import { createContext, ReactNode, RefObject, useContext } from 'react';
import { OnProgressProps } from 'react-player/base';
import ReactPlayer from 'react-player/lazy';
import { PlayerProgressProps } from 'type/playerProgressProps';

export interface VideoProviderProps {
  videoRef: RefObject<ReactPlayer>;
  controlVideoRef: RefObject<HTMLDivElement>;
  backgroundControlRef: RefObject<HTMLButtonElement>;
  playerProgress: PlayerProgressProps;
  handleTimeChange: (timeChange: number, playingValue?: boolean) => void;
  setPlayerProgress: (
    playerProgress: PlayerProgressProps | ((prev: PlayerProgressProps) => PlayerProgressProps),
  ) => void;
  handleChangeProgress: (progress: OnProgressProps) => void;
  handleVideoReady: (player: ReactPlayer) => void;
  onBuffer: () => void;
  onBufferEnd: () => void;
  onHoverVideo: () => void;
  onFocusOutVideo: () => void;
}

export const defaultProgress = {
  playing: false,
  duration: 0,
  volume: 100,
  muted: false,
  buffering: false,
  currentTime: 0,
  secondsLoaded: 0,
  seeking: false,
  ready: false,
};

export function useVideoProvider(): VideoProviderProps {
  const context = useContext(VideoContext);

  if (!context) {
    throw new Error('useVideoProvider must be used within a VideoProvider');
  }

  return context;
}

const VideoContext = createContext<VideoProviderProps | null>(null);

export function VideoProvider({
  children,
}: Readonly<{
  children: ReactNode;
}>) {
  const videoContainer: any = useVideoPlayer();

  return <VideoContext.Provider value={videoContainer}>{children}</VideoContext.Provider>;
}

export default VideoContext;
