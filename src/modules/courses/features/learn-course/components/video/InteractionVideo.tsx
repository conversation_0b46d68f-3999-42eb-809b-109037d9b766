'use client';

import { usePreventHydration } from '@/hooks';
import { useClient } from '@/hooks/useClient';
import InteractionOverlay from '@/modules/courses/components/interaction-overlay/InteractionOverlay';
import useVideoInteractions from '@/modules/courses/features/create-course/design-step/hooks/useVideoInteractions';
import VideoControls from '@/modules/courses/features/learn-course/components/video/VideoControls';
import { Lecture } from '@/modules/courses/types/course.type';
import { PlayerProgressProps } from '@/type/playerProgressProps';
import { useVideoProgressTracking } from 'hooks/useVideoProgressTracking';
import React, { useRef } from 'react';
import { OnProgressProps } from 'react-player/base';
import ReactPlayer, { ReactPlayerProps } from 'react-player/lazy';

type InteractionVideoProps = {
  reactPlayerProps: ReactPlayerProps;
  selectedLecture: Lecture | undefined;
  courseId?: string;
  sectionId?: string;
  isPreviewMode?: boolean;
  userId?: string;
};

function BufferingSpin() {
  return (
    <div className={'absolute top-0 z-10 flex size-full items-center justify-center'}>
      <div className="inline-block size-8 animate-spin rounded-full border-4 border-solid border-r-transparent align-[-0.125em] text-primary-500 motion-reduce:animate-[spin_1.5s_linear_infinite]"></div>
    </div>
  );
}

function InteractionVideo(props: InteractionVideoProps) {
  const { reactPlayerProps, selectedLecture, courseId, sectionId, isPreviewMode = false, userId } = props;

  const [playerProgress, setPlayerProgress] = React.useState<PlayerProgressProps>({
    playing: false,
    currentTime: 0,
    duration: 0,
    ready: false,
    volume: 100,
    muted: false,
    secondsLoaded: 0,
    seeking: false,
    buffering: false,
  });

  const playerRef = useRef<ReactPlayer>(null);
  const controlVideoRef = useRef<HTMLDivElement>(null);
  const backgroundControlRef = useRef<HTMLButtonElement>(null);

  const onHoverVideo = () => {};
  const onFocusOutVideo = () => {};

  const handleChangeProgress = (progress: OnProgressProps) => {
    setPlayerProgress((prev) => ({
      ...prev,
      currentTime: progress.playedSeconds,
      secondsLoaded: progress.loadedSeconds,
      played: progress.played,
      buffered: progress.loaded,
    }));
  };

  const onBuffer = () => setPlayerProgress((prev) => ({ ...prev, buffering: true }));
  const onBufferEnd = () => setPlayerProgress((prev) => ({ ...prev, buffering: false }));

  const { selectedInteraction, isShowInteraction, onCloseInteraction } = useVideoInteractions({
    interactions: selectedLecture?.lectureInteracts || [],
    currentTime: playerProgress.currentTime,
    onPause: () => {
      setPlayerProgress({
        ...playerProgress,
        playing: false,
      });
    },
  });

  const savedTimeRef = useRef<number>(0);
  // const isSeeked = useRef(false);

  usePreventHydration();

  // Track video progress every 5 seconds and restore saved progress
  useVideoProgressTracking({
    courseId: courseId || '',
    lectureId: selectedLecture?.id?.toString() || '',
    sectionId: sectionId || '',
    currentTime: playerProgress.currentTime,
    isPlaying: playerProgress.playing,
    isPreviewMode,
    userId,
    onProgressLoaded: (savedTime: number) => {
      // Store the saved time in a ref to avoid state updates that could cause loops
      if (savedTime > 0) {
        savedTimeRef.current = savedTime;
      }
    },
  });

  const handleVideoReadyWithProgress = () => {
    setPlayerProgress((prevState) => ({
      ...prevState,
      duration: selectedLecture?.videoId.fileDuration ?? 0,
      ready: true,
      buffering: false,
    }));

    // // Restore saved progress if available and current time is less than saved time
    // if (savedTimeRef.current > 0 && playerProgress.currentTime < savedTimeRef.current && !isSeeked.current) {
    //   // Small delay to ensure video is fully ready
    //   setTimeout(() => {
    //     if (playerRef.current) {
    //       isSeeked.current = true;
    //       playerRef.current.seekTo(savedTimeRef.current);
    //     }
    //   }, 100);
    // }
  };

  const isClient = useClient();
  if (!isClient) return null;

  return (
    <div className="relative flex h-[calc(100vh-72px)] w-full flex-col">
      {(!playerProgress.ready || playerProgress.buffering) && <BufferingSpin />}

      <div
        className="relative size-full flex-1 bg-black"
        onClick={() => {
          setPlayerProgress({ ...playerProgress, playing: !playerProgress.playing });
        }}
      >
        {isClient && (
          <ReactPlayer
            width="100%"
            height="100%"
            fallback={<BufferingSpin />}
            onBuffer={onBuffer}
            onBufferEnd={onBufferEnd}
            onReady={handleVideoReadyWithProgress}
            ref={playerRef}
            muted={playerProgress.muted}
            volume={playerProgress.volume / 100}
            playing={playerProgress.playing}
            onProgress={handleChangeProgress}
            config={{ file: { attributes: { crossOrigin: 'anonymous' } } }}
            {...reactPlayerProps}
          />
        )}

        {isShowInteraction && (
          <InteractionOverlay
            isVisible={!!isShowInteraction}
            currentInteraction={selectedInteraction}
            onClose={() => {
              onCloseInteraction();
              setPlayerProgress({ ...playerProgress, playing: true });
            }}
          />
        )}
      </div>

      <div className="relative z-10">
        <VideoControls
          playerProgress={playerProgress}
          setPlayerProgress={setPlayerProgress}
          fullScreenRef={false}
          isFullScreen={false}
          isPreviewMode={false}
          selectedLecture={selectedLecture}
          backgroundControlRef={backgroundControlRef}
          controlVideoRef={controlVideoRef}
          onHoverVideo={onHoverVideo}
          onFocusOutVideo={onFocusOutVideo}
          onProgressChanges={(time) => {
            if (playerRef.current) {
              playerRef.current.seekTo(time);
              setPlayerProgress((prev) => ({ ...prev, currentTime: time, playing: false }));
            }
          }}
        />
      </div>
    </div>
  );
}

export default InteractionVideo;
