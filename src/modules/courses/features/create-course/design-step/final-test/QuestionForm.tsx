'use client';

import { Button } from '@/components/ui';
import DraggableList from '@/modules/courses/components/draggable-list/DraggableList';
import DeleteQuestionModal from '@/modules/courses/features/create-course/design-step/quiz/DeleteQuestionModal';
import { PlusCircleIcon } from '@heroicons/react/24/outline';
import React from 'react';
import { useFieldArray } from 'react-hook-form';
import AnswerForm from './AnswerForm';
import { questionDefaultValue } from './final-test.schema';
import QuestionFormActions from './QuestionFormActions';
import QuestionFormItem from './QuestionFormItem';
import { useTypedFormContext } from './useTypedFormContext';

export const MIN_QUESTIONS = 10;
export const MAX_QUESTIONS = 50;

const checkCanAddQuestion = (questions: unknown[]): boolean => {
  if (!Array.isArray(questions)) return false;
  return questions.length < MAX_QUESTIONS;
};

const useQuestionForm = () => {
  const { control } = useTypedFormContext();

  const {
    fields: questions,
    append: addQuestion,
    insert,
    remove: deleteQuestion,
    swap: swapQuestion,
  } = useFieldArray({ control, name: 'questions' });

  const isDisabledAddQuestion = !checkCanAddQuestion(questions);

  const handleDuplicateQuestion = (index: number) => {
    insert(index + 1, questionDefaultValue);
  };

  return {
    questions,
    isDisabledAddQuestion,
    onAddQuestion: addQuestion,
    onDeleteQuestion: deleteQuestion,
    onSwapQuestion: swapQuestion,
    onDuplicateQuestion: handleDuplicateQuestion,
  };
};

function QuestionForm() {
  const { questions, isDisabledAddQuestion, onAddQuestion, onSwapQuestion, onDeleteQuestion, onDuplicateQuestion } =
    useQuestionForm();

  const [openConfirmDeleteQuestion, setOpenConfirmDeleteQuestion] = React.useState(false);
  const [selectedQuestionIndex, setSelectedQuestionIndex] = React.useState<number | null>(null);

  return (
    <div className="flex w-full flex-col gap-4">
      <DraggableList
        items={questions}
        droppableId="questions"
        onDragEnd={(result) => {
          if (!result.destination) return;
          onSwapQuestion(result.source.index, result.destination.index);
        }}
        renderItem={({ provided, item, index }) => {
          return (
            <div ref={provided.innerRef} {...provided.draggableProps} {...provided.dragHandleProps}>
              <QuestionFormItem
                key={item.id}
                index={index}
                actions={
                  <QuestionFormActions
                    questionIndex={index}
                    onDeleteQuestion={() => {
                      setOpenConfirmDeleteQuestion(true);
                      setSelectedQuestionIndex(index);
                    }}
                    onAddQuestion={() => onDuplicateQuestion(index)}
                    disabledDeleteButton={questions.length <= MIN_QUESTIONS}
                  />
                }
                optionsContent={<AnswerForm questionIndex={index} />}
              />
            </div>
          );
        }}
      />

      <div className="pl-6">
        <Button
          size="large"
          startIcon={<PlusCircleIcon />}
          onClick={() => onAddQuestion(questionDefaultValue)}
          disabled={isDisabledAddQuestion}
        >
          Thêm câu hỏi
        </Button>
      </div>

      {openConfirmDeleteQuestion && (
        <DeleteQuestionModal
          open={openConfirmDeleteQuestion}
          onConfirm={() => {
            if (selectedQuestionIndex !== null) {
              onDeleteQuestion(selectedQuestionIndex);
            }
            setOpenConfirmDeleteQuestion(false);
          }}
          onClose={() => setOpenConfirmDeleteQuestion(false)}
        />
      )}
    </div>
  );
}

export default QuestionForm;
