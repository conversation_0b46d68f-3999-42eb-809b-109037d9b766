import { Button, Typography } from '@/components/ui';
import useTestActions from '@/modules/courses/features/create-course/design-step/hooks/useTestActions';
import { useTypedFormContext } from '@/modules/courses/features/create-course/design-step/quiz/useTypedFormContext';

const FinalTestHeader = () => {
  const { testDetail } = useTestActions();
  const { formState } = useTypedFormContext();

  const isDisabledSaveButton = formState.isSubmitting || !formState.isDirty || !formState.isValid;

  return (
    <div className="flex w-full justify-between">
      <div className="flex w-1/3 max-w-full flex-col gap-2">
        <Typography variant="labelMd">{testDetail?.testName}</Typography>
        <Typography variant="headlineSm">{testDetail?.testName}</Typography>
      </div>
      <div className="flex items-end">
        <div>
          <Button
            variant="primary"
            loading={formState.isSubmitting}
            size="large"
            htmlType="submit"
            // disabled={isDisabledSaveButton}
          >
            Lưu bài kiểm tra
          </Button>
        </div>
      </div>
    </div>
  );
};

export default FinalTestHeader;
