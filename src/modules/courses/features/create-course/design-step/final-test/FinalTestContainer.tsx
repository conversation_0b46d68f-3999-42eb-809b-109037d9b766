'use client';

import { useNotification } from '@/hooks';
import useTestActions from '@/modules/courses/features/create-course/design-step/hooks/useTestActions';
import { zodResolver } from '@hookform/resolvers/zod';
import React from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { FinalTestFormData } from './final-test-form.type';
import { finalTestSchema, getFinalTestDefaultValues, getFinalTestPayloadRequest } from './final-test.schema';
import QuizHeader from './FinalTestHeader';
import QuizSettings from './FinalTestSettings';
import QuestionForm from './QuestionForm';

const useFinalTestForm = () => {
  const notification = useNotification();
  const { onUpdateTest, testDetail } = useTestActions();

  const formMethods = useForm({ mode: 'all', resolver: zodResolver(finalTestSchema) });

  const handleInvalid = (errors: any) => {
    const qErr = errors?.questions;
    const qArr = Array.isArray(qErr) ? qErr : [];

    const hasEmptyRequired =
      qArr.some((q: any) => q?.questionName?.message) ||
      qArr.some(
        (q: any) => Array.isArray(q?.questionAnswers) && q.questionAnswers.some((a: any) => a?.answerName?.message),
      );

    const missingCorrect = qArr.some((q: any) => q?.questionCorrectAnswer?.message);

    if (hasEmptyRequired && missingCorrect) {
      return notification.error({
        message: 'Lưu bài kiểm tra không thành công',
        description: 'Vui lòng không để trống các trường bắt buộc và chọn đáp án đúng cho câu hỏi',
      });
    }

    if (hasEmptyRequired) {
      return notification.error({
        message: 'Lưu bài kiểm tra không thành công',
        description: 'Vui lòng không để trống các trường bắt buộc',
      });
    }

    if (missingCorrect) {
      return notification.error({
        message: 'Lưu bài kiểm tra không thành công',
        description: 'Vui lòng chọn đáp án đúng cho câu hỏi!',
      });
    }

    const isMin10 = typeof qErr?.root?.message === 'string' && qErr.root.message.includes('tối thiểu 10');
    if (isMin10) {
      return notification.error({
        message: 'Lưu bài kiểm tra không thành công',
        description: 'Bài kiểm tra yêu cầu có tối thiểu 10 câu hỏi',
      });
    }

    return notification.error({
      message: 'Lưu bài kiểm tra không thành công',
      description: 'Vui lòng kiểm tra lại thông tin bài kiểm tra',
    });
  };

  const handleSaveTest = (values: FinalTestFormData) => {
    if (!testDetail) return;

    const payload = getFinalTestPayloadRequest(values, testDetail);
    onUpdateTest(payload, {
      onSuccess: () => {
        notification.success({ message: 'Lưu bài kiểm tra thành công' });
      },
      onError: () => {
        notification.error({ message: 'Lưu bài kiểm tra không thành công' });
      },
    });
    formMethods.reset(values);
  };

  React.useEffect(() => {
    if (testDetail) {
      const defaultValues = getFinalTestDefaultValues(testDetail);
      formMethods.reset(defaultValues);
    }
  }, [testDetail]);

  return {
    formMethods,
    onInvalid: handleInvalid,
    onSaveTest: handleSaveTest,
  };
};

export default function FinalTestContainer() {
  const { formMethods, onInvalid, onSaveTest } = useFinalTestForm();

  return (
    <FormProvider {...formMethods}>
      <form onSubmit={formMethods.handleSubmit(onSaveTest, onInvalid)} className="h-full">
        <div className="flex h-full flex-col gap-4 overflow-y-auto px-8 py-10">
          <QuizHeader />

          <div className="flex h-full min-h-fit gap-6">
            <QuestionForm />
            <QuizSettings />
          </div>
        </div>
      </form>
    </FormProvider>
  );
}
