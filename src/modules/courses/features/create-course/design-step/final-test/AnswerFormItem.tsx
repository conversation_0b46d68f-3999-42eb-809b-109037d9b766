'use client';

import { Icon } from '@/components/client/icon';
import { Button, Checkbox, Radio, RichTextEditor } from '@/components/ui';
import { PhotoIcon, TrashIcon } from '@heroicons/react/24/outline';
import Image from 'next/image';

import { cn } from '@/lib/utils';
import { LibraryModal } from '@/modules/courses/components';
import { LibraryFileType } from '@/modules/courses/constants/file.const';
import { HolderOutlined } from '@ant-design/icons';
import React from 'react';
import { Controller, useWatch } from 'react-hook-form';
import { useTypedFormContext } from './useTypedFormContext';

interface AnswerFormItemProps {
  answerIndex: number;
  questionIndex: number;
  onSelectAnswer: (isSelected: boolean) => void;
  onDeleteAnswer: () => void;
}

const MIN_ANSWERS = 2;

const AnswerFormItem = (props: AnswerFormItemProps) => {
  const { answerIndex, questionIndex, onDeleteAnswer, onSelectAnswer } = props;

  const { control, setValue, trigger } = useTypedFormContext();
  const [openLibraryModal, setOpenLibraryModal] = React.useState(false);

  const isMultiChoiceWatched = useWatch({ name: `questions.${questionIndex}.isMultipleChoice`, control });
  const correctAnswersWatched = useWatch({ name: `questions.${questionIndex}.questionCorrectAnswer`, control }) || [];

  const isChecked = correctAnswersWatched.includes(answerIndex);

  const thumbnailImageWatched = useWatch({
    name: `questions.${questionIndex}.questionAnswers.${answerIndex}.answerThumbnailImage`,
    control,
  });

  const questionAnswersWatched = useWatch({
    name: `questions.${questionIndex}.questionAnswers`,
    control,
  });

  const isDisabledDeleteAnswer = questionAnswersWatched.length <= MIN_ANSWERS;

  const handleSetThumbnailImage = (file: { fileUrl: string; fileId: string } | null) => {
    setValue(`questions.${questionIndex}.questionAnswers.${answerIndex}.answerThumbnailImage`, file, {
      shouldDirty: true,
    });

    trigger(`questions.${questionIndex}.questionAnswers.${answerIndex}.answerThumbnailImage`);
    setOpenLibraryModal(false);
  };

  return (
    <div className="flex items-start gap-4">
      <div className="mt-2 cursor-move">
        <HolderOutlined className="text-xl" />
      </div>

      <div className="mt-2">
        {isMultiChoiceWatched ? (
          <Checkbox checked={isChecked} onChange={(e) => onSelectAnswer(e.target.checked)} />
        ) : (
          <Radio checked={isChecked} onChange={(e) => onSelectAnswer(e.target.checked)} />
        )}
      </div>

      <div className="flex-1">
        <Controller
          name={`questions.${questionIndex}.questionAnswers.${answerIndex}.answerName`}
          control={control}
          render={({ field, fieldState: { error } }) => {
            return (
              <React.Fragment>
                <RichTextEditor
                  content={field.value || ''}
                  onChange={(content) => {
                    field.onChange(content);
                  }}
                  className={cn(
                    'w-full border-x-0 border-b border-t-0 border-b-neutral-200 bg-white p-0',
                    'focus-within:border-none focus-within:ring-0',
                    error && 'border-b border-red-500',
                  )}
                  placeholder="Nhập đáp án"
                />
                {error && <div className="mt-1 text-sm text-red-500">{error.message}</div>}
              </React.Fragment>
            );
          }}
        />

        {thumbnailImageWatched && (
          <div className="relative mt-2 size-fit">
            <Image
              src={thumbnailImageWatched.fileUrl}
              alt="Answer"
              className="h-[200px] w-[300px] rounded object-cover"
              width={100}
              height={100}
            />
            <div className="absolute right-1 top-1">
              <Button
                variant="ghost"
                size="small"
                className="text-primary_text"
                onClick={() => {
                  handleSetThumbnailImage(null);
                }}
              >
                <Icon icon={<TrashIcon />} />
              </Button>
            </div>
          </div>
        )}
      </div>

      <div className="flex items-center gap-1">
        <Button
          variant="ghost"
          size="small"
          className="text-primary_text hover:bg-neutral-50 hover:text-primary_text active:bg-neutral-50 active:text-primary_text"
          onClick={() => {
            setOpenLibraryModal(true);
          }}
        >
          <Icon icon={<PhotoIcon />} />
        </Button>

        <Button
          variant="ghost"
          size="small"
          className="text-primary_text hover:bg-neutral-50 hover:text-primary_text active:bg-neutral-50 active:text-primary_text"
          disabled={isDisabledDeleteAnswer}
          onClick={onDeleteAnswer}
        >
          <Icon icon={<TrashIcon />} />
        </Button>
      </div>

      {openLibraryModal && (
        <LibraryModal
          open={openLibraryModal}
          title="Chọn hình ảnh"
          enabledTypes={[LibraryFileType.IMAGE]}
          onAddFile={(file) => {
            handleSetThumbnailImage({ fileUrl: file.fileUrl, fileId: file.id });
          }}
          onClose={() => setOpenLibraryModal(false)}
        />
      )}
    </div>
  );
};

export default AnswerFormItem;
