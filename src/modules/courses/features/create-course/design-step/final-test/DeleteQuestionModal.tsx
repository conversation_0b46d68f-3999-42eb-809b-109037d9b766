import { Button, Modal, Typography } from '@/components/ui';

function DeleteQuestionModal({
  open,
  isDeleting,
  onClose,
  onConfirm,
}: {
  open: boolean;
  isDeleting?: boolean;
  onConfirm?: () => void;
  onClose?: () => void;
}) {
  return (
    <Modal
      open={open}
      title="Xóa câu hỏi này?"
      type="confirmation"
      onClose={onClose}
      footer={
        <div className="flex items-center justify-end gap-3 px-6 py-4">
          <Button variant="tertiary" onClick={onClose}>
            Hủy
          </Button>
          <Button variant="error" onClick={onConfirm} loading={isDeleting}>
            Xóa
          </Button>
        </div>
      }
    >
      <div className="flex flex-col gap-4 px-6 py-4">
        <div className="flex flex-col">
          <Typography variant="bodyLg">Bạn c<PERSON> chắc chắn muốn xóa câu hỏi này không?</Typography>
          <Typography variant="bodyLg">Dữ liệu sẽ không thể phục hồi lại sau khi đã xóa.</Typography>
        </div>
      </div>
    </Modal>
  );
}

export default DeleteQuestionModal;
