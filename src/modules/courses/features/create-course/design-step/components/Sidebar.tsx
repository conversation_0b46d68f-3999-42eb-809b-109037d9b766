'use client';

import { Icon } from '@/components/client';
import { Collapse, Typography } from '@/components/ui';
import { QUERY_KEYS } from '@/constants/query-keys';
import { useClient } from '@/hooks/useClient';
import useSafeSearchParams from '@/hooks/useSafeSearchParams';
import DotIcon from '@/icons/DotIcon';
import { cn } from '@/lib/utils';
import { ChapterType, LessonType } from '@/modules/courses/constants/course.const';
import { Lecture, Section } from '@/modules/courses/types/course.type';
import { ChevronDoubleLeftIcon, ChevronDoubleRightIcon } from '@heroicons/react/16/solid';
import { ChevronDownIcon, ChevronRightIcon } from '@heroicons/react/20/solid';
import { BookOpenIcon, DocumentTextIcon, VideoCameraIcon } from '@heroicons/react/24/outline';
import { CollapseProps } from 'antd';
import React from 'react';
import { useQueryClient } from 'react-query';

const sidebarItemOptions = {
  headerClass: 'p-0 py-3',
} as NonNullable<CollapseProps['items']>[number];

type Props = {
  sections: Section[];
};

const isDraftSection = (section: Section) => section.id === 'draft';

const isDraftLesson = (lesson: Lecture) => lesson.id === 'draft';

const LectureTitle = ({ lecture }: { lecture: Lecture }) => {
  if (isDraftLesson(lecture) && lecture.lectureType === LessonType.Video) {
    return (
      <Typography variant="labelMd" title="Tiêu đề bài học" className={'text-disabled_text'}>
        Tiêu đề bài học
      </Typography>
    );
  }

  if (isDraftLesson(lecture) && lecture.lectureType === LessonType.Test) {
    return (
      <Typography variant="labelMd" title="Tiêu đề bài ôn tập" className={'text-disabled_text'}>
        Tiêu đề bài ôn tập
      </Typography>
    );
  }

  return (
    <Typography variant="labelMd" title={lecture.lectureName} className={cn('truncate')}>
      {lecture.lectureName}
    </Typography>
  );
};

const SectionTitle = ({ section }: { section: Section }) => {
  const { parsedQueryParams } = useSafeSearchParams<{
    lessonId: string;
    sectionId: string;
    testId: string;
  }>();

  if (isDraftSection(section)) {
    return (
      <div className="flex items-center gap-2">
        <Icon icon={<DotIcon />} />
        <Typography variant="labelMd" title={section.sectionName} className={cn('text-disabled_text')}>
          Tiêu đề chương
        </Typography>
      </div>
    );
  }

  const isTestSection = section.sectionType === ChapterType.Test;
  const isTestSelected = section.test?.id === parsedQueryParams.testId;
  const isTestSectionSelected = isTestSection && isTestSelected;

  return (
    <div className="flex items-center">
      <Typography
        variant="labelMd"
        title={section.sectionName}
        className={cn('truncate', {
          'flex w-full items-center gap-2 rounded-lg px-2.5 py-3 hover:bg-neutral-100': isTestSection,
          'bg-neutral-100': isTestSectionSelected,
        })}
      >
        {section.sectionName}
      </Typography>
    </div>
  );
};

const getLectureSearchParams = ({ lecture, section }: { lecture: Lecture; section: Section }) => {
  if (lecture.lectureType === LessonType.Video) {
    return { sectionId: section.id, lessonId: lecture.id, testId: '' };
  }

  if (lecture.lectureType === LessonType.Test) {
    return { sectionId: section.id, lessonId: lecture.id, testId: lecture.test?.id };
  }

  return null;
};

const getLectureIcon = (lecture: Lecture) => {
  return lecture.lectureType === LessonType.Video ? <VideoCameraIcon /> : <DocumentTextIcon />;
};

export default function Sidebar(props: Props) {
  const { sections } = props;

  const { setSearchParams, parsedQueryParams } = useSafeSearchParams<{
    lessonId: string;
    sectionId: string;
    testId: string;
  }>();

  const { isClient } = useClient();

  const [sidebarExpanded, setSidebarExpanded] = React.useState(true);

  const queryClient = useQueryClient();

  const handleClickLecture = ({ lecture, section }: { lecture: Lecture; section: Section }) => {
    setSearchParams({ sectionId: '', lessonId: '', testId: '' });
    const searchParams = getLectureSearchParams({ lecture, section });
    if (!searchParams) return;

    setSearchParams(searchParams);
  };

  const showExpandIcon = (section: Section) => {
    if (isDraftSection(section)) return false;
    if (section.sectionType === ChapterType.Default) return true;
    return false;
  };

  const tests = sections.filter((section) => section.sectionType === ChapterType.Test);
  const chapters = sections.filter((section) => section.sectionType == ChapterType.Default);
  const filteredSections = [...chapters, ...tests];

  const handleBackToChapterList = () => {
    setSearchParams({ sectionId: '', lessonId: '', testId: '' });
    queryClient.invalidateQueries([QUERY_KEYS.COURSE_DETAIL]);
  };

  React.useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 1000) {
        setSidebarExpanded(false);
      }
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  const getItems = () => {
    return filteredSections.map((section) => ({
      ...sidebarItemOptions,
      showArrow: showExpandIcon(section),
      key: section.id,
      label: <SectionTitle section={section} />,
      onClick: () => {
        if (section.sectionType === ChapterType.Test && section.test) {
          setSearchParams({ sectionId: section.id, lessonId: '', testId: section.test.id });
        }
      },
      classNames: { body: cn('p-0') },
      children: (
        <div className="flex flex-col gap-2 p-0">
          {section.lectures.map((lecture) => {
            const isLectureSelected = lecture.id === parsedQueryParams.lessonId;
            const lectureIcon = getLectureIcon(lecture);

            return (
              <div
                key={lecture.id}
                onClick={() => handleClickLecture({ lecture, section })}
                className={cn(
                  'flex cursor-pointer items-center gap-2 rounded-lg px-2.5 py-3',
                  'hover:bg-neutral-100',
                  isLectureSelected && 'cursor-default rounded-lg bg-neutral-100',
                )}
              >
                <div className="flex items-center gap-2">
                  <Icon icon={<DotIcon />} className="flex size-5 items-center" />
                  {isDraftLesson(lecture) ? null : <Icon icon={lectureIcon} className="size-5" />}
                </div>

                <LectureTitle lecture={lecture} />
              </div>
            );
          })}
        </div>
      ),
    })) satisfies CollapseProps['items'];
  };

  if (!isClient) return <div className={cn('w-72')} />;

  return (
    <React.Fragment>
      <div className={cn('relative z-50 h-full transition-all duration-300', sidebarExpanded ? 'w-72' : 'w-[50px]')}>
        <Icon
          size="md"
          icon={
            sidebarExpanded ? (
              <ChevronDoubleLeftIcon className="text-secondary_text" />
            ) : (
              <ChevronDoubleRightIcon className="text-secondary_text" />
            )
          }
          className={cn(
            'absolute right-0 top-6 z-50 translate-x-2',
            'cursor-pointer rounded-lg border border-neutral-100 bg-white p-1 text-secondary_text',
          )}
          onClick={() => setSidebarExpanded((prev) => !prev)}
        />

        {sidebarExpanded ? (
          <div className="flex size-full flex-col gap-2 overflow-y-auto border-r border-neutral-200 p-4">
            <div
              className={cn(
                'flex w-fit items-center gap-2 rounded-lg bg-neutral-100 p-3 text-lg',
                sidebarExpanded && 'w-full',
                'cursor-pointer hover:bg-neutral-200',
              )}
              onClick={handleBackToChapterList}
            >
              <Icon icon={<BookOpenIcon />} className="size-5" />
              <Typography variant="labelMd">Chương bài học</Typography>
            </div>
            <div className="border-t border-neutral-200 p-2">
              <Typography variant="labelSm" className="text-secondary_text">
                NỘI DUNG
              </Typography>

              <div className="mt-2">
                <Collapse
                  ghost
                  defaultActiveKey={sections.map((section) => section.id)}
                  expandIcon={({ isActive }) => {
                    return <Icon size="sm" icon={isActive ? <ChevronDownIcon /> : <ChevronRightIcon />} />;
                  }}
                  items={getItems()}
                />
              </div>
            </div>
          </div>
        ) : null}
      </div>
    </React.Fragment>
  );
}
