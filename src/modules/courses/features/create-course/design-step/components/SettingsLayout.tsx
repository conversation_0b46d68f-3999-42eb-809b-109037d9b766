import { Typography } from '@/components/ui';
import React from 'react';

const LessonSettingsLayout = ({ children }: { children: React.ReactNode }) => {
  return (
    <React.Fragment>
      <div className="h-full min-h-fit w-1/3 min-w-72 max-w-[500px] rounded-lg border border-neutral-200 bg-white">
        <div className="border-b border-neutral-200 p-4">
          <Typography variant="labelLg" className="font-bold text-secondary_text">
            BẢNG TÙY CHỈNH
          </Typography>
        </div>

        {children}
      </div>
    </React.Fragment>
  );
};

export default LessonSettingsLayout;
