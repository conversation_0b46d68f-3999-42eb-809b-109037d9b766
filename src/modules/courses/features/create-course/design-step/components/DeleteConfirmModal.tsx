import { Button, Modal, Typography } from '@/components/ui';

const confirmationMessageConfig = {
  LESSON: {
    title: 'Xóa bài học?',
    description:
      'Bạn có chắc chắn muốn xoá bài ôn tập này ra khỏi danh sách Chương bài học? Toàn bộ dữ liệu sẽ không thể phục hồi lại sau khi đã xóa.',
  },
  QUIZ: {
    title: 'Xóa bài ôn tập?',
    description:
      'Bạn có chắc chắn muốn xoá bài ôn tập này ra khỏi danh sách bài ôn tập? Tất cả các câu hỏi trong bài ôn tập sẽ không thể phục hồi lại sau khi đã xóa.',
  },
  CHAPTER: {
    title: 'Xóa chương?',
    description:
      'Bạn có chắc chắn muốn xoá chương này ra khỏi danh sách Chương bài học? Tất cả các bài giảng video và bài ôn tập trong chương sẽ không thể phục hồi lại sau khi đã xóa.',
  },
  FINAL_TEST: {
    title: 'Xóa bài kiểm tra cuối khóa?',
    description:
      'Bạn có chắc chắn muốn xoá bài kiểm tra cuối khóa này ra khỏi danh sách chương bài học? Tất cả các câu hỏi trong bài kiểm tra cuối khóa sẽ không thể phục hồi lại sau khi đã xóa.',
  },
};

function DeleteConfirmModal({
  open,
  type,
  isDeleting,
  onCancel,
  onConfirm,
  onClose,
}: {
  open: boolean;
  type: 'CHAPTER' | 'QUIZ' | 'LESSON' | 'FINAL_TEST';
  isDeleting?: boolean;
  onCancel?: () => void;
  onConfirm?: () => void;
  onClose?: () => void;
}) {
  const { title, description } = confirmationMessageConfig[type];

  return (
    <Modal
      open={open}
      onClose={onClose}
      type="confirmation"
      title={title}
      footer={
        <div className="flex items-center justify-end gap-3 px-6 py-4">
          <Button variant="tertiary" onClick={onCancel}>
            Hủy
          </Button>
          <Button variant="error" onClick={onConfirm} loading={isDeleting}>
            Xóa
          </Button>
        </div>
      }
    >
      <div className="flex flex-col gap-4 px-6 py-4">
        <Typography variant="bodyLg">{description}</Typography>
        <Typography variant="bodyLg">Bạn có muốn tiếp tục?</Typography>
      </div>
    </Modal>
  );
}

export default DeleteConfirmModal;
