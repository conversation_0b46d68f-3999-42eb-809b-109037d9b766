'use client';

import { quillConfig } from '@/modules/courses/constants/quill-config.const';

type Props = {
  id: string;
};

const CustomToolbar = ({ id }: Props) => {
  return (
    <div id={id} className="ql-toolbar">
      {/* Font Size Dropdown */}
      <select className="ql-size">
        {quillConfig.fontSizes.map((size) => (
          <option key={size} value={size}>
            {size}
          </option>
        ))}
      </select>

      {/* Text Color Picker */}
      <select className="ql-color" />

      {/* Text Formatting Buttons */}
      <button className="ql-bold" />
      <button className="ql-italic" />
      <button className="ql-underline" />
      <button className="ql-strike" />

      {/* Text Alignment Buttons */}
      <button className="ql-align" value="" />
      <button className="ql-align" value="center" />
      <button className="ql-align" value="right" />

      {/* List Buttons */}
      <button className="ql-list" value="ordered" />
      <button className="ql-list" value="bullet" />

      {/* Content Insertion Buttons */}
      <button className="ql-image" />
      <button className="ql-link" />
    </div>
  );
};

export { CustomToolbar };
