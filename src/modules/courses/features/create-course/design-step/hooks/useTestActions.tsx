import { QUERY_KEYS } from '@/constants/query-keys';
import { useSafeSearchParams } from '@/hooks';
import { getTestByIdService, TestPayloadRequest, updateTestService } from '@/modules/courses/services/test.service';
import { useParams } from 'next/navigation';
import { useMutation, useQuery } from 'react-query';

const useTestActions = () => {
  const params = useParams<{ courseId: string }>();
  const { parsedQueryParams } = useSafeSearchParams<{ sectionId: string; testId: string; lessonId: string }>();
  const courseId = params.courseId;
  const { sectionId, testId } = parsedQueryParams;

  const { data: testDetailData } = useQuery({
    queryKey: [QUERY_KEYS.TEST_DETAIL, params.courseId, sectionId, testId],
    queryFn: () => {
      return getTestByIdService({ courseId, sectionId, testId });
    },
    suspense: true,
  });

  const updateTestMutation = useMutation({ mutationFn: updateTestService });

  const handleUpdateTest = (
    payload: TestPayloadRequest,
    mutateOptions?: { onSuccess?: () => void; onError?: () => void },
  ) => {
    const variables = { courseId: params.courseId, sectionId, testId, payload };
    updateTestMutation.mutate(variables, mutateOptions);
  };

  return {
    testDetail: testDetailData,
    onUpdateTest: handleUpdateTest,
  };
};

export default useTestActions;
