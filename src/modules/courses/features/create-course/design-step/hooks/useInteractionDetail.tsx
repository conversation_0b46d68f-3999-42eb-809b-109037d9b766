import { QUERY_KEYS } from '@/constants/query-keys';
import useSafeSearchParams from '@/hooks/useSafeSearchParams';
import { getInteractionDetailService } from '@/modules/courses/services/interaction.service';
import { Interaction, useLessonStore } from '@/z-store/lesson.store';
import { useParams } from 'next/navigation';
import { useEffect } from 'react';
import { useQuery } from 'react-query';

type UseInteractionDetailProps = {
  enabled?: boolean;
};

const useInteractionDetail = (props?: UseInteractionDetailProps) => {
  const { enabled = true } = props || {};

  const { courseId } = useParams<{ courseId: string }>();
  const { parsedQueryParams } = useSafeSearchParams<{ lessonId: string; sectionId: string }>();
  const { lessonId, sectionId } = parsedQueryParams;

  const settings = useLessonStore((state) => state.settings);
  const updateInteraction = useLessonStore((state) => state.updateInteraction);

  const { interactionId } = settings;

  const queryKey = [QUERY_KEYS.INTERACTION_DETAIL, courseId, sectionId, lessonId, interactionId];

  const query = useQuery({
    queryKey,
    queryFn: () =>
      getInteractionDetailService({
        courseId,
        sectionId,
        lectureId: lessonId,
        interactId: interactionId!,
      }),
    enabled: enabled && Boolean(courseId && sectionId && lessonId && interactionId),
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
  });

  // Update store when data is fetched
  useEffect(() => {
    if (query.data) {
      const transformedData = { ...query.data } as any;

      // Parse questionOptions if it's a JSON string
      if (transformedData.question?.questionOptions && typeof transformedData.question.questionOptions === 'string') {
        try {
          transformedData.question.questionOptions = JSON.parse(transformedData.question.questionOptions);
        } catch (error) {
          console.error('Failed to parse questionOptions:', error);
          transformedData.question.questionOptions = [];
        }
      }

      // Parse correctAnswer if it's a JSON string
      if (transformedData.question?.correctAnswer && typeof transformedData.question.correctAnswer === 'string') {
        try {
          transformedData.question.correctAnswer = JSON.parse(transformedData.question.correctAnswer);
        } catch (error) {
          console.error('Failed to parse correctAnswer:', error);
          transformedData.question.correctAnswer = [];
        }
      }

      const interactionData: Partial<Interaction> = {
        id: interactionId,
        ...transformedData,
      };
      updateInteraction(interactionData);
    }
  }, [query.data, interactionId, updateInteraction]);

  return {
    ...query,
    interactionData: query.data,
    isLoading: query.isLoading,
    isError: query.isError,
    error: query.error,
  };
};

export default useInteractionDetail;