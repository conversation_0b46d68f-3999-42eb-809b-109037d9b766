import { QUERY_KEYS } from '@/constants/query-keys';
import { useNotification } from '@/hooks';
import useSafeSearchParams from '@/hooks/useSafeSearchParams';
import { InteractionType } from '@/modules/courses/constants/course.const';
import { updateInteractionContentService } from '@/modules/courses/services/interaction.service';
import { QuestionInteractRequest } from '@/modules/courses/types/course-request.type';
import { useLessonStore } from '@/z-store/lesson.store';
import { useParams } from 'next/navigation';
import { useCallback } from 'react';
import { useMutation, useQueryClient } from 'react-query';

const useInteractionAutoSave = () => {
  const notification = useNotification();

  const queryClient = useQueryClient();

  const { courseId } = useParams<{ courseId: string }>();
  const { parsedQueryParams } = useSafeSearchParams<{ lessonId: string; sectionId: string }>();
  const { lessonId, sectionId } = parsedQueryParams;

  const { interaction, settings } = useLessonStore();
  const { interactionId } = settings;

  const updateMutation = useMutation({
    mutationFn: updateInteractionContentService,
    onSuccess: async () => {
      await queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.LECTURE_DETAIL] });
    },
    onError: () => {
      notification.error({ message: 'Lỗi khi lưu câu hỏi' });
    },
  });

  const transformToApiFormat = useCallback((data: typeof interaction): QuestionInteractRequest => {
    const toId = (v?: string | { id: string } | null) => (typeof v === 'string' ? v : (v?.id ?? null));
    const questionOptionsJson = data.question.questionOptions
      ? JSON.stringify(
          data.question.questionOptions.map((option) => ({
            option_index: option.option_index,
            option_name: option.option_name,
            option_thumbnail_image_file: toId(option.option_thumbnail_image_file),
          })),
        )
      : undefined;

    return {
      interact_type: InteractionType.QuickQuestion,
      questionTypeId: data.question.questionTypeId !== null ? data.question.questionTypeId : undefined,
      questionName: data.question.questionName || undefined,
      questionImageFile:
        data.question.questionImageFile === null ? null : (toId(data.question.questionImageFile) ?? undefined),
      videoFile: data.question.videoFile === null ? null : (toId(data.question.videoFile) ?? undefined),
      questionOptions: questionOptionsJson,
      questionAnswers: data.question.questionAnswers || undefined,
      correctAnswer: data.question.correctAnswer ? JSON.stringify(data.question.correctAnswer) : undefined,
      questionRequired: data.question.questionRequired ?? undefined,
      questionDuration: data.question.questionDuration || undefined,
      replyRightAnswer: data.question.replyRightAnswer || undefined,
      replyWrongAnswer: data.question.replyWrongAnswer || undefined,
      showResponse: data.question.showResponse ?? undefined,
      backgroundColor: data.question.backgroundColor || undefined,
    };
  }, []);

  const saveInteractionContent = useCallback(
    (overrideData?: any) => {
      if (!courseId || !sectionId || !lessonId || !interactionId) {
        return;
      }

      // Get the most current interaction state directly from the store
      const currentInteraction = useLessonStore.getState().interaction;

      // Use override data if provided, otherwise use current interaction state
      const dataToSave = overrideData ? { ...currentInteraction, ...overrideData } : currentInteraction;
      const content = transformToApiFormat(dataToSave);

      updateMutation.mutate({
        courseId,
        sectionId,
        lectureId: lessonId,
        interactId: interactionId,
        content,
      });
    },
    [courseId, sectionId, lessonId, interactionId, transformToApiFormat, updateMutation],
  );

  const saveWithCorrectAnswer = useCallback(
    (correctAnswer: number[]) => {
      const currentInteraction = useLessonStore.getState().interaction;
      const updatedInteraction = {
        ...currentInteraction,
        question: {
          ...currentInteraction.question,
          correctAnswer,
        },
      };

      saveInteractionContent(updatedInteraction);
    },
    [saveInteractionContent],
  );

  const saveWithQuestionTypeId = useCallback(
    (questionTypeId: number) => {
      const currentInteraction = useLessonStore.getState().interaction;
      const updatedInteraction = {
        ...currentInteraction,
        question: {
          ...currentInteraction.question,
          questionTypeId: questionTypeId || 0,
        },
      };

      saveInteractionContent(updatedInteraction);
    },
    [saveInteractionContent],
  );

  const saveWithQuestionUpdates = useCallback(
    (questionUpdates: any) => {
      const currentInteraction = useLessonStore.getState().interaction;
      const updatedInteraction = {
        ...currentInteraction,
        question: {
          ...currentInteraction.question,
          ...questionUpdates,
        },
      };

      saveInteractionContent(updatedInteraction);
    },
    [saveInteractionContent],
  );

  return {
    saveInteractionContent,
    saveWithCorrectAnswer,
    saveWithQuestionTypeId,
    saveWithQuestionUpdates,
    isSaving: updateMutation.isLoading,
  };
};

export default useInteractionAutoSave;
