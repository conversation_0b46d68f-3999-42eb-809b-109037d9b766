import { QUERY_KEYS } from '@/constants/query-keys';
import { useNotification } from '@/hooks';
import useSafeSearchParams from '@/hooks/useSafeSearchParams';
import { InteractionType } from '@/modules/courses/constants/course.const';
import {
  createInteractionService,
  deleteInteractionService,
  updateInteractionContentService,
  updateInteractionService,
} from '@/modules/courses/services/interaction.service';
import { ExploreInteractionRequest, QuestionInteractRequest } from '@/modules/courses/types/course-request.type';
import { useLessonStore } from '@/z-store/lesson.store';
import { useParams } from 'next/navigation';
import { useMutation, useQueryClient } from 'react-query';

const useInteractionActions = () => {
  const notification = useNotification();

  const { courseId } = useParams<{ courseId: string }>();
  const { parsedQueryParams } = useSafeSearchParams<{ lessonId: string; sectionId: string }>();
  const { lessonId, sectionId } = parsedQueryParams;

  const queryClient = useQueryClient();

  const resetSettings = useLessonStore((state) => state.resetSettings);

  const createInteractionMutation = useMutation({
    mutationFn: createInteractionService,
    onSuccess: () => {
      notification.success({ message: 'Tạo tương tác thành công' });
    },
    onError: () => {
      notification.error({ message: 'Tạo tương tác thất bại' });
    },
  });

  const updateInteractionMutation = useMutation({
    mutationFn: updateInteractionService,
  });

  const updateInteractionContentMutation = useMutation({
    mutationFn: updateInteractionContentService,
  });

  const deleteInteractionMutation = useMutation({
    mutationFn: deleteInteractionService,
  });

  const clearCache = () => {
    queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.LECTURE_DETAIL, courseId, sectionId, lessonId] });
  };

  const handleCreateInteraction = (payload: {
    interactionType: InteractionType;
    startAt: number;
    duration?: number;
  }) => {
    const { duration = 0, interactionType, startAt } = payload;

    const variables = { duration, courseId, sectionId, lectureId: lessonId, startAt, interactionType };
    createInteractionMutation.mutate(variables, {
      onSuccess: async () => {
        clearCache();
      },
      onError: () => {
        notification.error({ message: 'Tạo tương tác thất bại' });
      },
    });
  };

  const handleUpdateInteraction = (payload: { interactId: string; startAt: number; duration?: number }) => {
    const { duration = 0, interactId, startAt } = payload;

    const requests = { courseId, sectionId, lectureId: lessonId, interactId, startAt, duration };
    updateInteractionMutation.mutate(requests, {
      onSuccess: async () => {
        clearCache();
      },
    });
  };

  const handleUpdateInteractionContent = (payload: {
    interactId: string;
    content: QuestionInteractRequest | ExploreInteractionRequest;
  }) => {
    const variables = {
      courseId,
      sectionId,
      lectureId: lessonId,
      interactId: payload.interactId,
      content: payload.content,
    };

    updateInteractionContentMutation.mutate(variables, {
      onSuccess: () => {
        clearCache();
      },
    });
  };

  const handleDeleteInteraction = (payload: { interactId: string }) => {
    const { interactId } = payload;

    const requests = { courseId, sectionId, lectureId: lessonId, interactId };
    deleteInteractionMutation.mutate(requests, {
      onSuccess: () => {
        clearCache();
        resetSettings();
      },
    });
  };

  return {
    isCreating: createInteractionMutation.isLoading,
    isUpdating: updateInteractionMutation.isLoading,
    isDeleting: deleteInteractionMutation.isLoading,
    isContentUpdating: updateInteractionContentMutation.isLoading,

    onCreateInteraction: handleCreateInteraction,
    onUpdateInteraction: handleUpdateInteraction,
    onDeleteInteraction: handleDeleteInteraction,
    onUpdateInteractionContent: handleUpdateInteractionContent,
  };
};

export default useInteractionActions;
