import { QUERY_KEYS } from '@/constants/query-keys';
import { useNotification } from '@/hooks';
import {
  createSectionService,
  deleteSectionService,
  editSectionService,
  swapSectionService,
} from '@/modules/courses/services/course.service';
import { SectionCreateRequest } from '@/modules/courses/types/course-request.type';
import { useMutation, useQueryClient } from 'react-query';

const useSectionActions = () => {
  const queryClient = useQueryClient();
  const notification = useNotification();

  const editSectionMutation = useMutation({
    mutationFn: editSectionService,
    onSuccess: () => {
      notification.success({ message: 'S<PERSON><PERSON> chương thành công' });
    },
    onError: () => {
      notification.error({ message: 'Sửa chương thất bại' });
    },
  });

  const createSectionMutation = useMutation({
    mutationFn: createSectionService,
    onSuccess: () => {
      notification.success({ message: 'Tạo chương thành công' });
    },
    onError: () => {
      notification.error({ message: '<PERSON><PERSON><PERSON> chương thất bại' });
    },
  });

  const deleteSectionMutation = useMutation({
    mutationFn: deleteSectionService,
    onSuccess: () => {
      notification.success({ message: 'Xóa chương thành công' });
    },
    onError: () => {
      notification.error({ message: 'Xóa chương thất bại' });
    },
  });

  const swapSectionMutation = useMutation({
    mutationFn: swapSectionService,
  });

  const handleDeleteSection = (payload: { courseId: string; sectionId: string }) => {
    deleteSectionMutation.mutate(payload, {
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.COURSE_DETAIL] });
      },
    });
  };

  const handleCreateSection = (payload: SectionCreateRequest) => {
    createSectionMutation.mutate(payload, {
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: QUERY_KEYS.COURSE_DETAIL });
      },
    });
  };

  const handleEditSection = (payload: { sectionName: string; sectionId: string; courseId: string }) => {
    editSectionMutation.mutate(payload, {
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: QUERY_KEYS.COURSE_DETAIL });
      },
    });
  };

  const handleSwapSection = (payload: {
    courseId: string;
    sectionId: string;
    sourceIndex: number;
    destinationIndex: number;
  }) => {
    swapSectionMutation.mutate(payload, {
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: QUERY_KEYS.COURSE_DETAIL });
      },
    });
  };

  return {
    onDeleteSection: handleDeleteSection,
    onCreateSection: handleCreateSection,
    onEditSection: handleEditSection,
    onSwapSection: handleSwapSection,
  };
};

export default useSectionActions;
