'use client';

import { QUERY_KEYS } from '@/constants/query-keys';
import useSafeSearchParams from '@/hooks/useSafeSearchParams';
import { updateFileLessonService } from '@/modules/courses/services/course.service';
import { useParams } from 'next/navigation';
import { useMutation, useQueryClient } from 'react-query';

export const useUpdateVideoFile = () => {
  const { mutate: updateFileLesson, isLoading } = useMutation({
    mutationFn: updateFileLessonService,
  });

  const queryClient = useQueryClient();

  const params = useParams<{ courseId: string }>();

  const { parsedQueryParams } = useSafeSearchParams<{ sectionId: string; lessonId: string }>();
  const { sectionId, lessonId } = parsedQueryParams;

  const handleUpdateVideoFile = (fileId: string) => {
    const variables = {
      courseId: params.courseId,
      sectionId,
      lectureId: lessonId,
      payload: { fileId: fileId || undefined },
    };

    updateFileLesson(variables, {
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.COURSE_DETAIL, params.courseId] });
        queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.LECTURE_DETAIL, params.courseId, sectionId, lessonId] });
      },
    });
  };

  return {
    onUpdateVideoFile: handleUpdateVideoFile,
    isLoading,
  };
};
