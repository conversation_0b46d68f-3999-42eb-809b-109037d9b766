import { LectureInteract } from '@/modules/courses/types/interaction.type';
import React from 'react';

const useVideoInteractions = ({
  interactions,
  currentTime,
  onPause,
}: {
  interactions: LectureInteract[];
  currentTime: number;
  onPause: () => void;
}) => {
  const [selectedInteraction, setSelectedInteraction] = React.useState<LectureInteract | null>(null);
  const [isShowInteraction, setIsShowInteraction] = React.useState<boolean>(false);
  const [shownInteractions, setShownInteractions] = React.useState<Set<number>>(new Set());

  const onCloseInteraction = () => {
    if (selectedInteraction) {
      setShownInteractions((prev) => new Set([...prev, selectedInteraction.startAt]));
    }
    setSelectedInteraction(null);
    setIsShowInteraction(false);
  };

  React.useEffect(() => {
    const interaction = interactions.find((interaction) => {
      const interactionSecond = interaction.startAt;

      const rangeTime = currentTime >= interactionSecond && currentTime < interactionSecond + 1;

      return rangeTime && !shownInteractions.has(interactionSecond);
    });

    if (interaction) {
      setSelectedInteraction(interaction);
      setIsShowInteraction(true);
      onPause();
    }
  }, [currentTime, shownInteractions]);

  return {
    selectedInteraction,
    isShowInteraction,
    onCloseInteraction,
    setSelectedInteraction,
  };
};

export default useVideoInteractions;
