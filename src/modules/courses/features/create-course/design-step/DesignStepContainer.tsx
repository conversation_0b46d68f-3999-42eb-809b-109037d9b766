'use client';

import { routePaths } from '@/config';
import { QUERY_KEYS } from '@/constants/query-keys';
import { usePreventHydration } from '@/hooks';
import useSafeSearchParams from '@/hooks/useSafeSearchParams';
import FinalTestContainer from '@/modules/courses/features/create-course/design-step/final-test/FinalTestContainer';
import { getCourseByIdService } from '@/modules/courses/services/course.service';
import { CourseInfo, Section } from '@/modules/courses/types/course.type';
import { formatApiUrl } from '@/utils/url.util';
import { useRouter } from 'next-nprogress-bar';
import dynamic from 'next/dynamic';
import { useParams } from 'next/navigation';
import React from 'react';
import { useQuery } from 'react-query';
import CreateCourseHeader from '../components/header/Header';
import ChaptersSkeleton from './chapters-container/ChaptersSkeleton';
import Sidebar from './components/Sidebar';
import LessonSkeleton from './lesson-detail/LessonSkeleton';

const LessonContainer = dynamic(() => import('./lesson-detail/LessonContainer'), {
  ssr: false,
  loading: () => <LessonSkeleton />,
});

const ChaptersContainer = dynamic(() => import('./chapters-container/ChaptersContainer'), {
  ssr: false,
  loading: () => (
    <div className="flex items-center justify-center px-32">
      <ChaptersSkeleton />
    </div>
  ),
});

const QuizContainer = dynamic(() => import('./quiz/QuizContainer'), {
  ssr: false,
  loading: () => <LessonSkeleton />,
});

type Props = {
  courseDetail: CourseInfo;
};

const useChapters = ({ courseDetail }: { courseDetail: CourseInfo | null }) => {
  const [sections, setSections] = React.useState<Section[]>(courseDetail?.sections || []);

  React.useEffect(() => {
    setSections(courseDetail?.sections || []);
  }, [courseDetail]);

  return { sections, setSections };
};

export const useCourseDetail = ({ courseDetail }: { courseDetail: CourseInfo | null }) => {
  const params = useParams<{ courseId: string }>();

  const { data: courseDetailData } = useQuery({
    queryKey: [QUERY_KEYS.COURSE_DETAIL, params.courseId],
    enabled: !!courseDetail,
    initialData: courseDetail,
    queryFn: () => getCourseByIdService(params.courseId),
  });

  return courseDetailData;
};

function DesignStepContainer(props: Props) {
  const { courseDetail } = props;
  const router = useRouter();
  const params = useParams<{ courseId: string }>();
  const { parsedQueryParams } = useSafeSearchParams<{ lessonId: string; sectionId: string; testId: string }>();
  const { lessonId, sectionId, testId } = parsedQueryParams;

  const courseDetailData = useCourseDetail({ courseDetail });
  const { sections, setSections } = useChapters({ courseDetail: courseDetailData! });

  usePreventHydration();

  const handleNextStep = () => {
    router.push(routePaths.course.publish.replace(':courseId', params.courseId));
  };

  const handleBackStep = () => {
    const url = formatApiUrl(routePaths.course.editInfo, { courseId: params.courseId });
    router.push(url);
  };

  const renderContent = () => {
    const shouldShowQuizDetail = sectionId && lessonId && testId;
    if (shouldShowQuizDetail) {
      return <QuizContainer key={testId} />;
    }

    const shouldShowFinalTestDetail = sectionId && testId;
    if (shouldShowFinalTestDetail) {
      return <FinalTestContainer key={testId} />;
    }

    const shouldShowLessonDetail = sectionId && lessonId;
    if (shouldShowLessonDetail) {
      return <LessonContainer />;
    }

    return (
      <ChaptersContainer
        key={courseDetailData?.id}
        courseDetail={courseDetailData!}
        sections={sections}
        setSections={setSections}
      />
    );
  };

  return (
    <div className="flex h-screen flex-col overflow-hidden">
      <CreateCourseHeader
        stepIndex={1}
        onNext={handleNextStep}
        onBack={handleBackStep}
        slotProps={{
          setupItem: { status: 'finish', onClick: handleBackStep },
          designItem: { status: 'process', onClick: () => {} },
          publishItem: { status: 'wait', onClick: handleNextStep },
        }}
      />

      <main className="flex h-[calc(100vh-72px)] border-t border-neutral-200">
        <div className="h-full">
          <Sidebar sections={sections || []} />
        </div>

        <div className="size-full bg-neutral-50">{renderContent()}</div>
      </main>
    </div>
  );
}

export default DesignStepContainer;
