import { Button, Switch, Typography } from '@/components/ui';
import { cn } from '@/lib/utils';
import { PlusCircleIcon, TrashIcon } from '@heroicons/react/24/outline';
import { Controller, useWatch } from 'react-hook-form';
import { useTypedFormContext } from './useTypedFormContext';

type QuestionFormActionsProps = {
  onAddQuestion?: () => void;
  onDeleteQuestion?: () => void;
  questionIndex: number;
  disabledDeleteButton: boolean;
};

const QuestionFormActions = (props: QuestionFormActionsProps) => {
  const { questionIndex, onAddQuestion, onDeleteQuestion } = props;
  const { control, setValue } = useTypedFormContext();

  const minQuestions = useWatch({ control, name: 'minQuestions' }) as number;
  const maxQuestions = useWatch({ control, name: 'maxQuestions' }) as number;
  const questions = useWatch({ control, name: 'questions' });

  const isDisabledAddQuestion = questions.length >= maxQuestions;
  const isDisabledDeleteButton = questions.length <= minQuestions;

  return (
    <div className="flex flex-wrap justify-end gap-4">
      <Button
        variant="ghost"
        size="large"
        onClick={onAddQuestion}
        disabled={isDisabledAddQuestion}
        className={cn(
          'text-primary_text',
          'hover:bg-neutral-50 hover:text-primary_text',
          'active:bg-neutral-50 active:text-primary_text',
        )}
        startIcon={<PlusCircleIcon />}
      >
        Thêm câu hỏi
      </Button>

      <Controller
        name={`questions.${questionIndex}.isMultipleChoice`}
        control={control}
        render={({ field }) => {
          const onChange = () => {
            setValue(`questions.${questionIndex}.questionCorrectAnswer`, [], { shouldDirty: true });
            field.onChange(!field.value);
          };

          return (
            <div onClick={onChange} className="flex cursor-pointer items-center gap-2">
              <Switch {...field} />
              <Typography>Nhiều câu trả lời đúng</Typography>
            </div>
          );
        }}
      />

      <Button
        variant="ghost"
        size="large"
        startIcon={<TrashIcon />}
        className={cn(
          'text-primary_text',
          'hover:bg-neutral-50 hover:text-primary_text',
          'active:bg-neutral-50 active:text-primary_text',
        )}
        onClick={onDeleteQuestion}
        disabled={isDisabledDeleteButton}
      >
        <Typography>Xóa</Typography>
      </Button>
    </div>
  );
};

export default QuestionFormActions;
