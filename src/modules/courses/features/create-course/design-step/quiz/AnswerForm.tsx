'use client';

import { Button, Typography } from '@/components/ui';
import { PlusIcon } from '@heroicons/react/24/outline';

import DraggableList from '@/modules/courses/components/draggable-list/DraggableList';
import AnswerFormItem from '@/modules/courses/features/create-course/design-step/quiz/AnswerFormItem';
import React from 'react';
import { useFieldArray, useWatch } from 'react-hook-form';
import { useTypedFormContext } from './useTypedFormContext';

interface AnswerFormProps {
  questionIndex: number;
}

const MAX_ANSWERS = 5;

const useAnswerForm = ({ questionIndex }: { questionIndex: number }) => {
  const { control, trigger, setValue } = useTypedFormContext();

  const {
    fields: answers,
    append: appendAnswer,
    remove: removeAnswer,
    move: moveAnswer,
  } = useFieldArray({
    control,
    name: `questions.${questionIndex}.questionAnswers`,
  });

  const isMultiChoiceWatched = useWatch({ control, name: `questions.${questionIndex}.isMultipleChoice` });
  const correctAnswersWatched = useWatch({ name: `questions.${questionIndex}.questionCorrectAnswer`, control }) || [];

  const isDisabledAddAnswer = answers.length >= MAX_ANSWERS;

  const handleAnswerSelection = (index: number, isSelected: boolean) => {
    if (isMultiChoiceWatched) {
      const newCorrectAnswers = isSelected
        ? [...correctAnswersWatched, index]
        : correctAnswersWatched.filter((answerIndex) => answerIndex !== index);

      setValue(`questions.${questionIndex}.questionCorrectAnswer`, newCorrectAnswers, { shouldDirty: true });
      trigger(`questions.${questionIndex}.questionCorrectAnswer`);
      return;
    }

    setValue(`questions.${questionIndex}.questionCorrectAnswer`, [index], { shouldDirty: true });
    trigger(`questions.${questionIndex}.questionCorrectAnswer`);
  };

  const handleDeleteAnswer = (index: number, answerId: string) => {
    const answerPosition = answers.findIndex((answer) => answer.id === answerId);
    if (answerPosition === -1) return;

    removeAnswer(answerPosition);

    const remainingAnswers = answers.filter((_, idx) => idx !== answerPosition);
    remainingAnswers.forEach((_, idx) => {
      setValue(`questions.${questionIndex}.questionAnswers.${idx}.answerIndex`, idx);
    });

    const updatedCorrectAnswers = correctAnswersWatched
      .filter((answerIndex) => answerIndex !== index)
      .map((answerIndex) => (answerIndex > index ? answerIndex - 1 : answerIndex));

    setValue(`questions.${questionIndex}.questionCorrectAnswer`, updatedCorrectAnswers, { shouldDirty: true });
    trigger(`questions.${questionIndex}.questionCorrectAnswer`);
  };

  const handleSwapAnswer = (sourceIndex: number, destinationIndex: number) => {
    moveAnswer(sourceIndex, destinationIndex);

    answers.forEach((_, idx) => {
      setValue(`questions.${questionIndex}.questionAnswers.${idx}.answerIndex`, idx);
    });

    const updatedCorrectAnswers = correctAnswersWatched.map((answerIndex) => {
      if (answerIndex === sourceIndex) return destinationIndex;
      if (answerIndex === destinationIndex) return sourceIndex;
      return answerIndex;
    });

    setValue(`questions.${questionIndex}.questionCorrectAnswer`, updatedCorrectAnswers, { shouldDirty: true });
    trigger(`questions.${questionIndex}.questionCorrectAnswer`);
  };

  return {
    answers,
    isDisabledAddAnswer,
    onAddAnswer: appendAnswer,
    onDeleteAnswer: handleDeleteAnswer,
    onSwapAnswer: handleSwapAnswer,
    onSelectAnswer: handleAnswerSelection,
  };
};

const AnswerForm = (props: AnswerFormProps) => {
  const { questionIndex } = props;

  const { answers, isDisabledAddAnswer, onAddAnswer, onSelectAnswer, onDeleteAnswer, onSwapAnswer } = useAnswerForm({
    questionIndex,
  });

  const { formState } = useTypedFormContext();
  const correctAnswerError = formState.errors.questions?.[questionIndex]?.questionCorrectAnswer;

  return (
    <React.Fragment>
      <div className="flex flex-1 flex-col">
        <div className="flex-1 space-y-3 overflow-y-auto">
          <DraggableList
            onDragEnd={(result) => {
              if (!result.destination) return;
              onSwapAnswer(result.source.index, result.destination.index);
            }}
            items={answers}
            droppableId="answers"
            renderItem={({ provided, snapshot, item }) => {
              return (
                <div
                  ref={provided.innerRef}
                  {...provided.draggableProps}
                  className={`rounded-lg py-3 ${snapshot.isDragging ? 'opacity-50' : 'bg-white'}`}
                  style={provided.draggableProps.style}
                  {...provided.dragHandleProps}
                  key={item.id}
                >
                  <AnswerFormItem
                    answerIndex={item.answerIndex}
                    questionIndex={questionIndex}
                    onSelectAnswer={(isSelected) => onSelectAnswer(item.answerIndex, isSelected)}
                    onDeleteAnswer={() => onDeleteAnswer(item.answerIndex, item.id)}
                  />
                </div>
              );
            }}
          />

          <div className="flex flex-col items-start gap-4 px-6">
            <Button
              variant="ghost"
              onClick={() => {
                const defaultValue = {
                  answerName: '',
                  answerThumbnailImage: null,
                  answerIndex: answers.length,
                };
                onAddAnswer(defaultValue);
              }}
              disabled={isDisabledAddAnswer}
              startIcon={<PlusIcon />}
            >
              Thêm đáp án
            </Button>

            {correctAnswerError && (
              <Typography variant="labelLg" className="text-red-500">
                {correctAnswerError.message}
              </Typography>
            )}
          </div>
        </div>
      </div>
    </React.Fragment>
  );
};

export default AnswerForm;
