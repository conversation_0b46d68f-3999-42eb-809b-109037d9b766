import { QuestionPayloadRequest, TestPayloadRequest } from '@/modules/courses/services/test.service';
import { Question, Test } from '@/modules/courses/types/test.type';
import { uniqueId } from 'lodash-es';
import * as zod from 'zod';
import { QuizFormData, QuizQuestionFormData } from './quiz-form.type';

export const fileSchema = zod
  .object({
    fileUrl: zod.string(),
    fileId: zod.string(),
  })
  .optional()
  .nullable();

export const quizSchema = zod.object({
  questions: zod.array(
    zod.object({
      id: zod.string(),
      isMultipleChoice: zod.boolean(),
      questionName: zod
        .string()
        .min(1, 'Câu hỏi không được để trống')
        .max(200, 'Tên câu hỏi vượt quá giới hạn cho phép. Độ dài tối đa là 200 ký tự.'),
      questionThumbnailImage: fileSchema,
      questionVideoUrl: fileSchema,
      questionCorrectAnswer: zod.array(zod.number()).min(1, '<PERSON>ui lòng chọn đáp án đúng cho câu hỏi!'),
      questionAnswers: zod
        .array(
          zod.object({
            answerIndex: zod.number(),
            answerName: zod
              .string()
              .min(1, 'Tên đáp án không được để trống')
              .refine((value) => {
                const HTML_TAG_REGEX = /<[^>]*>/g;
                const content = value.replace(HTML_TAG_REGEX, '').trim();
                return content.length;
              }, 'Đáp án không được để trống')
              .max(200, 'Tên đáp án vượt quá giới hạn cho phép. Độ dài tối đa là 200 ký tự.'),
            answerThumbnailImage: fileSchema,
          }),
        )
        .min(2, 'Phải có ít nhất 2 đáp án'),
    }),
  ),
  minQuestions: zod.number().optional(),
  maxQuestions: zod.number().optional(),
});

export const mapQuestionToFormData = (question: Question): QuizQuestionFormData => ({
  id: question.id,
  isMultipleChoice: question.questionTypeId === 1,
  questionName: question.questionName ?? '',
  questionThumbnailImage: question.questionImageFile && {
    fileId: question.questionImageFile.id,
    fileUrl: question.questionImageFile.fileUrl,
  },
  questionVideoUrl: question.videoFile && {
    fileId: question.videoFile.id,
    fileUrl: question.videoFile.fileUrl,
  },
  questionCorrectAnswer: question.correctAnswer ?? [],
  questionAnswers: question.questionOptions.map((option) => ({
    answerIndex: option.optionIndex,
    answerName: option.optionName ?? '',
    answerThumbnailImage: option.optionThumbnailImageFile && {
      fileId: option.optionThumbnailImageFile.id,
      fileUrl: option.optionThumbnailImageFile.fileUrl,
    },
  })),
});

export const getQuizDefaultValues = (testDetail: Test) => {
  if (!testDetail) {
    return {
      questions: [],
      minQuestions: 1,
      maxQuestions: 5,
    } satisfies QuizFormData;
  }

  return {
    questions: testDetail.questions.map(mapQuestionToFormData),
    minQuestions: 1,
    maxQuestions: 5,
  } satisfies QuizFormData;
};

export const mapQuestionToPayload = (question: QuizQuestionFormData, index: number): QuestionPayloadRequest => ({
  question_name: question.questionName ?? '',
  question_image_file: question.questionThumbnailImage?.fileId ?? '',
  video_file: question.questionVideoUrl?.fileId,
  question_options: question.questionAnswers.map((answer, idx) => ({
    option_index: idx,
    option_name: answer.answerName ?? '',
    option_thumbnail_image_file: answer.answerThumbnailImage?.fileId ?? '',
  })),
  correct_answer: question.questionCorrectAnswer.map(Number),
  sort_index: index + 1,
});

export const getQuizPayloadRequest = (values: QuizFormData, testDetail: Test, lectureId: string) => {
  return {
    test_type: 'QUIZ',
    content: {
      lecture_id: lectureId,
      test_name: testDetail.testName,
      has_limit_time: 0,
      limit_time: 0,
      questions: values.questions.map(mapQuestionToPayload),
    },
  } satisfies TestPayloadRequest;
};

export const questionDefaultValue = {
  questionThumbnailImage: null,
  questionVideoUrl: null,
  questionName: '',
  questionAnswers: [
    { answerName: '', answerThumbnailImage: null, answerIndex: 0 },
    { answerName: '', answerThumbnailImage: null, answerIndex: 1 },
  ],
  isMultipleChoice: false,
  questionCorrectAnswer: [],
  id: uniqueId(),
} satisfies QuizQuestionFormData;
