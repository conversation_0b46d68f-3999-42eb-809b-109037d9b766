import { Button, Typography } from '@/components/ui';
import useLessonDetailActions from '@/modules/courses/features/create-course/design-step/hooks/useLessonDetailActions';
import { useTypedFormContext } from '@/modules/courses/features/create-course/design-step/quiz/useTypedFormContext';

const QuizHeader = () => {
  const { lessonDetail } = useLessonDetailActions();
  const { formState } = useTypedFormContext();

  const isDisabledSaveButton = formState.isSubmitting || !formState.isDirty || !formState.isValid;

  return (
    <div className="flex w-full justify-between">
      <div className="flex w-1/3 max-w-full flex-col gap-2">
        <Typography variant="labelMd">{lessonDetail?.section.sectionName}</Typography>
        <Typography variant="headlineSm">{lessonDetail?.lectureName}</Typography>
      </div>
      <div className="flex items-end">
        <div>
          <Button
            variant="primary"
            loading={formState.isSubmitting}
            size="large"
            htmlType="submit"
            // disabled={isDisabledSaveButton}
          >
            Lưu bài ôn tập
          </Button>
        </div>
      </div>
    </div>
  );
};

export default QuizHeader;
