import { Typography } from '@/components/ui';
import { cn } from '@/lib/utils';
import LessonSettingsLayout from '@/modules/courses/features/create-course/design-step/components/SettingsLayout';
import { useTypedFormContext } from '@/modules/courses/features/create-course/design-step/quiz/useTypedFormContext';
import { ClockIcon } from '@heroicons/react/24/outline';
import { useWatch } from 'react-hook-form';
import { MAX_QUESTIONS } from './QuestionForm';

function QuizSettings() {
  const { control } = useTypedFormContext();
  const questionsWatched = useWatch({ control, name: 'questions' });

  return (
    <LessonSettingsLayout>
      <div className="flex flex-col gap-8 p-4">
        <div className="flex justify-between">
          <Typography variant="labelMd">Tổng số câu hỏi (tối đa {MAX_QUESTIONS} câu hỏi):</Typography>
          <Typography variant="labelMd">{questionsWatched?.length}</Typography>
        </div>
        <div className="flex flex-col gap-2">
          <div className="flex gap-0.5">
            <Typography variant="labelMd">Giới hạn thời gian làm bài tập</Typography>
            <div className="text-red-500">*</div>
          </div>

          <div
            className={cn(
              'flex w-full items-center justify-between',
              'rounded-lg border border-neutral-200 bg-neutral-100 px-4 py-3',
            )}
          >
            <Typography className="text-disabled_text">Không giới hạn</Typography>
            <ClockIcon className="size-6 text-disabled_text" />
          </div>
        </div>
      </div>
    </LessonSettingsLayout>
  );
}

export default QuizSettings;
