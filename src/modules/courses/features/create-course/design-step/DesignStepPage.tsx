import { getCourseByIdService } from '../../../services/course.service';
import DesignStepContainer from './DesignStepContainer';

async function DesignStepPage({ params }: { params: { courseId: string } }) {
  const courseDetail = await getCourseByIdService(params.courseId);

  if (!courseDetail) {
    return <div>Không tìm thấy khóa học</div>;
  }

  return <DesignStepContainer courseDetail={courseDetail} />;
}

export default DesignStepPage;
