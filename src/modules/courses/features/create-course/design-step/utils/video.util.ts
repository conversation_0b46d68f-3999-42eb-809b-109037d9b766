export const calculateDurationAfterZoom = ({
  duration,
  zoomLevel,
  cursorPosition,
  currentTime,
}: {
  duration: number;
  zoomLevel: number;
  cursorPosition: number;
  currentTime?: number;
}) => {
  if (zoomLevel <= 0) {
    return {
      rulerEnd: duration,
      rulerStart: 0,
      displayedDuration: duration,
    };
  }

  const tenMinutes = 600;
  const zoomFactor = Math.min(zoomLevel / 100, 1);

  const maxDisplayDuration = duration;
  const minDisplayDuration = tenMinutes;
  const displayedDuration = maxDisplayDuration - zoomFactor * (maxDisplayDuration - minDisplayDuration);

  const indicatorTime = currentTime ?? calculateTime({ position: cursorPosition, duration });

  const halfDuration = displayedDuration / 2;
  const centeredStart = indicatorTime - halfDuration;
  const centeredEnd = indicatorTime + halfDuration;

  const isViewportStartBeforeVideoStart = centeredStart < 0;
  if (isViewportStartBeforeVideoStart) {
    return {
      rulerStart: 0,
      rulerEnd: Math.min(displayedDuration, duration),
      displayedDuration: Math.min(displayedDuration, duration),
    };
  }

  const isViewportEndAfterVideoEnd = centeredEnd > duration;
  if (isViewportEndAfterVideoEnd) {
    return {
      rulerStart: Math.max(0, duration - displayedDuration),
      rulerEnd: duration,
      displayedDuration: Math.min(displayedDuration, duration),
    };
  }

  return {
    rulerStart: centeredStart,
    rulerEnd: centeredEnd,
    displayedDuration,
  };
};

export const calculateRelativePosition = ({
  absoluteTime,
  rulerStart,
  displayedDuration,
}: {
  absoluteTime: number;
  rulerStart: number;
  displayedDuration: number;
}) => {
  const relativeTime = absoluteTime - rulerStart;
  return Math.max(0, Math.min(100, (relativeTime / displayedDuration) * 100));
};

export const calculateAbsoluteTime = ({
  relativePosition,
  rulerStart,
  displayedDuration,
}: {
  relativePosition: number;
  rulerStart: number;
  displayedDuration: number;
}) => {
  const relativeTime = (relativePosition / 100) * displayedDuration;
  return rulerStart + relativeTime;
};

export const getTimeAfterRulerChange = ({
  event,
  calculatedDuration,
}: {
  event: React.MouseEvent<HTMLDivElement>;
  calculatedDuration: number;
}) => {
  const rect = event.currentTarget.getBoundingClientRect();
  const clickX = event.clientX - rect.left;
  const rulerWidth = rect.width;

  const percentage = Math.max(0, Math.min(1, clickX / rulerWidth));
  const timeInSeconds = percentage * calculatedDuration;

  return timeInSeconds;
};

export const calculateViewportScroll = ({
  currentTime,
  rulerStart,
  rulerEnd,
  displayedDuration,
  totalDuration,
}: {
  currentTime: number;
  rulerStart: number;
  rulerEnd: number;
  displayedDuration: number;
  totalDuration: number;
}) => {
  const viewportEndThreshold = rulerEnd - displayedDuration;

  const shouldScroll = currentTime >= viewportEndThreshold && rulerEnd < totalDuration;

  if (shouldScroll) {
    const newRulerStart = rulerStart + displayedDuration;
    const newRulerEnd = Math.min(totalDuration, newRulerStart + displayedDuration);

    return { rulerStart: newRulerStart, rulerEnd: newRulerEnd, shouldScroll: true };
  }

  return { rulerStart, rulerEnd, shouldScroll: false };
};

export const calculatePositionInPercent = ({ time, duration }: { time: number; duration: number }) => {
  const position = (time / duration) * 100;
  return position;
};

export const calculateRelativePositionInViewport = ({
  absoluteTime,
  rulerStart,
  displayedDuration,
}: {
  absoluteTime: number;
  rulerStart: number;
  displayedDuration: number;
}) => {
  const relativeTime = absoluteTime - rulerStart;
  const position = (relativeTime / displayedDuration) * 100;
  return Math.max(0, Math.min(100, position));
};

export const handleRulerExtension = ({
  displayedDuration,
  totalDuration,
  rulerEnd,
  rulerStart,
  setRulerEnd,
  setRulerStart,
}: {
  displayedDuration: number;
  totalDuration: number;
  rulerEnd: number;
  rulerStart: number;
  setRulerEnd: (value: number) => void;
  setRulerStart: (value: number) => void;
}) => {
  const RULER_EXTENSION_RATIO = 0.2; // Extend ruler by 20% of current duration
  const extension = displayedDuration * RULER_EXTENSION_RATIO;

  const animationDuration = 300;
  const startTime = performance.now();
  const startRulerEnd = rulerEnd;
  const targetRulerEnd = Math.min(totalDuration, rulerEnd + extension);

  const animate = (currentTime: number) => {
    const elapsed = currentTime - startTime;
    const progress = Math.min(elapsed / animationDuration, 1);

    const easeProgress = 1 - Math.pow(1 - progress, 3);

    const newRulerEnd = startRulerEnd + (targetRulerEnd - startRulerEnd) * easeProgress;
    setRulerEnd(newRulerEnd);

    if (newRulerEnd > totalDuration) {
      const overflow = newRulerEnd - totalDuration;
      setRulerStart(Math.max(0, rulerStart - overflow));
    }

    if (progress < 1) {
      requestAnimationFrame(animate);
    }
  };

  requestAnimationFrame(animate);
};

export const handleRulerBackwardsExtension = ({
  displayedDuration,
  rulerStart,
  setRulerStart,
}: {
  displayedDuration: number;
  rulerStart: number;
  setRulerStart: (value: number) => void;
}) => {
  const RULER_EXTENSION_RATIO_START = 0.2; // Extend ruler by 20% of current duration

  const extension = displayedDuration * RULER_EXTENSION_RATIO_START;
  const newStart = Math.max(0, rulerStart - extension);

  const startTime = performance.now();
  const duration = 300;
  const startValue = rulerStart;
  const change = newStart - startValue;

  const animate = (currentTime: number) => {
    const elapsed = currentTime - startTime;
    const progress = Math.min(elapsed / duration, 1);

    const eased = progress * (2 - progress);
    setRulerStart(startValue + change * eased);

    if (progress < 1) {
      requestAnimationFrame(animate);
    }
  };

  requestAnimationFrame(animate);
};

export const calculateTime = ({ position, duration }: { position: number; duration: number }) => {
  const time = (position / 100) * duration;
  return time;
};
