import { ChapterType } from '@/modules/courses/constants/course.const';
import { LessonType } from '@/modules/courses/types/chapter.type';
import { Lecture, Section } from '@/modules/courses/types/course.type';

export const createChapterSortIndex = (list: Section[], type: ChapterType) => {
  const filtered = list?.filter((item) => item.sectionType === type) ?? [];
  const maxSortIndex = filtered.reduce((max, item) => Math.max(max, item.sortIndex), 0);
  return maxSortIndex + 1;
};

export const createLessonSortIndex = (list: Lecture[]) => {
  const maxSortIndex = list.reduce((max, item) => Math.max(max, item.sortIndex || 0), 0);
  return maxSortIndex + 1;
};

export const createDraftSection = (params: { sectionName?: string; sectionType: ChapterType; sortIndex: number }) => {
  const sortIndex = params.sortIndex;
  const draftSection = {
    id: 'draft',
    sectionName: '',
    sortIndex,
    sectionType: params.sectionType,
    lectures: [] as Lecture[],
  } as Section;
  return draftSection;
};

export const createSection = (params: { sectionName: string; sectionType: ChapterType; sortIndex: number }) => {
  const sectionName = params.sectionName;
  const sortIndex = params.sortIndex;
  const section = { sectionName, sortIndex, sectionType: params.sectionType } as Section;
  return section;
};

export const createDraftLesson = (params: { lessonName?: string; lessonType: LessonType; sortIndex: number }) => {
  const lessonName = params.lessonName || 'Tiêu đề bài học';
  const sortIndex = params.sortIndex;
  const lesson = { lessonName, sortIndex, lectureType: params.lessonType, id: 'draft' };
  return lesson;
};

export const checkSectionNameExist = ({ sections, sectionName }: { sections: Section[]; sectionName: string }) => {
  return sections.some((section) => section.sectionName === sectionName);
};
