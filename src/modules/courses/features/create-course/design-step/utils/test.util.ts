import { GetTestResponse, OptionResponse, QuestionResponse } from '@/modules/courses/services/test.service';
import { Question } from '@/modules/courses/types/test.type';

export const mapQuizResponse = (quiz: GetTestResponse) => {
  return {
    id: quiz.id,
    testName: quiz.test_name,
    minCorrectAnswer: quiz.min_correct_answer,
    hasLimitTime: quiz.has_limit_time,
    limitTime: quiz.limit_time,
    createdAt: quiz.created_at,
    updatedAt: quiz.updated_at,
    questions: quiz.questions.map(mapQuestionResponse),
  };
};

export const mapQuestionResponse = (question: QuestionResponse): Question => {
  return {
    id: question.id,
    questionName: question.question_name,
    questionImage: question.question_image,
    questionOptions: question.question_options.map(mapQuestionOption),
    correctAnswer: question.correct_answer,
    questionTypeId: question.question_type_id,
    sortIndex: question.sort_index,
    questionImageFile: question.question_image_file,
    videoFile: question.video_file,
  };
};

export const mapQuestionOption = (option: OptionResponse): Question['questionOptions'][number] => {
  return {
    optionIndex: option.option_index,
    optionName: option.option_name,
    optionThumbnailImageFile: option.option_thumbnail_image_file,
  };
};
