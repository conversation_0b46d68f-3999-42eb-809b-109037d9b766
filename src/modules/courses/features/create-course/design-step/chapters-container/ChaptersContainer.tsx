'use client';

import { CourseInfo, Section } from '@/modules/courses/types/course.type';
import React, { PropsWithChildren } from 'react';
import ChapterList from './ChapterList';
import { ChaptersProvider } from './ChaptersProvider';

type Props = {
  sections: Section[];
  courseDetail: CourseInfo;
  setSections: (sections: Section[]) => void;
} & PropsWithChildren;

function ChaptersContainer(props: Props) {
  const { courseDetail, sections, setSections } = props;

  const [isCreatingDraft, setIsCreatingDraft] = React.useState(false);

  return (
    <ChaptersProvider value={{ sections, isCreatingDraft, setSections, setIsCreatingDraft }}>
      <ChapterList courseDetail={courseDetail} />
    </ChaptersProvider>
  );
}

export default ChaptersContainer;
