import { Typography } from '@/components/ui';
import { useClickOutside } from '@/hooks';
import { cn } from '@/lib/utils';
import { InputEditor } from '@/modules/courses/components';
import { ChapterType, LessonType } from '@/modules/courses/constants/course.const';
import { useChaptersProvider } from '@/modules/courses/features/create-course/design-step/chapters-container/ChaptersProvider';
import React from 'react';
import { match } from 'ts-pattern';

type Props = {
  isEdit?: boolean;
  value: string;
  isCreatingDraft?: boolean;
  type: LessonType | ChapterType;
  editable?: boolean;

  onClickOutside?: (value: string) => void;
  onCancel?: () => void;
  setIsEdit?: (value: boolean) => void;
  onConfirm?: (value: string) => void;
};

const getPlaceholder = (type: LessonType | ChapterType) => {
  return match(type)
    .with(ChapterType.Default, () => 'Nhập tiêu đề chương')
    .with(ChapterType.Test, () => 'Nhập tiêu đề bài ôn tập')
    .with(LessonType.Video, () => 'Nhập tiêu đề bài học')
    .with(LessonType.Test, () => 'Nhập tiêu đề bài ôn tập')
    .exhaustive();
};

function SectionTitle(props: Props) {
  const {
    isEdit = false,
    editable = false,
    isCreatingDraft = false,
    value,
    type,

    onClickOutside = () => {},
    onCancel,
    setIsEdit,
    onConfirm,
  } = props;

  const [hoverEdit, setHoverEdit] = React.useState(false);
  const [inputValue, setInputValue] = React.useState(value);

  const { sections, setSections } = useChaptersProvider();

  const ref = React.useRef<HTMLDivElement>(null);

  const showEditInput = isEdit || isCreatingDraft;
  const placeholder = getPlaceholder(type);

  useClickOutside(ref as React.RefObject<HTMLElement>, () => {
    onClickOutside?.(inputValue);

    setIsEdit?.(false);
  });

  const handleEdit = (newValue: string) => {
    setInputValue(newValue);
    onConfirm?.(newValue);

    setIsEdit?.(false);
  };

  const handleCancel = () => {
    setIsEdit?.(false);

    const newSections = sections.filter((section) => section.id !== 'draft');

    setSections(newSections);
    setInputValue(value);
    onCancel?.();
  };

  React.useEffect(() => {
    if (isCreatingDraft || isEdit) {
      const inputElement = ref.current?.querySelector('input');
      inputElement?.focus();
    }
  }, [isEdit, isCreatingDraft]);

  return (
    <div className="w-3/4">
      {showEditInput ? (
        <div>
          <InputEditor
            ref={ref}
            inputProps={{ maxLength: 200 }}
            wrapProps={{ className: 'p-0' }}
            value={inputValue}
            placeholder={placeholder}
            onCancel={handleCancel}
            onEdit={handleEdit}
            onChange={(e) => setInputValue(e.target.value)}
          />
        </div>
      ) : (
        <div
          title={value}
          className={cn('h-10 w-full truncate p-2', editable && hoverEdit && 'cursor-text rounded-lg bg-neutral-50')}
          onClick={() => editable && setIsEdit?.(true)}
          onMouseEnter={() => editable && setHoverEdit(true)}
          onMouseLeave={() => editable && setHoverEdit(false)}
        >
          <Typography variant="titleMd">{value}</Typography>
        </div>
      )}
    </div>
  );
}

export default SectionTitle;
