'use client';

import { Section } from '@/modules/courses/types/course.type';
import React from 'react';

type ChaptersContextActions = {
  setIsCreatingDraft: (isCreatingCraft: boolean) => void;
  setSections: (sections: Section[]) => void;
};

type ChaptersContextState = {
  sections: Section[];
  isCreatingDraft: boolean;
};

type ChaptersContextValue = ChaptersContextActions & ChaptersContextState;

const ChaptersContext = React.createContext<ChaptersContextState | null>(null);

export const useChaptersProvider = () => {
  const context = React.useContext(ChaptersContext);
  if (!context) {
    throw new Error('useChaptersProvider must be used within a ChaptersProvider');
  }
  return context as ChaptersContextValue;
};

export function ChaptersProvider({ children, value }: { children: React.ReactNode; value: ChaptersContextValue }) {
  return <ChaptersContext.Provider value={value}>{children}</ChaptersContext.Provider>;
}
