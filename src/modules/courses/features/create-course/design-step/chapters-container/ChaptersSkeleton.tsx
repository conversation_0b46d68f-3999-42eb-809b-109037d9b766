import { Skeleton } from 'antd';

export default function ChaptersSkeleton() {
  return (
    <div className="flex size-full flex-col gap-4 py-10">
      <div className="flex w-full items-end justify-between">
        <div className="flex w-full flex-col gap-2">
          <Skeleton.Input active className="h-6 w-1/2" />
          <Skeleton.Input active className="h-8 w-1/2" />
        </div>

        <div className="w-1/6">
          <Skeleton.Button active size="small" className="h-12 w-full" />
        </div>
      </div>

      <div className="w-full">
        <Skeleton.Input active className="h-9 w-1/2" />
      </div>

      <div className="flex w-full flex-col gap-4">
        <Skeleton.Node active className="h-28 w-full" />
        <Skeleton.Node active className="h-28 w-full" />
        <Skeleton.Node active className="h-28 w-full" />
        <Skeleton.Node active className="h-28 w-full" />
        <Skeleton.Node active className="h-28 w-full" />
      </div>
    </div>
  );
}
