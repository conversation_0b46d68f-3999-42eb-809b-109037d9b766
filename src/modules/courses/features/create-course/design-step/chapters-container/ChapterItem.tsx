import { Icon } from '@/components/client';
import { Button, Typography } from '@/components/ui';
import { QUERY_KEYS } from '@/constants/query-keys';
import { cn } from '@/lib/utils';
import { useChaptersProvider } from '@/modules/courses/features/create-course/design-step/chapters-container/ChaptersProvider';
import DeleteConfirmModal from '@/modules/courses/features/create-course/design-step/components/DeleteConfirmModal';
import {
  checkSectionNameExist,
  createDraftLesson,
  createLessonSortIndex,
  createSection,
} from '@/modules/courses/features/create-course/design-step/utils/section.util';
import {
  createSectionService,
  deleteSectionService,
  editSectionService,
  swapLessonService,
  swapSectionService,
} from '@/modules/courses/services/course.service';
import { swapElements } from '@/utils';
import { HolderOutlined } from '@ant-design/icons';
import { DragDropContext, Draggable, Droppable, DropResult } from '@hello-pangea/dnd';
import { CameraIcon, DocumentTextIcon, TrashIcon } from '@heroicons/react/24/outline';
import { App, Tooltip } from 'antd';
import { useParams } from 'next/navigation';
import React from 'react';
import { useMutation, useQueryClient } from 'react-query';
import { ChapterType, LessonType } from '../../../../constants/course.const';
import { Section } from '../../../../types/course.type';
import LessonItem from './LessonItem';
import SectionTitle from './SectionTitle';

type Props = {
  section: Section;
  isCreatingDraft: boolean;
  isDragging: boolean;
  sectionType: ChapterType;
  setIsCreatingDraft: (isCreatingDraft: boolean) => void;
};

export function useChapterActions() {
  const { notification } = App.useApp();
  const queryClient = useQueryClient();

  const createSectionMutation = useMutation({
    mutationFn: createSectionService,
    onSuccess: () => {
      queryClient.invalidateQueries([QUERY_KEYS.COURSE_DETAIL]);
      notification.success({ message: 'Tạo chương thành công' });
    },
    onError: () => {
      notification.error({ message: 'Tạo chương thất bại' });
    },
  });

  const editSectionMutation = useMutation({
    mutationFn: editSectionService,
    onSuccess: () => {
      queryClient.invalidateQueries([QUERY_KEYS.COURSE_DETAIL]);
      notification.success({ message: 'Chỉnh sửa chương thành công' });
    },
    onError: () => {
      notification.error({ message: 'Chỉnh sửa chương thất bại' });
    },
  });

  const deleteSectionMutation = useMutation({
    mutationFn: deleteSectionService,
    onSuccess: () => {
      queryClient.invalidateQueries([QUERY_KEYS.COURSE_DETAIL]);
      notification.success({ message: 'Xóa chương thành công' });
    },
    onError: () => {
      notification.error({ message: 'Xóa chương thất bại' });
    },
  });

  return {
    createSectionMutation,
    editSectionMutation,
    deleteSectionMutation,
  };
}

const useChapter = ({ section, sectionType }: { section: Section; sectionType: ChapterType }) => {
  const { courseId = '' } = useParams<{ courseId: string }>();
  const { isCreatingDraft, sections, setIsCreatingDraft } = useChaptersProvider();

  const queryClient = useQueryClient();
  const { notification } = App.useApp();

  const [isEdit, setIsEdit] = React.useState(false);
  const [isOpenDeleteModal, setIsOpenDeleteModal] = React.useState(false);
  const [lastCreateRequest, setLastCreateRequest] = React.useState<string | null>(null);
  const [lastEditRequest, setLastEditRequest] = React.useState<string | null>(null);

  const { createSectionMutation, editSectionMutation, deleteSectionMutation } = useChapterActions();

  const swapSectionMutation = useMutation({
    mutationFn: swapSectionService,
    onSuccess: () => queryClient.invalidateQueries([QUERY_KEYS.COURSE_DETAIL]),
  });

  const handleCreateSection = (sectionName: string) => {
    if (createSectionMutation.isLoading) return;

    if (lastCreateRequest === sectionName && createSectionMutation.status === 'error') return;

    setLastCreateRequest(sectionName);
    const newSection = createSection({ sectionName, sortIndex: section.sortIndex, sectionType });
    createSectionMutation.mutate(
      { courseId, ...newSection },
      {
        onSuccess: () => {
          setIsCreatingDraft(false);
          setLastCreateRequest(null);
        },
      },
    );
  };

  const handleEditSection = (sectionName: string) => {
    if (editSectionMutation.isLoading || sectionName === section.sectionName) return;

    if (lastEditRequest === sectionName && editSectionMutation.status === 'error') return;

    setLastEditRequest(sectionName);
    editSectionMutation.mutate(
      { sectionName, sectionId: section.id, courseId },
      {
        onSuccess: () => {
          if (isEdit) {
            setIsEdit(false);
            setLastEditRequest(null);
          }
        },
      },
    );
  };

  const handleDeleteSection = () => {
    deleteSectionMutation.mutate(
      { courseId, sectionId: section.id },
      {
        onSuccess: () => {
          setIsCreatingDraft(false);
          setIsOpenDeleteModal(false);
        },
      },
    );
  };

  const handleConfirm = (sectionName: string) => {
    if (sectionName === section.sectionName) return;

    const isExisted = checkSectionNameExist({ sections, sectionName });

    if (isExisted) {
      return notification.error({ message: 'Tên chương đã tồn tại' });
    }

    if (isCreatingDraft) {
      return handleCreateSection(sectionName);
    }

    if (isEdit) return handleEditSection(sectionName);
  };

  const handleCancel = () => {
    setIsEdit(false);
    setLastCreateRequest(null);
    setLastEditRequest(null);
  };

  return {
    isEdit,
    isOpenDeleteModal,
    swapSectionMutation,
    onDeleteSection: handleDeleteSection,
    onCreateSection: handleCreateSection,
    onEditSection: handleEditSection,
    onConfirm: handleConfirm,
    onCancel: handleCancel,
    setIsEdit,
    setIsOpenDeleteModal,
  };
};

function ChapterItem(props: Props) {
  const { section, isDragging, sectionType } = props;

  const { isCreatingDraft, setIsCreatingDraft } = useChaptersProvider();

  const {
    isEdit,
    isOpenDeleteModal,
    onDeleteSection,
    onConfirm,
    onCreateSection,
    onEditSection,
    onCancel,
    setIsEdit,
    setIsOpenDeleteModal,
  } = useChapter({ section, sectionType });

  const { sections, setSections } = useChaptersProvider();

  const { courseId } = useParams<{ courseId: string }>();

  const isSectionDraft = section.id === 'draft';

  const handleCreateDraftLesson = (lessonType: LessonType) => {
    const hasDraftLesson = section.lectures.some((lesson) => lesson.id === 'draft');

    if (isCreatingDraft || hasDraftLesson) return;

    setIsCreatingDraft(true);

    const sortIndex = createLessonSortIndex(section.lectures);
    const newLesson = createDraftLesson({ lessonName: 'Tiêu đề bài học', sortIndex, lessonType });
    const newLessonList = [...section.lectures, newLesson];

    const newSections = sections.map((sectionItem) => {
      if (sectionItem.id === section.id) {
        return { ...sectionItem, lectures: newLessonList };
      }
      return sectionItem;
    }) as Section[];

    setSections(newSections);
  };

  const handleSwapLesson = (result: DropResult) => {
    const { source, destination } = result;
    if (!destination) return;

    const newLessonList = swapElements(section.lectures, source.index, destination.index, 'sortIndex');

    const newSections = sections.map((sectionItem) => {
      if (sectionItem.id === section.id) {
        return { ...sectionItem, lectures: newLessonList };
      }
      return sectionItem;
    }) as Section[];

    setSections(newSections);

    swapLessonService({
      courseId,
      sectionId: section.id,
      lectureId: newLessonList[source.index].id,
      sourceIndex: source.index,
      destinationIndex: destination.index,
    });
  };

  const handleClickOutside = (chapterName: string) => {
    if (isCreatingDraft) {
      const getUniqueChapterName = (baseIndex: number): string => {
        const name = `Chương ${baseIndex}`;
        const isExisted = sections.some((s) => s.sectionName === name && s.id !== section.id);
        if (isExisted) return getUniqueChapterName(baseIndex + 1);
        return name;
      };
      const finalChapterName = chapterName?.trim()?.length ? chapterName : getUniqueChapterName(section.sortIndex);
      return onCreateSection(finalChapterName);
    }

    if (isEdit) return onEditSection(chapterName);
  };

  const handleConfirm = (sectionName: string) => onConfirm(sectionName);

  const handleCancel = () => {
    if (isCreatingDraft) setIsCreatingDraft(false);
    onCancel();
  };

  return (
    <div
      className={cn(
        'flex w-full flex-col justify-between rounded-lg border border-neutral-200 bg-white',
        isDragging && 'rounded-lg border border-neutral-200 bg-white p-3 shadow-md',
      )}
    >
      <div className={cn('relative flex w-full justify-between py-4 pl-4 pr-2')}>
        {section.sectionType === ChapterType.Default ? (
          <div className="absolute left-0 top-1/2 -translate-x-6 -translate-y-2">
            <HolderOutlined className="text-xl" />
          </div>
        ) : null}

        <SectionTitle
          editable={true}
          isEdit={isEdit}
          value={section.sectionName}
          type={section.sectionType}
          isCreatingDraft={isSectionDraft}
          setIsEdit={setIsEdit}
          onConfirm={handleConfirm}
          onCancel={handleCancel}
          onClickOutside={handleClickOutside}
        />

        <div className="flex items-center justify-center">
          <Tooltip title={'Xóa chương'}>
            <Button
              disabled={isSectionDraft}
              variant="tertiary"
              className="p-2.5"
              size="small"
              onClick={() => setIsOpenDeleteModal(true)}
            >
              <Icon icon={<TrashIcon />} className="size-5" />
            </Button>
          </Tooltip>
        </div>
      </div>

      <div className="h-full border-t border-neutral-200">
        <DragDropContext onDragEnd={handleSwapLesson}>
          <Droppable droppableId={'lessons'} key={section.id}>
            {(provided, snapshot) => (
              <div ref={provided.innerRef} {...provided.droppableProps} className={cn('flex flex-col')}>
                {section?.lectures?.map((lesson, index) => {
                  const isLessonDraft = lesson.id === 'draft';

                  return (
                    <Draggable key={lesson.id} draggableId={`lesson-${lesson.id}`} index={index}>
                      {(provided) => (
                        <div ref={provided.innerRef} {...provided.draggableProps} {...provided.dragHandleProps}>
                          <LessonItem
                            isDraft={isLessonDraft}
                            section={section}
                            chapterType={section.sectionType}
                            isDragging={snapshot.isDraggingOver}
                            lesson={lesson}
                          />
                        </div>
                      )}
                    </Draggable>
                  );
                })}
              </div>
            )}
          </Droppable>
        </DragDropContext>
      </div>

      {section.sectionType === ChapterType.Default && !isSectionDraft ? (
        <div className="flex gap-2 border-t border-neutral-200 p-4">
          <Button variant="tertiary" size="small" onClick={() => handleCreateDraftLesson(LessonType.Video)}>
            <div className="flex items-center gap-2">
              <Icon icon={<CameraIcon />} className="size-5" />
              <Typography variant="labelMd">Thêm Bài giảng Video</Typography>
            </div>
          </Button>

          <Button variant="tertiary" size="small" onClick={() => handleCreateDraftLesson(LessonType.Test)}>
            <div className="flex items-center gap-2">
              <Icon icon={<DocumentTextIcon />} className="size-5" />
              <Typography variant="labelMd">Thêm bài ôn tập</Typography>
            </div>
          </Button>
        </div>
      ) : null}

      {isOpenDeleteModal && (
        <DeleteConfirmModal
          type={sectionType === ChapterType.Default ? 'CHAPTER' : 'QUIZ'}
          open={isOpenDeleteModal}
          onClose={() => setIsOpenDeleteModal(false)}
          onCancel={() => setIsOpenDeleteModal(false)}
          onConfirm={onDeleteSection}
        />
      )}
    </div>
  );
}

export default ChapterItem;
