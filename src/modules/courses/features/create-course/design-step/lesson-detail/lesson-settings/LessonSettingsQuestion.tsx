'use client';

import { <PERSON><PERSON>, <PERSON><PERSON>, Typo<PERSON> } from '@/components/ui';
import ClockOutlined from '@/icons/ClockOutlined';
import QuestionIcon from '@/icons/QuestionIcon';
import { useLessonStore } from '@/z-store/lesson.store';
import { TrashIcon } from '@heroicons/react/24/outline';
import { InputNumber, Radio, Select, Space, Switch, TimePicker } from 'antd';
import TextArea from 'antd/lib/input/TextArea';
import type { Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import React, { useState } from 'react';
import useInteractionActions from '../../hooks/useInteractionActions';
import useInteractionAutoSave from '../../hooks/useInteractionAutoSave';
import { useLessonProvider } from '../LessonProvider';

const { Option } = Select;

export default function LessonSettingsQuestion() {
  const {
    interaction,
    updateInteractionStartAt,
    updateQuestionTypeId,
    updateQuestionRequired,
    updateQuestionDuration,
    updateReplyR<PERSON>Answer,
    updateReplyWrongAnswer,
    updateShowResponse,
    updateCorrectAnswer,
  } = useLessonStore();
  const { lessonDetail } = useLessonProvider();

  const videoDuration = lessonDetail.videoId?.fileDuration || 0;
  // Auto-save functionality for content
  const { saveInteractionContent, isSaving } = useInteractionAutoSave();

  // Interaction actions hook for updating startAt/duration
  const { onUpdateInteraction, onDeleteInteraction, isUpdating, isDeleting } = useInteractionActions();

  // State for delete confirmation modal
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  // Convert seconds to dayjs time object
  const secondsToTime = (seconds: number | null): Dayjs | null => {
    if (!seconds) return null;
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return dayjs().hour(hours).minute(minutes).second(secs);
  };

  // Convert dayjs time object to seconds
  const timeToSeconds = (time: any): number | null => {
    if (!time) return null;
    return time.hour() * 3600 + time.minute() * 60 + time.second();
  };

  // Get the maximum selectable time based on video duration
  const getMaxSelectableTime = (): Dayjs | undefined => {
    if (!videoDuration) return undefined;
    return secondsToTime(videoDuration) || undefined;
  };

  const handleTimeChange = (time: any, _timeString: string | string[]) => {
    const startAtSeconds = timeToSeconds(time);

    // Validate that startAt doesn't exceed video duration
    if (startAtSeconds && videoDuration && startAtSeconds > videoDuration) {
      // If startAt exceeds video duration, set it to video duration
      const maxStartAt = videoDuration;
      updateInteractionStartAt(maxStartAt);

      if (interaction.id) {
        onUpdateInteraction({
          interactId: interaction.id,
          startAt: maxStartAt,
          duration: interaction.duration || 0,
        });
      }
      return;
    }

    updateInteractionStartAt(startAtSeconds);

    // Call onUpdateInteraction for startAt changes
    if (interaction.id) {
      onUpdateInteraction({
        interactId: interaction.id,
        startAt: startAtSeconds || 0,
        duration: interaction.duration || 0,
      });
    }
  };

  const showDeleteConfirmation = () => {
    setShowDeleteModal(true);
  };

  const handleDeleteInteraction = () => {
    if (interaction.id) {
      onDeleteInteraction({
        interactId: interaction.id,
      });
    }
    setShowDeleteModal(false);
  };

  const cancelDelete = () => {
    setShowDeleteModal(false);
  };

  const handleQuestionTypeChange = (value: 'single' | 'multiple') => {
    // Map to question: single = 0, multiple = 1
    const questionTypeId = value === 'single' ? 0 : 1;
    updateQuestionTypeId(questionTypeId);
    updateCorrectAnswer([]);
    // Save immediately for select changes
    saveInteractionContent();
  };

  const handleAnswerRequiredChange = (e: any) => {
    const isRequired = e.target.value === 'required';

    if (isRequired) {
      // If required, set a default questionDuration (e.g., 10 seconds)
      updateQuestionDuration(10);
      updateQuestionRequired(true);
    } else {
      // If optional, clear the questionDuration
      updateQuestionDuration(0);
      updateQuestionRequired(false);
    }

    // Save immediately for radio button changes
    saveInteractionContent();
  };

  const handleSkipTimeChange = (value: number | null) => {
    if (value !== null) {
      updateQuestionDuration(value);

      saveInteractionContent();
    }
  };

  const handleShowResponseChange = (checked: boolean) => {
    updateShowResponse(checked);

    if (checked && interaction.question) {
      // If turning on, set default reply messages if they don't exist
      if (!interaction.question.replyRightAnswer) {
        updateReplyRightAnswer('Chính xác!');
      }
      if (!interaction.question.replyWrongAnswer) {
        updateReplyWrongAnswer('Không chính xác. Hãy thử lại!');
      }
    } else {
      // If turning off, clear the reply messages
      updateReplyRightAnswer('');
      updateReplyWrongAnswer('');
    }

    // Save immediately for switch changes
    saveInteractionContent();
  };

  const handleCorrectAnswerChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    // Limit to 200 characters
    if (value.length > 200) return;
    updateReplyRightAnswer(value);

    // If adding text, make sure showResponse is enabled
    if (value.trim()) {
      updateShowResponse(true);
    }
  };

  const handleCorrectAnswerBlur = () => {
    // Get current value from the store and save
    const currentInteraction = useLessonStore.getState().interaction;

    // Check if both reply answers are empty and disable showResponse if so
    if (
      currentInteraction.question &&
      !currentInteraction.question.replyRightAnswer?.trim() &&
      !currentInteraction.question.replyWrongAnswer?.trim()
    ) {
      updateShowResponse(false);
    }

    saveInteractionContent();
  };

  const handleWrongAnswerChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    // Limit to 200 characters
    if (value.length > 200) return;
    updateReplyWrongAnswer(value);

    // If adding text, make sure showResponse is enabled
    if (value.trim()) {
      updateShowResponse(true);
    }
  };

  const handleWrongAnswerBlur = () => {
    // Get current value from the store and save
    const currentInteraction = useLessonStore.getState().interaction;

    // Check if both reply answers are empty and disable showResponse if so
    if (
      currentInteraction.question &&
      !currentInteraction.question.replyRightAnswer?.trim() &&
      !currentInteraction.question.replyWrongAnswer?.trim()
    ) {
      updateShowResponse(false);
    }

    saveInteractionContent();
  };

  return (
    <div className="rounded-lg bg-white p-6">
      <div className="space-y-6">
        {/* Appearance Time */}
        <div>
          <Typography className="text-gray-700 mb-2 block font-poppins text-sm font-semibold">
            Thời điểm xuất hiện <span className="text-red-500">*</span>
          </Typography>
          <div className="flex items-center space-x-2">
            <TimePicker
              value={secondsToTime(interaction.startAt)}
              onChange={handleTimeChange}
              format="HH:mm:ss"
              placeholder="HH:mm:ss"
              className="flex-1 font-nunito"
              showNow={false}
              components={{
                input: (props) => <input {...props} className="font-nunito" />,
              }}
              disabledTime={() => {
                if (!videoDuration) return {};
                const maxTime = secondsToTime(videoDuration);
                if (!maxTime) return {};

                const maxHour = maxTime.hour();
                const maxMinute = maxTime.minute();
                const maxSecond = maxTime.second();

                return {
                  disabledHours: () => Array.from({ length: 24 }, (_, i) => i).filter((hour) => hour > maxHour),
                  disabledMinutes: (selectedHour: number) => {
                    if (selectedHour < maxHour) return [];
                    if (selectedHour > maxHour) return Array.from({ length: 60 }, (_, i) => i);
                    return Array.from({ length: 60 }, (_, i) => i).filter((minute) => minute > maxMinute);
                  },
                  disabledSeconds: (selectedHour: number, selectedMinute: number) => {
                    if (selectedHour < maxHour) return [];
                    if (selectedHour > maxHour) return Array.from({ length: 60 }, (_, i) => i);
                    if (selectedMinute < maxMinute) return [];
                    if (selectedMinute > maxMinute) return Array.from({ length: 60 }, (_, i) => i);
                    return Array.from({ length: 60 }, (_, i) => i).filter((second) => second > maxSecond);
                  },
                };
              }}
            />
            <ClockOutlined />
            <TrashIcon className="size-5 cursor-pointer text-red-500" onClick={showDeleteConfirmation} />
          </div>
        </div>

        {/* Question Type */}
        <div>
          <Typography className="text-gray-700 mb-2 block font-poppins text-sm font-semibold">
            Loại câu hỏi <span className="text-red-500">*</span>
          </Typography>
          <Select
            value={interaction.question.questionTypeId === 0 ? 'single' : 'multiple'}
            onChange={handleQuestionTypeChange}
            className="w-full"
          >
            <Option value="single">
              <Typography className="font-nunito">Chỉ một đáp án đúng</Typography>
            </Option>
            <Option value="multiple">
              <Typography className="font-nunito">Nhiều đáp án đúng</Typography>
            </Option>
          </Select>
        </div>

        {/* Answer Required */}
        <div>
          <Typography className="text-gray-700 mb-2 block font-poppins text-sm font-semibold">
            Yêu cầu trả lời để tiếp tục <span className="text-red-500">*</span>
          </Typography>
          <Radio.Group
            value={interaction.question.questionRequired ? 'required' : 'optional'}
            onChange={handleAnswerRequiredChange}
          >
            <Space direction="vertical" className="w-full">
              <Radio value="required" className="font-poppins">
                Bắt buộc
              </Radio>
              <Radio value="optional" className="font-poppins">
                Không bắt buộc (Có thể bỏ qua)
              </Radio>
            </Space>
          </Radio.Group>
        </div>

        {/* Skip Time - Only show when answer is not required */}
        {!interaction.question.questionRequired && (
          <div>
            <Typography className="text-gray-700 mb-2 block font-poppins text-sm font-semibold">
              Bỏ qua sau thời gian [{interaction.question.questionDuration || 0}] giây{' '}
              <span className="text-red-500">*</span>
            </Typography>
            <div className="flex items-center space-x-2">
              <InputNumber
                value={interaction.question.questionDuration || 0}
                onChange={handleSkipTimeChange}
                min={1}
                max={999}
                className="flex-1 text-black"
                classNames={{
                  input: 'font-nunito bg-[#F2F2F4]',
                  wrapper: 'font-nunito',
                  variant: 'font-nunito',
                  groupWrapper: 'font-nunito',
                }}
                rootClassName="font-nunito"
              />
              <QuestionIcon />
            </div>
          </div>
        )}

        {/* Show Response */}
        <div className="flex items-center justify-between">
          <Typography className="text-gray-700 block font-poppins text-sm font-semibold">
            Hiển thị phản hồi với người học
          </Typography>
          <Switch checked={interaction.question.showResponse || false} onChange={handleShowResponseChange} />
        </div>

        {/* Correct Answer Text - Only show if showResponse is enabled */}
        {interaction.question.showResponse && (
          <div>
            <Typography className="text-gray-700 mb-2 block font-poppins text-sm font-semibold">Đáp án đúng</Typography>
            <div className="relative">
              <TextArea
                value={interaction.question.replyRightAnswer || ''}
                onChange={handleCorrectAnswerChange}
                onBlur={handleCorrectAnswerBlur}
                placeholder="Nhập phản hồi khi người học chọn đáp án đúng (Không bắt buộc)"
                className="w-full font-nunito"
                maxLength={200}
                classNames={{
                  textarea: 'font-nunito',
                }}
                count={{ max: 200 }}
                showCount
                autoSize={{ minRows: 3, maxRows: 6 }}
              />
            </div>
          </div>
        )}

        {/* Wrong Answer Text - Only show if showResponse is enabled */}
        {interaction.question.showResponse && (
          <div>
            <Typography className="text-gray-700 mb-2 block font-poppins text-sm font-semibold">Đáp án sai</Typography>
            <div className="relative">
              <TextArea
                value={interaction.question.replyWrongAnswer || ''}
                onChange={handleWrongAnswerChange}
                onBlur={handleWrongAnswerBlur}
                placeholder="Nhập phản hồi khi người học chọn đáp án sai (Không bắt buộc)"
                className="w-full"
                classNames={{
                  textarea: 'font-nunito',
                }}
                maxLength={200}
                showCount
                count={{ max: 200 }}
                autoSize={{ minRows: 3, maxRows: 6 }}
              />
            </div>
          </div>
        )}
      </div>

      {/* Delete Confirmation Modal */}
      <Modal open={showDeleteModal} type="confirmation" title={'Xóa Tương Tác'} onClose={cancelDelete}>
        <div className="p-6">
          <div className="mb-6 space-y-4">
            <p className="font-poppins text-neutral-700">
              Bạn có chắc chắn muốn xóa tương tác này không? Hành động này không thể hoàn tác.
            </p>
            <p className="font-poppins text-neutral-600">
              Tất cả dữ liệu liên quan đến tương tác này sẽ bị xóa vĩnh viễn.
            </p>
          </div>

          <div className="flex justify-end gap-3">
            <Button variant="tertiary" onClick={cancelDelete} disabled={isDeleting} className="font-poppins">
              Hủy
            </Button>
            <Button variant="error" onClick={handleDeleteInteraction} disabled={isDeleting} className="font-poppins">
              {isDeleting ? 'Đang xóa...' : 'Xóa Tương Tác'}
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
}
