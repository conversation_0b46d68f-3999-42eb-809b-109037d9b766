import { LessonSettingsExplore } from '@/modules/courses/features/create-course/design-step/lesson-detail/lesson-settings/LessonSettingsExplore';
import { useLessonStore } from '@/z-store/lesson.store';
import React from 'react';
import { match } from 'ts-pattern';
import { InteractionType } from '../../../../../constants/course.const';
import LessonSettingsLayout from '../../components/SettingsLayout';
import LessonSettingsDefault from './LessonSettingsDefault';
import LessonSettingsQuickQuestion from './LessonSettingsQuestion';

export default function LessonSettings() {
  const { isOpenInteractionType } = useLessonStore((state) => state.settings);

  return (
    <React.Fragment>
      <LessonSettingsLayout>
        {match(isOpenInteractionType)
          .with(InteractionType.Default, () => <LessonSettingsDefault />)
          .with(InteractionType.Explore, () => <LessonSettingsExplore />)
          .with(InteractionType.QuickQuestion, () => <LessonSettingsQuickQuestion />)
          .exhaustive()}
      </LessonSettingsLayout>
    </React.Fragment>
  );
}
