import { Button, Modal, Typography } from '@/components/ui';

type DeleteVideoModalProps = {
  open: boolean;
  isLoading?: boolean;

  onClose?: () => void;
  onConfirm: () => void;
  onCancel: () => void;
};

export default function DeleteVideoModal(props: DeleteVideoModalProps) {
  const { open, isLoading = false, onClose = () => {}, onConfirm, onCancel } = props;

  return (
    <Modal
      open={open}
      type="confirmation"
      onClose={onClose}
      title="Gỡ video khỏi bài giảng?"
      footer={
        <div className="flex items-center justify-end gap-3 px-6 py-4">
          <Button size="large" variant="tertiary" onClick={onCancel}>
            Hủy
          </Button>
          <Button onClick={onConfirm} variant="error" size="large" loading={isLoading}>
            Gỡ video
          </Button>
        </div>
      }
    >
      <div className="flex flex-col gap-4 px-6 py-4">
        <Typography variant="bodyLg">
          Bạn có chắc chắn muốn gỡ video này khỏi bài giảng?Các bài tập tương tác và tuỳ chỉnh trong video sẽ không thể
          khôi phục.Video vẫn được lưu trong Thư viện và có thể dùng lại sau.
        </Typography>
        <Typography variant="bodyLg">Bạn có muốn tiếp tục?</Typography>
      </div>
    </Modal>
  );
}
