import { Button, Modal, Typography } from '@/components/ui';
import useInteractionActions from '@/modules/courses/features/create-course/design-step/hooks/useInteractionActions';
import { useLessonStore } from '@/z-store/lesson.store';
import { TrashIcon } from '@heroicons/react/24/outline';
import { TimePicker } from 'antd';
import dayjs from 'dayjs';
import { useState } from 'react';
import { useLessonProvider } from '../LessonProvider';

type DeleteConfirmModalProps = {
  open: boolean;
  onCancel: () => void;
  onOk: () => void;
};
const DeleteConfirmModal = ({ open, onCancel, onOk }: DeleteConfirmModalProps) => {
  return (
    <Modal
      open={open}
      width={687}
      type="confirmation"
      title="Xoá Khám phá thêm ra khỏi bài giảng?"
      cancelText="Huỷ thao tác"
      okText="Xoá <PERSON>hám phá thêm"
      onClose={onCancel}
      onOk={onOk}
      okButtonProps={{ danger: true }}
      classNames={{
        body: 'px-6 py-4',
        footer: 'px-6 py-4',
      }}
    >
      <Typography variant="bodyLg">
        Bạn có chắc chắn muốn xoá bài tập tương tác Khám phá thêm này khỏi bài giảng video? Các tuỳ chỉnh và tệp đã tải
        lên của Khám phá thêm sẽ không thể khôi phục.
      </Typography>
      <Typography variant="bodyLg" className="mt-6 block">
        Bạn có muốn tiếp tục?
      </Typography>
    </Modal>
  );
};

const LessonSettingsExplore = () => {
  const { lessonDetail } = useLessonProvider();
  const interactionId = useLessonStore((state) => state.settings.interactionId);

  const interaction = lessonDetail?.lectureInteracts?.find((item) => item.id === interactionId);
  const explore = interaction?.explore;

  const startAtTime = interaction?.startAt ? dayjs().startOf('day').add(interaction.startAt, 'second') : null;

  const [open, setOpen] = useState(false);

  const { onUpdateInteraction, onDeleteInteraction } = useInteractionActions();

  const handleCancel = () => {
    setOpen(false);
  };

  const handleOpen = () => {
    const hasContent = explore?.description || !!explore?.attachFiles?.length;

    if (!hasContent && interactionId) return onDeleteInteraction({ interactId: interactionId });

    setOpen(true);
  };

  const handleTimeChange = (date: dayjs.Dayjs) => {
    if (!date || !interaction) return;

    const seconds = date.minute() * 60 + date.second();

    onUpdateInteraction({
      interactId: interaction.id,
      startAt: seconds,
    });
  };

  const handleDeleteInteraction = () => {
    if (!interactionId) return;
    onDeleteInteraction({ interactId: interactionId });
    setOpen(false);
  };

  return (
    <>
      <div className="p-4">
        <Typography variant="labelMd">
          Thời điểm xuất hiện{' '}
          <Typography variant="labelMd" className="text-[#F44D2C]">
            *
          </Typography>
        </Typography>
        <div className="flex items-center gap-2">
          <TimePicker
            value={startAtTime}
            format="mm:ss"
            className="w-full border-neutral-200 bg-white"
            onChange={(date) => handleTimeChange(date as dayjs.Dayjs)}
            disabledTime={() => {
              const videoDuration = lessonDetail?.videoId?.fileDuration || 0;
              const maxMinutes = Math.floor(videoDuration / 60);
              const maxSeconds = videoDuration % 60;
              return {
                disabledMinutes: () => {
                  const minutes = [];
                  for (let i = maxMinutes + 1; i < 60; i++) {
                    minutes.push(i);
                  }
                  return minutes;
                },
                disabledSeconds: (_, selectedMinute: number) => {
                  if (selectedMinute === maxMinutes) {
                    const seconds = [];
                    for (let i = maxSeconds + 1; i < 60; i++) {
                      seconds.push(i);
                    }
                    return seconds;
                  }
                  return [];
                },
              };
            }}
          />
          <Button
            variant="ghost"
            size="large"
            icon={<TrashIcon className="size-6 text-red-500" />}
            onClick={handleOpen}
          />
        </div>
      </div>

      <DeleteConfirmModal open={open} onCancel={handleCancel} onOk={handleDeleteInteraction} />
    </>
  );
};

export { LessonSettingsExplore };
