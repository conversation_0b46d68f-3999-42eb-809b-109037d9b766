import { Button, Modal, Typography } from '@/components/ui';

type ReplaceVideoModalProps = {
  open: boolean;
  isLoading?: boolean;

  onClose?: () => void;
  onConfirm: () => void;
  onCancel: () => void;
};

export default function ReplaceVideoModal(props: ReplaceVideoModalProps) {
  const { open, isLoading = false, onClose = () => {}, onConfirm, onCancel } = props;

  return (
    <Modal
      open={open}
      onClose={onClose}
      type="confirmation"
      title="Thay đổi video khác?"
      footer={
        <div className="flex items-center justify-end gap-3 px-6 py-4">
          <Button size="large" variant="tertiary" onClick={onCancel}>
            Hủy
          </Button>
          <Button onClick={onConfirm} size="large" loading={isLoading}>
            <PERSON><PERSON><PERSON> sang video khác
          </Button>
        </div>
      }
    >
      <div className="flex flex-col gap-4 px-6 py-4">
        <Typography variant="bodyLg">
          <PERSON><PERSON><PERSON> bài tập tương tác và tuỳ chỉnh trong video sẽ bị xoá và không thể khôi phục.
        </Typography>
        <Typography variant="bodyLg">Bạn có chắc chắn muốn đổi sang video khác?</Typography>
      </div>
    </Modal>
  );
}
