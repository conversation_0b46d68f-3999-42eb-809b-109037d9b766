import { Skeleton } from 'antd';

export default function LessonSkeleton() {
  return (
    <div className="flex size-full flex-col gap-4 px-8 py-10">
      <div className="flex w-full flex-col gap-1">
        <Skeleton.Input active className="h-6 w-1/2" />
        <Skeleton.Input active className="h-8 w-2/3" />
      </div>

      <div className="flex h-screen gap-6">
        <Skeleton.Node active className="size-full" />
        <div className="w-1/3 min-w-72">
          <Skeleton.Node active className="size-full" />
        </div>
      </div>
    </div>
  );
}
