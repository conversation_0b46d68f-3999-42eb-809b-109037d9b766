'use client';

import React from 'react';
import { Lecture } from '../../../../types/course.type';

// type LessonContextActions = {};

type LessonContextState = {
  lessonDetail: Lecture;
};

export type LessonContextValue = LessonContextState;

const LessonContext = React.createContext<LessonContextState | null>(null);

export const useLessonProvider = () => {
  const context = React.useContext(LessonContext);

  if (!context) {
    throw new Error('useLessonProvider must be used within a LessonProvider');
  }

  return context as LessonContextValue;
};

export function LessonProvider({ children, value }: { children: React.ReactNode; value: LessonContextValue }) {
  return <LessonContext.Provider value={value}>{children}</LessonContext.Provider>;
}
