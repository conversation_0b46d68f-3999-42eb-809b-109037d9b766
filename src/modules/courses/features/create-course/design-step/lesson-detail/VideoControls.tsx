'use client';

import { Icon } from '@/components/client';
import { Typography } from '@/components/ui';
import { cn } from '@/lib/utils';
import {
  ArrowsPointingOutIcon,
  MinusCircleIcon,
  PlusCircleIcon,
  SpeakerWaveIcon,
  SpeakerXMarkIcon,
} from '@heroicons/react/24/outline';
import { PauseIcon, PlayIcon } from '@heroicons/react/24/solid';

import { Slider } from 'antd';

export function RulerAdjustment({ value, onChange }: { value: number; onChange: (value: number) => void }) {
  return (
    <div className="flex w-24 items-center">
      <Icon
        size="lg"
        icon={<MinusCircleIcon className="size-6" />}
        className="flex cursor-pointer items-center text-neutral-700"
        onClick={() => {
          if (value > 0) {
            onChange(value - 10);
          } else {
            onChange(0);
          }
        }}
      />
      <Slider
        defaultValue={0}
        onChange={onChange}
        value={value}
        max={100}
        min={0}
        className="w-full"
        classNames={{
          handle: cn('after:bg-black after:shadow-none after:outline-none'),
          track: cn('bg-neutral-200'),
        }}
      />
      <Icon
        size="lg"
        icon={<PlusCircleIcon className="size-6" />}
        className="flex cursor-pointer items-center text-neutral-700"
        onClick={() => {
          if (value < 100) {
            onChange(value + 10);
          } else {
            onChange(100);
          }
        }}
      />
    </div>
  );
}

export function VideoTime({
  isPlaying,
  onPauseVideo,
  onPlayVideo,
  renderVideoTime,
}: {
  isPlaying: boolean;
  onPauseVideo: () => void;
  onPlayVideo: () => Promise<void>;
  renderVideoTime: () => string;
}) {
  return (
    <div className="flex items-center gap-4">
      <Icon
        icon={isPlaying ? <PauseIcon className="size-6" /> : <PlayIcon className="size-6" />}
        onClick={isPlaying ? onPauseVideo : onPlayVideo}
        className="cursor-pointer"
      />
      <Typography variant="labelMd">{renderVideoTime()}</Typography>
    </div>
  );
}

export function VideoControl({
  mute,
  onMuteVideo,
  onShowFullScreen,
}: {
  mute: boolean;
  onMuteVideo: () => void;
  onShowFullScreen: () => void;
}) {
  return (
    <div className="flex items-center gap-4">
      <Icon
        icon={mute ? <SpeakerXMarkIcon className="size-6" /> : <SpeakerWaveIcon className="size-6" />}
        onClick={onMuteVideo}
        className="cursor-pointer"
      />
      <Icon icon={<ArrowsPointingOutIcon className="size-6" />} onClick={onShowFullScreen} className="cursor-pointer" />
    </div>
  );
}
