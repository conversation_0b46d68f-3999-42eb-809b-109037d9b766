'use client';

import { InteractionType } from '@/modules/courses/constants/course.const';
import { Skeleton } from 'antd';
import React from 'react';
import useInteractionActions from '../hooks/useInteractionActions';
import AddInteractionButton from './AddInteractionButton';
import TimelineInteractions from './TimelineInteractions';

type VideoTimelineProps = {
  timelineRef: React.RefObject<HTMLDivElement | null>;
  position: number;
  isDragging: boolean;
  duration: number;
  timelineStart: number;
  timelineEnd: number;
  onUpdateTime: ({ duration, time, rulerStart }: { duration: number; time: number; rulerStart?: number }) => void;
};

export default function VideoTimeline(props: VideoTimelineProps) {
  const { timelineRef, position, isDragging, duration, timelineStart, timelineEnd, onUpdateTime } = props;

  const { isCreating, onCreateInteraction } = useInteractionActions();

  const [isHover, setIsHover] = React.useState<boolean>(false);
  const [mouseX, setMouseX] = React.useState<number | null>(null);
  const [timelineWidth, setTimelineWidth] = React.useState<number>(0);
  const [isOpenAddInteraction, setIsOpenAddInteraction] = React.useState<boolean>(false);

  const getAddInteractionButtonPosition = () => {
    if (mouseX !== null && timelineWidth > 0) {
      return (mouseX / timelineWidth) * 100;
    }

    return position;
  };

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!timelineRef.current || isOpenAddInteraction) return;

    const rect = timelineRef.current.getBoundingClientRect();
    const currentMouseX = e.clientX - rect.left;
    setMouseX(Math.max(0, Math.min(currentMouseX, rect.width)));
    setTimelineWidth(rect.width);
  };

  const resetState = () => {
    setIsOpenAddInteraction(false);
    setMouseX(null);
  };

  const handleMouseLeave = () => {
    setIsHover(false);
    resetState();
  };

  const handleCreateInteraction = (type: InteractionType) => {
    const position = getAddInteractionButtonPosition();

    const displayedDuration = timelineEnd - timelineStart;
    const time = timelineStart + (position / 100) * displayedDuration;

    onCreateInteraction({ interactionType: type, startAt: Math.floor(time) });
    resetState();
  };

  React.useEffect(() => {
    if (timelineRef.current) {
      setTimelineWidth(timelineRef.current.offsetWidth);
    }
  }, [timelineRef.current]);

  if (isCreating) {
    return (
      <div className="relative py-4">
        <Skeleton.Node active className="h-28 w-full rounded-s" />
      </div>
    );
  }

  return (
    <div className="relative py-4">
      <div
        ref={timelineRef}
        className="h-28 cursor-pointer overflow-hidden rounded-s bg-neutral-100"
        onMouseEnter={() => setIsHover(true)}
        onMouseLeave={handleMouseLeave}
        onMouseMove={handleMouseMove}
      >
        <TimelineInteractions
          duration={duration}
          timelineStart={timelineStart}
          timelineEnd={timelineEnd}
          onUpdateTime={onUpdateTime}
          onSetIsHover={setIsHover}
        />

        {isHover && mouseX !== null && !isDragging && (
          <AddInteractionButton
            duration={duration}
            timelineStart={timelineStart}
            isOpen={isOpenAddInteraction}
            onOpenChange={setIsOpenAddInteraction}
            position={getAddInteractionButtonPosition()}
            onCreateInteraction={handleCreateInteraction}
          />
        )}
      </div>
    </div>
  );
}
