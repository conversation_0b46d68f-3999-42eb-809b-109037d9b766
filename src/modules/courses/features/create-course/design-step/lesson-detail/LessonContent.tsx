'use client';

import { useL<PERSON>onProvider } from '@/modules/courses/features/create-course/design-step/lesson-detail/LessonProvider';
import VideoContainer from '@/modules/courses/features/create-course/design-step/lesson-detail/VideoContainer';
import LessonEmptyContent from './LessonEmptyContent';
import LessonSettings from './lesson-settings/LessonSettings';

export default function LessonContent() {
  const { lessonDetail } = useLessonProvider();
  const hasVideo = !!lessonDetail?.videoId?.fileUrl;

  return (
    <div className="h-full min-h-fit">
      <div className="flex size-full gap-6">
        {hasVideo ? <VideoContainer /> : <LessonEmptyContent />}

        <LessonSettings />
      </div>
    </div>
  );
}
