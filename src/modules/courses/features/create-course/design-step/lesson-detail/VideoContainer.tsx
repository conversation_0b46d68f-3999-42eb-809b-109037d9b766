'use client';

import { Icon } from '@/components/client';
import { secondToHHMMSS } from '@/lib/dateTime';
import InteractionOverlay from '@/modules/courses/features/create-course/design-step/lesson-detail/InteractionOverlay';
import VideoPlayer from '@/modules/courses/features/create-course/design-step/lesson-detail/VideoPlayer';
import {
  calculateAbsoluteTime,
  calculateDurationAfterZoom,
  calculatePositionInPercent,
  calculateRelativePosition,
  calculateRelativePositionInViewport,
  calculateViewportScroll,
  handleRulerBackwardsExtension,
  handleRulerExtension,
} from '@/modules/courses/features/create-course/design-step/utils/video.util';
import { useLessonStore } from '@/z-store/lesson.store';
import { SparklesIcon } from '@heroicons/react/24/outline';
import React, { MouseEvent, useRef, useState } from 'react';
import { useLessonProvider } from './LessonProvider';
import { RulerAdjustment, VideoControl, VideoTime } from './VideoControls';
import VideoTimeline from './VideoTimeline';
import VideoTimelineCursor from './VideoTimelineCursor';
import VideoTimelineRuler from './VideoTimelineRuler';

const useVideoControls = ({ videoRef }: { videoRef: React.RefObject<HTMLVideoElement | null> }) => {
  const [isPlaying, setIsPlaying] = useState<boolean>(false);
  const [mute, setMute] = useState<boolean>(false);
  const [zoomLevel, setZoomLevel] = useState<number>(0);

  const handlePlayVideo = async () => {
    if (!videoRef.current) return;

    await videoRef.current.play();
    setIsPlaying(true);
  };

  const handlePauseVideo = () => {
    if (!videoRef.current) return;
    videoRef.current.pause();
    setIsPlaying(false);
  };

  const handleMuteVideo = () => {
    if (!videoRef.current) return;
    videoRef.current.muted = !videoRef.current.muted;
    setMute(!mute);
  };

  const handleShowFullScreen = async () => {
    if (!videoRef.current) return;
    await videoRef.current.requestFullscreen();
  };

  const handleSetZoomLevel = (value: number) => {
    setZoomLevel(value);
  };

  return {
    isPlaying,
    mute,
    zoomLevel,

    onPlayVideo: handlePlayVideo,
    onPauseVideo: handlePauseVideo,
    onMuteVideo: handleMuteVideo,
    onShowFullScreen: handleShowFullScreen,
    onSetZoomLevel: handleSetZoomLevel,
  };
};

const useVideoContainer = () => {
  const { lessonDetail } = useLessonProvider();
  const totalDuration = lessonDetail?.videoId?.fileDuration || 0;

  const timelineRef = useRef<HTMLDivElement | null>(null);
  const videoRef = useRef<HTMLVideoElement | null>(null);

  const [position, setPosition] = useState<number>(0);
  const [isDragging, setIsDragging] = useState<boolean>(false);
  const [duration, setDuration] = useState<number>(totalDuration);

  const { zoomLevel, isPlaying, mute, onMuteVideo, onPauseVideo, onPlayVideo, onShowFullScreen, onSetZoomLevel } =
    useVideoControls({
      videoRef,
    });

  const [rulerStart, setRulerStart] = useState<number>(0);
  const [rulerEnd, setRulerEnd] = useState<number>(totalDuration);

  const timeline = timelineRef.current;
  const video = videoRef.current;

  const currentTime = video?.currentTime ?? 0;

  const setCurrentTime = (time: number) => {
    if (video) {
      video.currentTime = time;
    }
  };

  const renderVideoTime = () => {
    return `${secondToHHMMSS(currentTime)} / ${secondToHHMMSS(totalDuration)}`;
  };

  const handleUpdatePositionIndicator = ({ duration, time }: { duration: number; time: number }) => {
    const newPosition = calculatePositionInPercent({ time, duration });

    setDuration(duration);
    setPosition(newPosition);
    setCurrentTime(time);
  };

  const handleSetZoomLevel = (value: number) => {
    onSetZoomLevel(value);

    const { displayedDuration, rulerStart, rulerEnd } = calculateDurationAfterZoom({
      duration: totalDuration,
      zoomLevel: value,
      cursorPosition: position,
      currentTime: currentTime,
    });

    setDuration(displayedDuration);
    setRulerStart(rulerStart);
    setRulerEnd(rulerEnd);

    const newPosition = calculateRelativePosition({
      absoluteTime: currentTime,
      rulerStart: rulerStart,
      displayedDuration,
    });

    setPosition(newPosition);
  };

  const handleDrag = (e: MouseEvent<HTMLDivElement>): void => {
    e.stopPropagation();
    if (!isDragging || !timeline) return;

    const rect = timeline.getBoundingClientRect();
    const relativePosition = Math.max(0, Math.min(100, ((e.clientX - rect.left) / rect.width) * 100));

    const displayedDuration = rulerEnd - rulerStart;

    const CURSOR_END_THRESHOLD = 99; // Percentage threshold to trigger ruler extension
    const shouldExtendRuler = relativePosition > CURSOR_END_THRESHOLD;

    // If cursor reaches near the end, extend the ruler
    if (shouldExtendRuler) {
      const newRelativePosition = Math.max(0, Math.min(100, ((e.clientX - rect.left) / rect.width) * 100));
      setPosition(newRelativePosition);

      handleRulerExtension({
        displayedDuration,
        totalDuration,
        rulerEnd,
        rulerStart,
        setRulerEnd,
        setRulerStart,
      });
    }

    // If cursor reaches near the start, extend the ruler backwards
    const CURSOR_START_THRESHOLD = 5; // Percentage threshold to trigger ruler extension backwards
    const shouldExtendRulerBackwards = relativePosition < CURSOR_START_THRESHOLD;
    if (shouldExtendRulerBackwards) {
      handleRulerBackwardsExtension({
        displayedDuration,
        rulerStart,
        setRulerStart,
      });
    }

    const absoluteTime = calculateAbsoluteTime({
      relativePosition,
      rulerStart: rulerStart,
      displayedDuration,
    });

    setPosition(relativePosition);

    if (video) {
      const time = Math.max(0, Math.min(totalDuration, absoluteTime));
      setCurrentTime(time);
      video.currentTime = time;
    }
  };

  const handleUpdateCurrentTime = ({
    time,
    duration,
    rulerStart: clickRulerStart,
  }: {
    time: number;
    duration: number;
    rulerStart?: number;
  }) => {
    if (!video || !timeline) return;

    const newPosition =
      clickRulerStart !== undefined
        ? calculateRelativePositionInViewport({
            absoluteTime: time,
            rulerStart: clickRulerStart,
            displayedDuration: duration,
          })
        : calculatePositionInPercent({ time, duration });

    setCurrentTime(time);
    setDuration(duration);
    setPosition(newPosition);
  };

  const handleDragStart = (): void => {
    setIsDragging(true);
    onPauseVideo();
  };

  const handleDragEnd = async (): Promise<void> => {
    if (isDragging) {
      setIsDragging(false);
      await onPlayVideo();
    }
  };

  React.useEffect(() => {
    if (!video) return;

    const updatePosition = () => {
      const displayedDuration = rulerEnd - rulerStart;

      // Check if viewport should scroll when video is playing
      if (isPlaying && !isDragging) {
        const scrollResult = calculateViewportScroll({
          currentTime,
          rulerStart,
          rulerEnd,
          displayedDuration,
          totalDuration,
        });

        const scrollThreshold = rulerStart + displayedDuration;
        if (currentTime >= scrollThreshold && scrollResult.shouldScroll) {
          setRulerStart(scrollResult.rulerStart);
          setRulerEnd(scrollResult.rulerEnd);
        }
      }

      const newPosition = calculateRelativePosition({
        absoluteTime: currentTime,
        rulerStart: rulerStart,
        displayedDuration,
      });

      setPosition(newPosition);
    };

    video.addEventListener('timeupdate', updatePosition);
    return () => video.removeEventListener('timeupdate', updatePosition);
  }, [duration, currentTime, isPlaying, rulerStart, rulerEnd]);

  React.useEffect(() => {
    const { displayedDuration, rulerStart, rulerEnd } = calculateDurationAfterZoom({
      duration: totalDuration,
      zoomLevel: zoomLevel,
      cursorPosition: position,
      currentTime: currentTime,
    });

    setRulerStart(rulerStart);
    setRulerEnd(rulerEnd);
    setDuration(displayedDuration);
  }, [totalDuration, zoomLevel]);

  return {
    // State
    position,
    isDragging,
    timelineRef,
    videoRef,
    duration,
    currentTime,
    isPlaying,
    mute,
    zoomLevel,
    rulerStart,
    rulerEnd,

    // Setters
    setPosition,
    onUpdateDuration: setDuration,
    onSetCurrentTime: setCurrentTime,

    // Handlers
    onUpdatePositionIndicator: handleUpdatePositionIndicator,
    onUpdateCurrentTime: handleUpdateCurrentTime,
    onDrag: handleDrag,
    onDragStart: handleDragStart,
    onDragEnd: handleDragEnd,
    onSetZoomLevel: handleSetZoomLevel,
    onPlayVideo,
    onPauseVideo,
    onMuteVideo,
    onShowFullScreen,
    renderVideoTime,
  };
};

export default function VideoContainer() {
  const isOpenInteractionType = useLessonStore((state) => state.settings.isOpenInteractionType);

  const {
    duration,
    position,
    timelineRef,
    videoRef,
    isDragging,
    currentTime,
    isPlaying,
    mute,
    zoomLevel,
    rulerStart,
    rulerEnd,

    onDrag,
    onDragStart,
    onDragEnd,
    onUpdateCurrentTime,
    onSetZoomLevel,
    onPlayVideo,
    onPauseVideo,
    onMuteVideo,
    onShowFullScreen,
    renderVideoTime,
  } = useVideoContainer();

  return (
    <div className="size-full rounded-lg border border-neutral-200 bg-white pb-20">
      <div className="relative">
        <VideoPlayer videoRef={videoRef} onPlay={onPlayVideo} onPause={onPauseVideo} />

        <InteractionOverlay type={isOpenInteractionType} />
      </div>

      <div className="flex items-center justify-between p-4">
        <RulerAdjustment value={zoomLevel} onChange={onSetZoomLevel} />

        <VideoTime
          isPlaying={isPlaying}
          onPauseVideo={onPauseVideo}
          onPlayVideo={onPlayVideo}
          renderVideoTime={renderVideoTime}
        />

        <VideoControl mute={mute} onMuteVideo={onMuteVideo} onShowFullScreen={onShowFullScreen} />
      </div>

      <div className="flex h-full max-h-44">
        <div className="flex flex-col items-center border-b border-neutral-200">
          <div className="h-full max-h-[31px] border-b border-neutral-200" />
          <div className="flex h-full items-center border-r border-t border-neutral-200 px-4">
            <Icon icon={<SparklesIcon className="size-6" />} />
          </div>
        </div>
        <div
          className="relative w-full border-b border-neutral-200 bg-white px-4"
          onMouseMove={onDrag}
          onMouseUp={onDragEnd}
          onMouseLeave={onDragEnd}
        >
          <div className="flex h-full flex-col justify-center">
            <div className="-mx-4 border-b border-neutral-200 px-4">
              <VideoTimelineRuler
                calculatedDuration={Math.floor(rulerEnd - rulerStart)}
                zoomLevel={zoomLevel}
                cursorPosition={position}
                currentTime={currentTime}
                rulerStart={rulerStart}
                rulerEnd={rulerEnd}
                onUpdateTime={onUpdateCurrentTime}
              />
            </div>

            <div className="relative">
              <VideoTimeline
                timelineRef={timelineRef}
                position={position}
                isDragging={isDragging}
                timelineStart={rulerStart}
                timelineEnd={rulerEnd}
                duration={duration}
                onUpdateTime={onUpdateCurrentTime}
              />
            </div>
          </div>

          <VideoTimelineCursor position={position} onDragStart={onDragStart} />
        </div>
      </div>
    </div>
  );
}
