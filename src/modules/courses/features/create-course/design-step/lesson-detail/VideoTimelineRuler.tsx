'use client';

import { Typography } from '@/components/ui';
import { secondToHHMMSS } from '@/lib/dateTime';
import { cn } from '@/lib/utils';
import { getTimeAfterRulerChange } from '@/modules/courses/features/create-course/design-step/utils/video.util';
import React from 'react';
import { useLessonProvider } from './LessonProvider';

type Props = {
  zoomLevel: number;
  cursorPosition: number;
  rulerStart: number;
  rulerEnd: number;
  currentTime: number;
  calculatedDuration: number;

  onUpdateTime: ({ duration, time, rulerStart }: { duration: number; time: number; rulerStart?: number }) => void;
};

const TOTAL_INTERVALS = 30;
const MAJOR_INTERVAL_COUNT = 5;

const useTimelineRuler = ({ calculatedDuration, rulerStart }: { calculatedDuration: number; rulerStart: number }) => {
  const { lessonDetail } = useLessonProvider();

  const videoDuration = lessonDetail.videoId?.fileDuration || 0;

  const handleTimeUpdate = (event: React.MouseEvent<HTMLDivElement>) => {
    const relativeTime = getTimeAfterRulerChange({ event, calculatedDuration });
    const absoluteTime = Math.max(0, Math.min(videoDuration, rulerStart + relativeTime));

    return { duration: calculatedDuration, time: absoluteTime, rulerStart };
  };

  return {
    calculatedDuration,
    handleTimeUpdate,
  };
};

export default function VideoTimelineRuler(props: Props) {
  const { calculatedDuration, rulerStart, onUpdateTime } = props;

  const { handleTimeUpdate } = useTimelineRuler({ calculatedDuration, rulerStart });

  const intervalSeconds = calculatedDuration / TOTAL_INTERVALS;

  const handleRulerClick = (event: React.MouseEvent<HTMLDivElement>) => {
    const timeUpdate = handleTimeUpdate(event);
    onUpdateTime(timeUpdate);
  };

  return (
    <div className="flex w-full items-center">
      <div className="flex size-full h-8 items-center">
        <div className="relative flex size-full cursor-pointer items-end" onClick={handleRulerClick}>
          {Array.from({ length: TOTAL_INTERVALS + 1 }).map((_, index) => {
            const isMajor = index % MAJOR_INTERVAL_COUNT === 0;
            const isLastMajor = isMajor && index === TOTAL_INTERVALS;

            const seconds = rulerStart + index * intervalSeconds;
            const label = secondToHHMMSS(seconds);

            return (
              <div
                key={index}
                className={cn(
                  'flex cursor-pointer flex-col items-start',
                  index === TOTAL_INTERVALS ? 'w-0' : 'w-full',
                  isLastMajor && 'items-end',
                )}
                data-time={seconds}
              >
                {isMajor && (
                  <div className="relative top-0">
                    <Typography className={cn('break-normal')} variant="labelXs">
                      {label}
                    </Typography>
                  </div>
                )}
                <div className={cn('w-0.5 bg-neutral-200', isMajor ? 'h-3' : 'h-1.5')} />
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}
