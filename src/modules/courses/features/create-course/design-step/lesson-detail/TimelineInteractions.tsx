'use client';

import { Typography } from '@/components/ui';
import { cn } from '@/lib/utils';
import { InteractionType } from '@/modules/courses/constants/course.const';
import { useLessonProvider } from '@/modules/courses/features/create-course/design-step/lesson-detail/LessonProvider';
import { LectureInteract } from '@/modules/courses/types/interaction.type';
import { createDefaultQuestion, Question, useLessonStore } from '@/z-store/lesson.store';
import { GlobeAltIcon, QuestionMarkCircleIcon } from '@heroicons/react/16/solid';

type TimelineInteractionsProps = {
  duration: number;
  timelineStart: number;
  timelineEnd: number;
  onSetIsHover?: (isHover: boolean) => void;
  onUpdateTime: ({ duration, time, rulerStart }: { duration: number; time: number; rulerStart?: number }) => void;
};

const getInteractionType = (type: number) => {
  if (type === 2) {
    return InteractionType.QuickQuestion;
  }

  return InteractionType.Explore;
};

const getInteractions = ({
  duration,
  timelineStart,
  timelineEnd,
  list,
}: {
  duration: number;
  timelineStart: number;
  timelineEnd: number;
  list: LectureInteract[];
}) => {
  const interactions =
    list
      ?.filter((item) => {
        // Only show interactions that are within the current viewport
        return item.startAt >= timelineStart && item.startAt <= timelineEnd;
      })
      .map((item) => {
        // Calculate position relative to current viewport
        const relativeTime = item.startAt - timelineStart;
        const position = Math.floor((relativeTime / duration) * 100);

        return {
          ...item,
          position,
          interactType: getInteractionType(item.interactTypeId),
        };
      }) || [];

  return interactions;
};

export default function TimelineInteractions(props: TimelineInteractionsProps) {
  const { duration, timelineStart, timelineEnd, onSetIsHover = () => {}, onUpdateTime } = props;

  const updateSettings = useLessonStore((state) => state.updateSettings);
  const updateInteraction = useLessonStore((state) => state.updateInteraction);

  const { lessonDetail } = useLessonProvider();

  const interactions = getInteractions({
    duration,
    timelineStart,
    timelineEnd,
    list: lessonDetail?.lectureInteracts || [],
  });
  const hasInteraction = !!interactions.length;

  const handleOpenInteractionItem = (interaction: LectureInteract) => {
    const { id, interactType } = interaction;
    onUpdateTime({ duration, time: interaction.startAt, rulerStart: timelineStart });
    updateSettings({ isOpenInteractionType: interactType, interactionId: id });
    updateInteraction({
      ...interaction,
      question: (interaction as LectureInteract & { question?: Question })?.question || createDefaultQuestion(),
    });
  };

  return (
    <div className="relative h-full">
      {hasInteraction ? (
        interactions.map((item) => {
          const isQuestionType = item.interactType === InteractionType.QuickQuestion;
          const isExploreType = item.interactType === InteractionType.Explore;

          return (
            <div
              key={item.id}
              className="absolute inset-y-0 z-50 flex -translate-x-1/3 cursor-pointer items-center justify-center"
              style={{ left: `${item.position}%` }}
              onClick={() => handleOpenInteractionItem(item)}
              onMouseEnter={() => onSetIsHover(false)}
              onMouseLeave={() => onSetIsHover(true)}
            >
              <div
                className={cn(
                  'flex items-center justify-center rounded-full bg-white p-2 shadow-light',
                  isQuestionType && 'bg-pink-500',
                  isExploreType && 'bg-primary-500',
                )}
              >
                {isQuestionType ? (
                  <QuestionMarkCircleIcon className={cn('size-6 rounded-full bg-pink-500 fill-white')} />
                ) : (
                  <GlobeAltIcon className={cn('size-6 rounded-full bg-primary-500 fill-white')} />
                )}
              </div>
            </div>
          );
        })
      ) : (
        <div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2">
          <div className="flex justify-center">
            <Typography variant="labelLg" className="text-secondary_text">
              Nhấn vào đây để thêm tương tác
            </Typography>
          </div>
        </div>
      )}
    </div>
  );
}
