'use client';

import { Button, Typography } from '@/components/ui';
import { routePaths } from '@/config';
import { QUERY_KEYS } from '@/constants/query-keys';
import useSafeSearchParams from '@/hooks/useSafeSearchParams';
import { getLessonById } from '@/modules/courses/services/course.service';
import { formatApiUrl } from '@/utils/url.util';
import { useParams, useRouter } from 'next/navigation';
import queryString from 'query-string';
import { useQuery } from 'react-query';
import LessonContent from './LessonContent';
import { LessonContextValue, LessonProvider } from './LessonProvider';
import LessonTitle from './LessonTitle';

export default function LessonContainer() {
  const { courseId } = useParams<{ courseId: string }>();

  const router = useRouter();

  const { parsedQueryParams } = useSafeSearchParams<{ sectionId: string; lessonId: string }>();
  const { sectionId, lessonId } = parsedQueryParams;

  const { data: lesson } = useQuery({
    queryKey: [QUERY_KEYS.LECTURE_DETAIL, courseId, sectionId, lessonId],
    queryFn: () => getLessonById({ courseId, sectionId, lectureId: lessonId }),
    suspense: true,
  });

  const contextValue = {
    lessonDetail: lesson!,
  } satisfies LessonContextValue;

  const handlePreview = () => {
    const url = queryString.stringifyUrl({
      url: formatApiUrl(routePaths.course.preview, { courseId }),
      query: { sectionId, lessonId },
    });

    router.push(url);
  };

  return (
    <div className="flex h-full flex-col gap-4 overflow-y-auto px-8 py-10">
      <LessonProvider value={contextValue}>
        <LessonTitle
          actions={
            <Button variant="secondary" size="large" onClick={handlePreview}>
              <Typography variant="labelLg" className="text-primary-500">
                Xem trước
              </Typography>
            </Button>
          }
        />

        <LessonContent />
      </LessonProvider>
    </div>
  );
}
