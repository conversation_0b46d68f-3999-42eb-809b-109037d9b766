export default function VideoTimelineCursor({ position, onDragStart }: { position: number; onDragStart: () => void }) {
  return (
    <div
      className="relative -top-full z-50 h-full w-[2px] cursor-grab select-none rounded-full bg-primary-600"
      style={{ left: `${position}%` }}
      onMouseDown={onDragStart}
    >
      <div
        className="absolute -top-1 left-1/2 size-2 rounded-full bg-primary-600"
        style={{ transform: 'translateX(-50%)' }}
      />
    </div>
  );
}
