import React from 'react';
import { useLessonProvider } from './LessonProvider';

type VideoPlayerProps = {
  videoRef: React.RefObject<HTMLVideoElement | null>;
  onPlay: () => void;
  onPause: () => void;
};

export default function VideoPlayer(props: VideoPlayerProps) {
  const { videoRef, onPlay, onPause } = props;

  const { lessonDetail } = useLessonProvider();

  const videoUrl = lessonDetail?.videoId?.fileUrl;

  if (!videoUrl) return null;

  return (
    <div className="aspect-video w-full overflow-hidden px-4 pt-4">
      <video
        preload="metadata"
        ref={videoRef}
        src={videoUrl}
        className="size-full rounded-lg object-cover"
        onPlay={onPlay}
        onPause={onPause}
        playsInline
      >
        <source src={videoUrl} type="video/mp4" />
        <source src={videoUrl} type="video/mov" />
        Your browser does not support the video tag.
      </video>
    </div>
  );
}
