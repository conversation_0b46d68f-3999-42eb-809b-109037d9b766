'use client';

import { Icon } from '@/components/client';
import { Dropdown } from '@/components/ui';
import { palette } from '@/config/theme';
import { secondToHHMMSS } from '@/lib/dateTime';
import { cn } from '@/lib/utils';
import { InteractionType } from '@/modules/courses/constants/course.const';
import { calculateTime } from '@/modules/courses/features/create-course/design-step/utils/video.util';
import { GlobeAltIcon, PlusIcon, QuestionMarkCircleIcon } from '@heroicons/react/16/solid';
import { MenuProps } from 'antd';

type AddInteractionButtonProps = {
  duration: number;
  position: number;
  timelineStart: number;
  isOpen: boolean;

  onOpenChange: (open: boolean) => void;
  onCreateInteraction: (type: InteractionType) => void;
};

export default function AddInteractionButton(props: AddInteractionButtonProps) {
  const { isOpen, duration, position, timelineStart, onOpenChange, onCreateInteraction } = props;

  const relativeTime = calculateTime({ position, duration });
  const absoluteTime = timelineStart + relativeTime;

  const interactionItems = [
    {
      key: InteractionType.QuickQuestion,
      label: 'Câu hỏi nhanh',
      icon: (
        <div className="flex items-center justify-center">
          <QuestionMarkCircleIcon className="size-6 rounded-full bg-white" fill={palette.pink[500]} />
        </div>
      ),
      onClick: () => onCreateInteraction(InteractionType.QuickQuestion),
    },
    {
      key: InteractionType.Explore,
      label: 'Khám phá',
      icon: (
        <div className="flex items-center justify-center">
          <GlobeAltIcon className="size-6 rounded-full bg-white" fill={palette.primary[500]} />
        </div>
      ),
      onClick: () => onCreateInteraction(InteractionType.Explore),
    },
  ] as MenuProps['items'];

  return (
    <div
      className={cn(
        'absolute flex w-2 translate-y-0 transform items-center justify-center',
        'border-1 h-4/5 cursor-pointer border-neutral-200 bg-white',
        'top-1/2 z-20 -translate-y-1/2',
      )}
      style={{ left: `calc(${position}%` }}
      onClick={(e) => {
        e.stopPropagation();
      }}
    >
      {/* Tooltip time */}
      <div
        className={cn(
          'absolute -top-8 left-1/2 -translate-x-1/2 rounded-lg bg-black px-2 py-1 text-sm text-white',
          'after:absolute after:left-1/2 after:top-full after:-translate-x-1/2 after:border-4 after:border-transparent after:border-t-black',
        )}
      >
        {secondToHHMMSS(absoluteTime)}
      </div>

      <Dropdown
        menu={{ items: interactionItems }}
        trigger={['click']}
        overlayClassName="w-48"
        open={isOpen}
        onOpenChange={onOpenChange}
      >
        <div className="absolute flex items-center justify-center rounded-full border-neutral-200 bg-white p-3">
          <div className="flex items-center justify-center rounded-full bg-neutral-600 p-0.5">
            <Icon icon={<PlusIcon />} className="size-5 text-white" />
          </div>
        </div>
      </Dropdown>
    </div>
  );
}
