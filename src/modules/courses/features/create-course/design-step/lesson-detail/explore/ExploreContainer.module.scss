.editor {
  position: relative;
  border: 1px solid #d9d9d9;
  border-radius: 8px;

  :global(.ql-container) {
    resize: vertical;
    overflow-y: auto;
    min-height: 120px;
    max-height: 300px;

    :global(.ql-editor) {
      min-height: 128px;
      padding-block: 8px;
      padding-inline: 16px;
      font-size: 1rem;
      color: #0c0c0c;
    }

    :global(.ql-blank)::before {
      color: #666;
      font-style: normal;
      font-size: 1rem;
      font-weight: 500;
    }

    :global(.ql-tooltip-arrow) {
      display: none !important;
    }

    :global(.ql-tooltip) {
      :global(#toolbar) {
        display: flex;
        align-items: center;
        width: fit-content !important;
        height: 40px;
        padding-inline: 16px;
        border-radius: 8px;
      }
    }
  }
}
