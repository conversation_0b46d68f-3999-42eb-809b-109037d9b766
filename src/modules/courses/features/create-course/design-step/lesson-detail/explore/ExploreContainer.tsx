'use client';

import { XMarkIcon } from '@heroicons/react/24/outline';
import { Form } from 'antd';
import { useEffect, useRef, useState } from 'react';

import { Icon } from '@/components/client';
import { Button, Input, RichTextEditor, Typography } from '@/components/ui';
import { LibraryModal } from '@/modules/courses/components';
import { InteractionType } from '@/modules/courses/constants/course.const';
import { LibraryFileType } from '@/modules/courses/constants/file.const';
import { useLessonProvider } from '@/modules/courses/features/create-course/design-step/lesson-detail/LessonProvider';
import { LibraryFile } from '@/modules/courses/types/file.type';
import { downloadFile } from '@/utils';
import { useLessonStore } from '@/z-store/lesson.store';
import { ArrowUpTrayIcon, PaperClipIcon } from '@heroicons/react/16/solid';
import { TrashIcon } from '@heroicons/react/24/solid';
import 'react-quill-new/dist/quill.bubble.css';
import useInteractionActions from '../../hooks/useInteractionActions';

type FormData = {
  description: string;
  attached_files: LibraryFile[];
};

const ExploreContainer = () => {
  const { lessonDetail } = useLessonProvider();

  const settings = useLessonStore((state) => state.settings);
  const resetSettings = useLessonStore((state) => state.resetSettings);

  const interaction = lessonDetail?.lectureInteracts?.find((item) => item.id === settings.interactionId);
  const explore = interaction?.explore;

  const [form] = Form.useForm<FormData>();

  const [openLibraryModal, setOpenLibraryModal] = useState(false);

  const quillRef = useRef<any>(null);
  const containerRef = useRef<HTMLElement>(null!);

  const { onUpdateInteractionContent } = useInteractionActions();

  const description = Form.useWatch('description', form) || '';
  const attachedFiles = Form.useWatch('attached_files', form) || [];

  const modules = {
    toolbar: '#toolbar',
  };

  const handleCloseInteraction = () => {
    resetSettings();
  };

  const handleAddFiles = (files: LibraryFile[]) => {
    if (!interaction?.id) return;

    onUpdateInteractionContent({
      interactId: interaction.id,
      content: {
        interact_type: InteractionType.Explore,
        description,
        attached_files: Array.from(new Set([...attachedFiles, ...files].map((file) => file.id))),
      },
    });
    setOpenLibraryModal(false);
  };

  const handleDownload = async (file: LibraryFile) => {
    await downloadFile(file.fileUrl, file.fileName);
  };

  const handleSubmit = (values: FormData) => {
    if (interaction) {
      onUpdateInteractionContent({
        interactId: interaction.id,
        content: {
          interact_type: InteractionType.Explore,
          description: values.description,
          attached_files: values.attached_files.map((file) => file.id),
        },
      });
    }
  };

  const handleDescriptionBlur = () => {
    form.validateFields(['description']).then(() => {
      handleSubmit({
        description: description,
        attached_files: attachedFiles,
      });
    });
  };

  useEffect(() => {
    const adjustTooltipPosition = () => {
      const tooltip = document.querySelector('.ql-tooltip') as HTMLElement | null;
      if (tooltip) {
        const left = parseFloat(tooltip.style.left) || 0;
        if (left < 0) {
          tooltip.style.left = '10px';
        }
      }
    };

    const observer = new MutationObserver(adjustTooltipPosition);
    const editorContainer = document.querySelector('.ql-container');

    if (editorContainer) {
      observer.observe(editorContainer, {
        childList: true,
        subtree: true,
        attributes: true,
      });
    }

    return () => {
      observer.disconnect();
    };
  }, []);

  useEffect(() => {
    const quill = quillRef.current?.getEditor();
    if (!quill) return;

    const editorEl = document.querySelector('.ql-editor');

    const handleClick = () => {
      quill.theme.tooltip.show('link');
    };

    editorEl?.addEventListener('click', handleClick);

    return () => {
      editorEl?.removeEventListener('click', handleClick);
    };
  }, []);

  useEffect(() => {
    const values = {
      description: explore?.description,
      attached_files: explore?.attachFiles,
    };
    form.setFieldsValue(values);
  }, [explore]);

  return (
    <>
      <section ref={containerRef} className="flex h-full flex-col gap-4 overflow-y-auto p-6">
        <header className="flex items-center justify-between">
          <Typography variant="headlineXs">Khám phá</Typography>
          <Button
            variant="ghost-reversed"
            icon={<XMarkIcon className="size-6 text-base-black-100" />}
            onClick={handleCloseInteraction}
          />
        </header>

        <Form
          form={form}
          initialValues={{ description: '', attached_files: [] }}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            label={<Typography variant="labelMd">Nội dung</Typography>}
            name="description"
            rules={[{ max: 500, message: 'Nội dung không được vượt quá 500 ký tự' }]}
            getValueFromEvent={(content: string) => content}
            getValueProps={(value: string) => ({ content: value })}
          >
            <RichTextEditor
              maxLength={500}
              placeholder="Nhập nội dung..."
              classNames={{
                root: 'focus-within:ring-0',
                editor: 'border-neutral-5 rounded-lg border min-h-[152px] max-h-[300px] resize-y overflow-y-auto',
              }}
              onBlur={handleDescriptionBlur}
            />
          </Form.Item>

          <Form.Item label={<Typography variant="labelMd">Học liệu</Typography>}>
            <div>
              <Button variant="tertiary" startIcon={<ArrowUpTrayIcon />} onClick={() => setOpenLibraryModal(true)}>
                Tải tệp lên
              </Button>
              <div className="mt-2">
                {attachedFiles?.length > 0 ? (
                  <ul className="flex flex-col gap-1">
                    {attachedFiles.map((file: LibraryFile) => (
                      <li
                        key={file.id}
                        className="group flex items-center justify-between gap-2 p-1 hover:bg-neutral-50"
                      >
                        <div className="flex cursor-pointer items-center gap-2" onClick={() => handleDownload(file)}>
                          <Icon size="sm" icon={<PaperClipIcon className="text-neutral-500" />} />
                          <Typography className="text-sm text-blue-500">{file.fileName}</Typography>
                        </div>
                        <Icon
                          size="sm"
                          icon={<TrashIcon className="text-neutral-500" />}
                          className="invisible cursor-pointer text-neutral-400 hover:text-neutral-600 group-hover:visible"
                          onClick={(e) => {
                            e.stopPropagation();
                            if (interaction?.id) {
                              const updatedFiles = attachedFiles.filter((f) => f.id !== file.id);
                              onUpdateInteractionContent({
                                interactId: interaction.id,
                                content: {
                                  interact_type: InteractionType.Explore,
                                  description,
                                  attached_files: updatedFiles.map((f) => f.id),
                                },
                              });
                              form.setFieldValue('attached_files', updatedFiles);
                            }
                          }}
                        />
                      </li>
                    ))}
                  </ul>
                ) : (
                  <div>
                    <Typography variant="bodySm">Dung lượng tệp tối đa: 20MB</Typography>
                    <Typography variant="bodySm" className="block">
                      Cho phép tải lên các tệp có đuôi: .pdf, .doc, .xls, .png, .jpeg
                    </Typography>
                  </div>
                )}
              </div>
            </div>
          </Form.Item>

          <Form.Item hidden name="attached_files">
            <Input />
          </Form.Item>
        </Form>
      </section>

      <LibraryModal
        open={openLibraryModal}
        enabledTypes={[LibraryFileType.IMAGE, LibraryFileType.DOCUMENT]}
        title="Chọn hình ảnh/học liệu"
        onClose={() => setOpenLibraryModal(false)}
        selectMode={{
          [LibraryFileType.IMAGE]: 'multiple',
          [LibraryFileType.DOCUMENT]: 'multiple',
        }}
        onAddFiles={handleAddFiles}
      />
    </>
  );
};

export { ExploreContainer };
