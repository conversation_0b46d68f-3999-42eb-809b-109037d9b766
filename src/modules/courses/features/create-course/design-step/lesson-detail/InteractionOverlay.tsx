'use client';

import { InteractionType } from '../../../../constants/course.const';
import { ExploreContainer } from './explore/ExploreContainer';
import QuickQuestionContainer from './quick-question/QuickQuestionContainer';

export default function InteractionOverlay({ type }: { type: InteractionType }) {
  if (type === InteractionType.Default) return null;

  const components = {
    [InteractionType.QuickQuestion]: <QuickQuestionContainer key={type} />,
    [InteractionType.Explore]: <ExploreContainer key={type} />,
  };

  return (
    <div className="absolute left-1/2 top-1/2 size-11/12 -translate-x-1/2 -translate-y-1/2 rounded-lg bg-white">
      {[components[type]]}
    </div>
  );
}
