'use client';

import { Icon } from '@/components/client';
import { Button, ButtonProps, Steps, StepsProps, Typography } from '@/components/ui';
import { routePaths } from '@/config';
import { cn } from '@/lib/utils';
import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/outline';
import { useRouter } from 'next/navigation';
import { CourseActionContainer } from '../../../../../../components/container/course-action-container';

type Step = {
  title: string;
  description: string;
};

type Props = {
  stepIndex?: number;
  stepList?: Step[];
  slotProps?: {
    setupItem?: NonNullable<StepsProps['items']>[number];
    designItem?: NonNullable<StepsProps['items']>[number];
    publishItem?: NonNullable<StepsProps['items']>[number];

    backButton?: ButtonProps;
    nextButton?: ButtonProps;
    publishButton?: ButtonProps;
  };

  onNext?: () => void;
  onBack?: () => void;
  onPublish?: () => void;
  onStepChange?: (step: number) => void;
  onBackToCreatorList?: () => void;
  hasUnsavedChanges?: boolean;
};

export default function CreateCourseHeader(props: Props) {
  const {
    stepIndex = 0,
    slotProps,
    onNext,
    onBack,
    onPublish,
    onStepChange = () => {},
    onBackToCreatorList,
    hasUnsavedChanges = false,
  } = props;

  const router = useRouter();

  const handleBackToCreatorList = () => {
    if (onBackToCreatorList) {
      onBackToCreatorList();
    } else if (hasUnsavedChanges) {
      const confirmed = window.confirm('Bạn có thay đổi chưa được lưu. Bạn có chắc chắn muốn rời khỏi trang này?');
      if (confirmed) {
        router.push(routePaths.profile.children.creator.path);
      }
    } else {
      router.push(routePaths.profile.children.creator.path);
    }
  };

  return (
    <div className="h-[72px] w-full bg-white px-6">
      <div className="flex items-center justify-between">
        <Button variant="tertiary" className="border-none" onClick={handleBackToCreatorList}>
          <div className="flex gap-2">
            <Icon icon={<ChevronLeftIcon className="size-6" />} />
            <Typography variant="labelLg">Trở về</Typography>
          </div>
        </Button>

        <CourseActionContainer className="w-full">
          <Steps
            type="navigation"
            size="small"
            status="process"
            current={stepIndex}
            onChange={onStepChange}
            items={[
              {
                title: 'Thiết lập',
                description: (
                  <Typography variant="bodyMd" className="text-secondary_text">
                    Thông tin cơ bản khóa học
                  </Typography>
                ),
                ...slotProps?.setupItem,
                className: cn('w-full max-w-full', slotProps?.setupItem?.className),
              },
              {
                title: 'Thiết kế',
                description: (
                  <Typography variant="bodyMd" className="text-secondary_text">
                    Cấu trúc và chi tiết khoá học
                  </Typography>
                ),
                ...slotProps?.designItem,
                className: cn('w-full max-w-full', slotProps?.designItem?.className),
              },
              {
                title: 'Xuất bản',
                description: (
                  <Typography variant="bodyMd" className="text-secondary_text">
                    Khoá học đóng gói hoàn chỉnh
                  </Typography>
                ),
                ...slotProps?.publishItem,
                className: cn('w-full max-w-full', slotProps?.publishItem?.className),
              },
            ]}
          />
        </CourseActionContainer>

        <div className="flex items-center gap-2">
          {onBack && (
            <Button variant="tertiary" onClick={onBack} {...slotProps?.backButton}>
              Trở lại
            </Button>
          )}

          {onNext && (
            <Button onClick={onNext} {...slotProps?.nextButton}>
              <div className="flex items-center gap-2">
                <Typography variant="labelLg" className="text-white">
                  Tiếp tục
                </Typography>
                <Icon className="flex items-center" icon={<ChevronRightIcon className="size-6" />} />
              </div>
            </Button>
          )}
          {onPublish && (
            <Button onClick={onPublish} {...slotProps?.publishButton}>
              Xuất bản
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}
