import { routePaths } from '@/config';
import { formatApiUrl } from '@/utils/url.util';
import { useParams, useRouter } from 'next/navigation';

const useNavigateSteps = () => {
  const router = useRouter();

  const params = useParams<{ courseId: string }>();
  const courseId = params.courseId;

  const handleBackToCreatorList = () => router.push(routePaths.profile.children.creator.path);

  const handleBackToSetupStep = () => router.push(formatApiUrl(routePaths.course.editInfo, { courseId }));

  const handleBackToDesignStep = () => router.push(formatApiUrl(routePaths.course.design, { courseId }));

  return {
    onBackDesignStep: handleBackToDesignStep,
    onBackToCreatorList: handleBackToCreatorList,
    onBackSetupStep: handleBackToSetupStep,
  };
};

export default useNavigateSteps;
