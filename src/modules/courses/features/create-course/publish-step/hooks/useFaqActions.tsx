import { QUERY_KEYS } from '@/constants/query-keys';
import {
  createFaqService,
  deleteFaqService,
  editFaqService,
  swapFaqService,
} from '@/modules/courses/services/course.service';
import { useMutation, useQueryClient } from 'react-query';

const useFaqActions = () => {
  const queryClient = useQueryClient();

  const createFaqMutation = useMutation({
    mutationFn: createFaqService,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.COURSE_DETAIL] });
    },
  });

  const editFaqMutation = useMutation({
    mutationFn: editFaqService,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.COURSE_DETAIL] });
    },
  });

  const deleteFaqMutation = useMutation({
    mutationFn: deleteFaqService,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.COURSE_DETAIL] });
    },
  });

  const swapFaqMutation = useMutation({
    mutationFn: swapFaqService,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.COURSE_DETAIL] });
    },
  });

  return {
    onCreateFaq: createFaqMutation.mutate,
    onEditFaq: editFaqMutation.mutate,
    onDeleteFaq: deleteFaqMutation.mutate,
    onSwapFaq: swapFaqMutation.mutate,
  };
};

export default useFaqActions;
