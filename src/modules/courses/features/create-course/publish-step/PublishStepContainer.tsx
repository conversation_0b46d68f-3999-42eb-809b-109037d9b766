'use client';

import { routePaths } from '@/config';
import { QUERY_KEYS } from '@/constants/query-keys';
import ContentSummary from '@/modules/courses/features/create-course/publish-step/components/content-summary/ContentSummary';
import { CourseFaq } from '@/modules/courses/features/create-course/publish-step/components/faq/Faq';
import InfoSummary from '@/modules/courses/features/create-course/publish-step/components/info-summary/InfoSummary';
import SummaryLayout from '@/modules/courses/features/create-course/publish-step/components/SummaryLayout';
import useNavigateSteps from '@/modules/courses/features/create-course/publish-step/hooks/useNavigateSteps';
import usePublishCourse from '@/modules/courses/features/create-course/publish-step/hooks/usePublishCourse';
import { getCourseByIdService } from '@/modules/courses/services/course.service';
import { useRouter } from 'next-nprogress-bar';
import { useParams } from 'next/navigation';
import { useQuery } from 'react-query';
import { CourseInfo } from '../../../types/course.type';
import CreateCourseHeader from '../components/header/Header';
import { PublishConfirmModal, PublishInfoAlert, PublishSuccessModal } from './components';

const useCourseDetail = ({ initialData }: { initialData: CourseInfo }) => {
  const params = useParams<{ courseId: string }>();

  const { data: courseDetailData } = useQuery({
    queryKey: [QUERY_KEYS.COURSE_DETAIL],
    initialData,
    queryFn: () => getCourseByIdService(params.courseId),
  });

  return courseDetailData;
};

interface Props {
  courseDetail: CourseInfo;
}

function PublishStepContainer({ courseDetail }: Props) {
  const router = useRouter();
  const courseDetailData = useCourseDetail({ initialData: courseDetail });

  const { onBackDesignStep, onBackSetupStep } = useNavigateSteps();

  const {
    isPublishing,
    isLoading,
    showSuccessModal,

    onPublish,
    onCancel,
    onConfirmPublish,
    setShowSuccessModal,
  } = usePublishCourse();

  return (
    <>
      <CreateCourseHeader
        stepIndex={2}
        onBack={onBackDesignStep}
        onPublish={onPublish}
        slotProps={{
          setupItem: { status: 'finish', onClick: onBackSetupStep },
          designItem: { status: 'finish', onClick: onBackDesignStep },
          publishItem: { status: 'process', onClick: onPublish },
        }}
      />

      <div className="bg-neutral-50">
        <div className="mx-auto flex w-full flex-col gap-6 px-8 py-6 md:w-2/3 lg:w-3/5">
          <PublishInfoAlert />

          <InfoSummary courseDetail={courseDetail} />

          <ContentSummary courseDetail={courseDetail} />

          <SummaryLayout title="CÂU HỎI THƯỜNG GẶP">
            <CourseFaq data={courseDetailData!} />
          </SummaryLayout>
        </div>
      </div>

      {isPublishing && (
        <PublishConfirmModal
          key={Number(isPublishing)}
          open={isPublishing}
          onCancel={onCancel}
          onConfirm={onConfirmPublish}
          isLoading={isLoading}
        />
      )}

      <PublishSuccessModal
        open={showSuccessModal}
        onClose={() => setShowSuccessModal(false)}
        onNavigateToCourseList={() => router.push(routePaths.profile.children.creator.path)}
      />
    </>
  );
}

export default PublishStepContainer;
