import { getCourseByIdService } from '@/modules/courses/services/course.service';
import PublishStepContainer from './PublishStepContainer';

async function PublishStepPage({ params }: { params: { courseId: string } }) {
  const courseDetail = await getCourseByIdService(params.courseId);

  if (!courseDetail) {
    return <div>Không tìm thấy khóa học</div>;
  }

  return <PublishStepContainer courseDetail={courseDetail} />;
}

export default PublishStepPage;
