import { Modal, Typography } from '@/components/ui';
import { FAQ } from '@/features/courses';
import { XMarkIcon } from '@heroicons/react/24/outline';

const ConfirmDeleteModal = ({
  open,
  data,
  onCancel,
  onDelete,
}: {
  open: boolean;
  data: FAQ;
  onCancel: () => void;
  onDelete: (data: FAQ) => void;
}) => {
  return (
    <Modal
      centered
      open={open}
      title="Xoá câu hỏi thường gặp này?"
      cancelText="Huỷ thao tác"
      okText="Xoá câu hỏi"
      closeIcon={<XMarkIcon className="size-7 text-base-black-100" />}
      onCancel={onCancel}
      onOk={() => onDelete(data)}
      okButtonProps={{ danger: true }}
      classNames={{
        header: 'px-6 py-4',
        body: 'px-6 py-4',
        footer: 'px-6 py-4',
      }}
    >
      <Typography variant="bodyLg"><PERSON><PERSON>n có chắc chắn muốn xóa câu hỏi thường gặp này không?</Typography>
      <Typography variant="bodyLg" className="block">
        Dữ liệu sẽ không thể phục hồi lại sau khi đã xóa.
      </Typography>
    </Modal>
  );
};

export { ConfirmDeleteModal };
