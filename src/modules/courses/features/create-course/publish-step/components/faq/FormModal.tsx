import { Input, Modal, RichTextEditor, Typography } from '@/components/ui';
import { FAQ } from '@/features/courses';
import { Form } from 'antd';
import { useEffect, useRef } from 'react';
import ReactQuill, { EmitterSource } from 'react-quill-new';
import 'react-quill-new/dist/quill.bubble.css';
import 'react-quill-new/dist/quill.snow.css';
import styles from './FormModal.module.scss';

type Props = {
  open: boolean;
  data?: FAQ | null;
  onCancel: () => void;
  onSave: (data: FAQ) => void;
};

const FaqFormModal = ({ open, data, onCancel, onSave }: Props) => {
  const isEdit = !!data?.id;

  const questionQuillRef = useRef<ReactQuill | null>(null);
  const answerQuillRef = useRef<ReactQuill | null>(null);

  const [form] = Form.useForm();

  const title = isEdit ? 'Chỉnh sửa câu hỏi thường gặp' : 'Thêm câu hỏi thường gặp';
  const okText = isEdit ? 'Lưu chỉnh sửa' : 'Thêm câu hỏi vào khoá học';

  const modules = {
    toolbar: ['bold', 'italic', 'underline', 'strike', { list: 'ordered' }, { list: 'bullet' }, 'link'],
  };

  // const handleQuestionTextChange = (
  //   selection: ReactQuill.Range,
  //   source: EmitterSource,
  //   editor: ReactQuill.UnprivilegedEditor,
  // ) => {
  //   if (source === 'user' && questionQuillRef.current) {
  //     const text = questionQuillRef.current.getEditor().getText();
  //     if (text.length > 1500) {
  //       questionQuillRef.current.getEditor().history.undo();
  //     }
  //   }
  // };

  const handleAnswerTextChange = (
    selection: ReactQuill.Range,
    source: EmitterSource,
    editor: ReactQuill.UnprivilegedEditor,
  ) => {
    if (source === 'user' && answerQuillRef.current) {
      const text = answerQuillRef.current.getEditor().getText();
      if (text.length > 1500) {
        answerQuillRef.current.getEditor().history.undo();
      }
    }
  };

  const handleSave = () => {
    form
      .validateFields()
      .then((values) => {
        onSave(values);
      })
      .catch(() => {});
  };

  useEffect(() => {
    if (open) {
      if (data) {
        form.setFieldsValue(data);
      } else {
        form.resetFields();
      }
    }
  }, [open, data]);

  return (
    <Modal
      open={open}
      title={title}
      okText={okText}
      cancelText="Huỷ thao tác"
      onClose={onCancel}
      onOk={handleSave}
      classNames={{
        body: 'px-4 pt-2 pb-4 pt-3',
        footer: 'p-4 pt-3 flex justify-end gap-1',
      }}
    >
      <Form form={form} layout="vertical">
        <Form.Item
          label={
            <Typography variant="labelMd" as="h2" className="flex items-center gap-1">
              Câu hỏi
              <Typography variant="labelMd" className="text-[#F44D2C]">
                *
              </Typography>
            </Typography>
          }
          name="question"
          rules={[{ required: true, message: 'Vui lòng nhập câu hỏi' }]}
          getValueFromEvent={(content: string) => content}
          getValueProps={(value: string) => ({ content: value })}
        >
          <RichTextEditor
            maxLength={1500}
            placeholder="Nhập câu hỏi"
            classNames={{
              root: 'focus-within:ring-0',
              editor: 'border-neutral-5 rounded-lg border resize-y overflow-y-auto',
            }}
          />
        </Form.Item>
        <Form.Item
          label={
            <Typography variant="labelMd" as="h2" className="flex items-center gap-1">
              Câu trả lời
              <Typography variant="labelMd" className="text-[#F44D2C]">
                *
              </Typography>
            </Typography>
          }
          name="answer"
          rules={[{ required: true, message: 'Vui lòng nhập câu trả lời' }]}
        >
          <ReactQuill
            ref={answerQuillRef}
            modules={modules}
            placeholder="Nhập câu trả lời"
            onChangeSelection={handleAnswerTextChange}
            className={styles['answer-quill']}
          />
        </Form.Item>
        <Form.Item hidden label="Câu hỏi" name="id">
          <Input placeholder="Nhập câu hỏi" />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export { FaqFormModal };
