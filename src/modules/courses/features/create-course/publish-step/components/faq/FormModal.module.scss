.question-quill {
  :global(.ql-container) {
    border: 1px solid #bfc8d9;
    border-radius: 8px;

    :global(.ql-blank)::before {
      color: #666;
      font-style: normal;
      font-size: 1rem;
      font-weight: 500;
    }
  }
}

.answer-quill {
  :global(.ql-toolbar) {
    border-color: #bfc8d9;
    border-radius: 8px 8px 0 0;
  }

  :global(.ql-container) {
    resize: vertical;
    overflow-y: auto;
    min-height: 120px;
    max-height: 300px;
    border-color: #bfc8d9;
    border-radius: 0 0 8px 8px;

    :global(.ql-editor) {
      height: 128px;
      min-height: 128px;
      font-size: 14px;
    }

    :global(.ql-blank)::before {
      color: #666;
      font-style: normal;
      font-size: 1rem;
      font-weight: 500;
    }
  }
}
