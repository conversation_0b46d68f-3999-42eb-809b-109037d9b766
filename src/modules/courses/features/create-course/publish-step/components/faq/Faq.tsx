'use client';

import { <PERSON><PERSON>, Collapse } from '@/components/ui';
import { FAQ } from '@/features/courses';
import ReorderIcon from '@/icons/ReorderIcon';
import { cn } from '@/lib/utils';
import { ConfirmDeleteModal } from '@/modules/courses/features/create-course/publish-step/components/faq/ConfirmDeleteModal';
import { FaqFormModal } from '@/modules/courses/features/create-course/publish-step/components/faq/FormModal';
import useFaqActions from '@/modules/courses/features/create-course/publish-step/hooks/useFaqActions';
import { CourseInfo } from '@/modules/courses/types/course.type';
import {
  closestCenter,
  DndContext,
  DragEndEvent,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import {
  SortableContext,
  sortableKeyboardCoordinates,
  useSortable,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { ChevronRightIcon, PencilIcon, PlusIcon, TrashIcon } from '@heroicons/react/24/outline';
import { ConfigProvider, Tooltip } from 'antd';
import DOMPurify from 'dompurify';
import { useParams } from 'next/navigation';
import { useState } from 'react';
import 'react-quill-new/dist/quill.snow.css';

const SortableFAQItem = ({
  faq,
  onOpenEdit,
  onOpenDelete,
}: {
  faq: FAQ;
  onOpenEdit: (faq: FAQ) => void;
  onOpenDelete: (faq: FAQ) => void;
}) => {
  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({ id: faq.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  const question = DOMPurify.sanitize(faq.question);
  const answer = DOMPurify.sanitize(faq.answer);

  return (
    <div ref={setNodeRef} style={style} className="flex items-start gap-3">
      <div {...attributes} {...listeners} className="cursor-grab pt-1.5">
        <Tooltip title="Kéo thả để sắp xếp thứ tự">
          <Button variant="ghost-reversed" size="large" icon={<ReorderIcon />} />
        </Tooltip>
      </div>

      <div className="flex-1">
        <ConfigProvider
          theme={{
            components: {
              Collapse: {
                headerBg: '#fff',
                headerPadding: '16px 20px',
                contentBg: '#F5F7FA',
                contentPadding: '16px 20px',
                colorBorder: '#E6EAF0',
                fontSizeIcon: 24,
              },
            },
          }}
        >
          <Collapse
            items={[
              {
                label: <div dangerouslySetInnerHTML={{ __html: question }} />,
                children: <div dangerouslySetInnerHTML={{ __html: answer }} />,
              },
            ]}
            expandIconPosition="end"
            expandIcon={({ isActive }) => (
              <ChevronRightIcon
                className={cn('size-6', {
                  'rotate-90': isActive,
                })}
              />
            )}
            className="w-full"
          />
        </ConfigProvider>
      </div>

      <div className="flex items-center pt-1.5">
        <Tooltip title="Chỉnh sửa câu hỏi">
          <Button
            variant="ghost-reversed"
            icon={<PencilIcon className="text-base-black-100" />}
            onClick={(e) => {
              e.stopPropagation();
              onOpenEdit(faq);
            }}
          />
        </Tooltip>
        <Tooltip title="Xóa câu hỏi">
          <Button
            variant="ghost-reversed"
            icon={<TrashIcon className="text-base-black-100" />}
            onClick={(e) => {
              e.stopPropagation();
              onOpenDelete(faq);
            }}
          />
        </Tooltip>
      </div>
    </div>
  );
};

type Props = {
  data: CourseInfo;
};

const CourseFaq = ({ data }: Props) => {
  const params = useParams<{ courseId: string }>();

  const faqs = data.courseFaq;

  const [open, setOpen] = useState(false);
  const [openDelete, setOpenDelete] = useState(false);
  const [faq, setFaq] = useState<FAQ | null>(null);

  const { onCreateFaq, onEditFaq, onDeleteFaq, onSwapFaq } = useFaqActions();

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    }),
  );

  const handleReset = () => {
    setFaq(null);
    setOpen(false);
    setOpenDelete(false);
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (active.id !== over?.id && over?.id) {
      const oldIndex = faqs.findIndex((item) => item.id === active.id);
      const newIndex = faqs.findIndex((item) => item.id === over.id);

      if (oldIndex !== -1 && newIndex !== -1) {
        onSwapFaq({
          courseId: params.courseId,
          from: oldIndex,
          to: newIndex,
        });
      }
    }
  };

  const handleOpenAdd = () => {
    setFaq(null);
    setOpen(true);
  };

  const handleOpenEdit = (faq: FAQ) => {
    setFaq(faq);
    setOpen(true);
  };

  const handleOpenDelete = (faq: FAQ) => {
    const hasQuestion = !!faq.question;
    const hasAnswer = !!faq.answer;
    const hasContent = hasQuestion && hasAnswer;

    if (hasContent) {
      setFaq(faq);
      setOpenDelete(true);
    } else {
      if (faq?.id) {
        handleDelete(faq);
      }
    }
  };

  const handleSave = (data: FAQ) => {
    if (data?.id) {
      onEditFaq({
        courseId: params.courseId,
        faqId: data.id,
        question: data.question,
        answer: data.answer,
      });
    } else {
      const maxSortIndex = faqs.length > 0 ? Math.max(...faqs.map((faq) => faq.sortIndex)) : -1;

      onCreateFaq({
        courseId: params.courseId,
        faqs: [
          {
            question: data.question,
            answer: data.answer,
            sort_index: maxSortIndex + 1,
          },
        ],
      });
    }
    handleReset();
  };

  const handleDelete = (data: FAQ) => {
    if (data?.id) {
      onDeleteFaq({
        courseId: params.courseId,
        faqId: data.id,
      });
    }
    handleReset();
  };

  return (
    <div className="space-y-3">
      <div className="flex w-full flex-col gap-6">
        <DndContext sensors={sensors} collisionDetection={closestCenter} onDragEnd={handleDragEnd}>
          <SortableContext items={faqs.map((f) => f.id)} strategy={verticalListSortingStrategy}>
            <div className="flex flex-col gap-4">
              {faqs.map((faq) => (
                <SortableFAQItem key={faq.id} faq={faq} onOpenEdit={handleOpenEdit} onOpenDelete={handleOpenDelete} />
              ))}
            </div>
          </SortableContext>
        </DndContext>

        <Tooltip title="Tối đa 5 câu hỏi thường gặp">
          <Button
            disabled={faqs.length >= 5}
            variant="tertiary"
            startIcon={<PlusIcon />}
            onClick={handleOpenAdd}
            className={cn('flex w-full items-center text-base-black-100', {
              'w-fit': faqs.length >= 1,
            })}
          >
            Thêm câu hỏi và câu trả lời
          </Button>
        </Tooltip>
      </div>

      <FaqFormModal open={open} data={faq} onCancel={handleReset} onSave={handleSave} />

      {faq && <ConfirmDeleteModal open={openDelete} data={faq} onCancel={handleReset} onDelete={handleDelete} />}
    </div>
  );
};

export { CourseFaq };
