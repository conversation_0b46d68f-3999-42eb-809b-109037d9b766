import { Button, Typography } from '@/components/ui';
import { routePaths } from '@/config';
import CourseSectionsSummary from '@/modules/courses/components/course-sections-summary/CourseSectionsSummary';
import CourseSummaryInfo from '@/modules/courses/components/course-sections-summary/CourseSummaryInfo';
import { CourseInfo } from '@/modules/courses/types/course.type';
import { formatApiUrl } from '@/utils/url.util';
import { PencilSquareIcon } from '@heroicons/react/24/outline';
import { useParams, useRouter } from 'next/navigation';
import SummaryLayout from '../SummaryLayout';

export default function ContentSummary({ courseDetail }: { courseDetail: CourseInfo }) {
  const router = useRouter();
  const params = useParams<{ courseId: string }>();

  const sections = courseDetail.sections || [];
  const totalCourseDuration = sections.reduce(
    (total, section) => total + section.lectures.reduce((total, lesson) => total + (lesson.duration ?? 0), 0),
    0,
  );

  const handleEditCourse = () => {
    const url = formatApiUrl(routePaths.course.design, { courseId: params.courseId });
    router.push(url);
  };

  return (
    <SummaryLayout
      title={
        <Typography variant="labelLg" className="uppercase">
          Nội dung khóa học
        </Typography>
      }
      icon={
        <Button
          variant="ghost-reversed"
          icon={<PencilSquareIcon className="size-6 text-base-black-100" />}
          onClick={handleEditCourse}
        />
      }
    >
      <div className="flex size-full flex-col gap-4">
        <CourseSummaryInfo totalSections={sections.length} totalDuration={totalCourseDuration} />

        <div className="h-px w-full border border-neutral-100" />

        <CourseSectionsSummary courseDetail={courseDetail} />
      </div>
    </SummaryLayout>
  );
}
