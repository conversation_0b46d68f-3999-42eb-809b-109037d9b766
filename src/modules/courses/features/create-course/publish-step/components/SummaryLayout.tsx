import { cn } from '@/lib/utils';

type SummaryLayoutProps = {
  title: React.ReactNode;
  icon?: React.ReactNode;
  children: React.ReactNode;
  classNames?: {
    root?: string;
    title?: string;
    content?: string;
  };
};

const SummaryLayout = ({ title, icon, children, classNames = {} }: SummaryLayoutProps) => {
  return (
    <section className={cn('flex flex-col gap-4', classNames.root)}>
      <header className={cn('flex items-center justify-between', classNames.title)}>
        {title}
        {icon}
      </header>

      <div className={cn('rounded-lg border border-neutral-100 bg-white p-5', classNames.content)}>{children}</div>
    </section>
  );
};

export default SummaryLayout;
