import { Typography } from '@/components/ui';
import { InformationCircleIcon } from '@heroicons/react/24/solid';
import { Alert } from 'antd';

const PublishInfoAlert = () => {
  return (
    <div className="size-full">
      <Alert
        message={<Typography variant="labelMd"><PERSON><PERSON><PERSON> học gần như sẵn sàng để xuất bản!</Typography>}
        description={
          <Typography variant="bodyMd">
            Vui lòng kiểm tra lại nội dung và thông tin lần cuối để đảm bảo mọi thứ đầy đủ. Bạn vẫn có thể chỉnh sửa nếu
            cần. Đừng quên thêm mục “Câu hỏi thường gặp” để hỗ trợ người học tốt hơn nhé!
          </Typography>
        }
        type="info"
        className="rounded-md border border-blue-500 p-3"
        showIcon
        closable
        icon={<InformationCircleIcon className="flex size-6 items-center" />}
      />
    </div>
  );
};

export default PublishInfoAlert;
