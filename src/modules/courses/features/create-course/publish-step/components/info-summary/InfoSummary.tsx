'use client';

import { But<PERSON>, Tag, Typography } from '@/components/ui';
import { routePaths } from '@/config';
import { CourseInfo } from '@/modules/courses/types/course.type';
import { formatApiUrl } from '@/utils/url.util';
import { UserOutlined } from '@ant-design/icons';
import { PencilSquareIcon } from '@heroicons/react/24/outline';
import { Avatar } from 'antd';
import Image from 'next/image';
import { useParams, useRouter } from 'next/navigation';
import SummaryLayout from '../SummaryLayout';
import CourseTags from './CourseTags';
import RelatedCourses from './RelatedCourses';

function InfoSummary({ courseDetail }: { courseDetail: CourseInfo }) {
  const router = useRouter();

  const params = useParams<{ courseId: string }>();

  const handleEditCourse = () => {
    const url = formatApiUrl(routePaths.course.editInfo, { courseId: params.courseId });
    router.push(url);
  };

  return (
    <SummaryLayout
      title={
        <div className="flex items-center gap-2">
          <Typography variant="labelLg" className="uppercase">
            Thông tin khóa học
          </Typography>
          <div className="flex items-center gap-2">
            {['Khóa học cơ bản', 'Nội dung tiêu chuẩn'].map((item) => (
              <Tag key={item} className="m-0 rounded border border-neutral-200 bg-neutral-50 px-2 py-1">
                <Typography variant="labelSm">{item}</Typography>
              </Tag>
            ))}
          </div>
        </div>
      }
      icon={
        <Button
          variant="ghost-reversed"
          icon={<PencilSquareIcon className="size-6 text-base-black-100" />}
          onClick={handleEditCourse}
        />
      }
    >
      <article className="flex flex-col gap-4 bg-white">
        <header className="flex justify-between gap-6">
          <div className="flex flex-col gap-3">
            <Typography variant="labelMd">{courseDetail.topic.topicName}</Typography>

            <Typography variant="headlineSm" as="h1">
              {courseDetail.courseName}
            </Typography>

            <div className="flex items-center gap-2">
              <Avatar src={courseDetail.createdBy.avatar || null} size={40} icon={<UserOutlined />} shape="square" />

              <div>
                <Typography variant="labelMd" as="h3">
                  {courseDetail.createdBy.name}
                </Typography>

                <Typography variant="labelSm" className="text-secondary_text">
                  Giảng viên tại Studify
                </Typography>
              </div>
            </div>
          </div>

          <Image
            src={courseDetail.courseThumbnailImage}
            alt={courseDetail.courseName}
            width={267}
            height={150}
            className="h-[150px] w-[267px] rounded-lg object-cover"
          />
        </header>

        <div className="h-px bg-neutral-100" />

        <div className="flex flex-col gap-2">
          <Typography variant="labelMd">Mô tả khóa học</Typography>
          <div dangerouslySetInnerHTML={{ __html: courseDetail.courseDescription }} />
        </div>

        <CourseTags tags={courseDetail.courseTag} />

        <RelatedCourses courseRelated={courseDetail.courseRelated} />
      </article>
    </SummaryLayout>
  );
}

export default InfoSummary;
