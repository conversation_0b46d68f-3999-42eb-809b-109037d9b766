import { Tag, Typography } from '@/components/ui';
import { Tag as TagType } from '@/features/courses/types/course.type';

interface CourseTagsProps {
  tags: TagType[];
}

const mapCourseTags = ({ tags }: { tags: TagType[] }) => {
  return (
    tags?.map((item) => {
      const tag = item.tag as unknown as TagType['tag'] & { tag_name: string };
      return {
        ...tag,
        tagName: tag?.tagName || tag?.tag_name,
      };
    }) || []
  );
};

const CourseTags = ({ tags }: CourseTagsProps) => {
  const courseTags = mapCourseTags({ tags });

  if (!courseTags.length) {
    return null;
  }

  return (
    <div className="flex flex-col gap-2">
      <Typography variant="labelMd">Từ khóa</Typography>

      <div className="flex flex-wrap gap-1">
        {courseTags.map((tag) => {
          return (
            <Tag className="border border-neutral-200 bg-neutral-50 px-2 py-1.5" key={tag.id}>
              <Typography variant="labelMd">{tag.tagName}</Typography>
            </Tag>
          );
        })}
      </div>
    </div>
  );
};

export default CourseTags;
