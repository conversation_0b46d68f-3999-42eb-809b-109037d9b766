'use client';

import { Typography } from '@/components/ui';
import { RelatedCourse } from '@/features/courses';
import Image from 'next/image';

const RelatedCourseItem = ({ courseImg, courseName }: { courseImg: string; courseName: string }) => {
  return (
    <div className="flex w-[49%] gap-3 rounded-lg border border-neutral-200 p-3">
      <Image
        src={courseImg}
        alt="course-thumbnail"
        width={100}
        height={62}
        className="h-16 w-24 rounded-lg object-cover"
      />
      <div className="flex items-center">
        <Typography variant="labelLg" className="text-ink-black">
          {courseName}
        </Typography>
      </div>
    </div>
  );
};

function RelatedCourses({ courseRelated }: { courseRelated: RelatedCourse[] }) {
  return (
    <div className="flex flex-col gap-2">
      <Typography variant="labelMd">Kh<PERSON>a học liên quan</Typography>

      <div className="flex w-full flex-wrap gap-4">
        {courseRelated.map((item) => {
          const course = item.related;
          return (
            <RelatedCourseItem
              key={course.id}
              courseImg={course?.courseThumbnailImage ?? ''}
              courseName={course?.courseName ?? ''}
            />
          );
        })}
      </div>
    </div>
  );
}

export default RelatedCourses;
