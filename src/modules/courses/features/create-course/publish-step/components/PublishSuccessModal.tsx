'use client';

import { Button, Typography } from '@/components/ui';
import { Modal } from 'antd';
import Image from 'next/image';

interface PublishSuccessModalProps {
  open: boolean;
  onClose: () => void;
  onNavigateToCourseList: () => void;
}

export function PublishSuccessModal({ open, onClose, onNavigateToCourseList }: PublishSuccessModalProps) {
  return (
    <Modal
      open={open}
      onCancel={onClose}
      className="md:!w-[558px]"
      footer={
        <div className="flex justify-center pb-6">
          <Button size="large" onClick={onNavigateToCourseList} className="">
            Trở về trang Danh sách khoá học
          </Button>
        </div>
      }
    >
      <div className="flex flex-col rounded-md">
        <Image
          src="/publish/publish-success.svg"
          alt="Publish Success"
          height={314}
          width={558}
          className="rounded-t-sm"
        />

        <div className="flex flex-col items-center justify-center gap-4 p-6">
          <Typography variant="headlineXs">Kh<PERSON>a học đã được xuất bản thành công và đang chờ xét duyệt!</Typography>
          <div className="flex flex-col gap-2">
            <Typography variant="titleMd">
              Chúng tôi sẽ thông báo ngay khi khóa học được duyệt và hiển thị với học viên.
            </Typography>
            <Typography variant="bodyMd">
              📌 Lưu ý: Bạn vẫn có thể chỉnh sửa nội dung trong thời gian chờ duyệt.
            </Typography>
          </div>
        </div>
      </div>
    </Modal>
  );
}
