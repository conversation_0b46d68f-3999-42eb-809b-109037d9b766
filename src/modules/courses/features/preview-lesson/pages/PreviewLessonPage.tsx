import { Typography } from '@/components/ui';
import BackEditScreenButton from '@/modules/courses/features/preview-lesson/components/BackButton';
import VideoContent from '@/modules/courses/features/preview-lesson/components/video/VideoContent';
import { getLessonById } from '@/modules/courses/services/course.service';
import { SearchParamsProps } from '@/type/appProps';

export type PreviewLessonPageProps = {
  params: Promise<{ courseId: string }>;
  searchParams: Promise<SearchParamsProps> | undefined;
};

export default async function PreviewLessonPage(props: PreviewLessonPageProps) {
  const params = await props.params;
  const searchParams = await props.searchParams;
  const { courseId } = params;
  const { sectionId, lessonId } = searchParams as { sectionId: string; lessonId: string };

  const course = await getLessonById({ courseId, sectionId, lectureId: lessonId });

  const sectionName = course?.section.sectionName;
  const lectureName = course?.lectureName;

  return (
    <div className="flex h-screen flex-col">
      <div className="flex h-[70px] shrink-0 justify-between px-6 py-3">
        <div className="flex flex-col justify-between gap-1">
          <Typography variant="labelSm">{sectionName}</Typography>
          <Typography variant="headlineXs">{lectureName}</Typography>
        </div>

        <BackEditScreenButton />
      </div>

      <div className="h-[calc(100vh-70px)]">
        <VideoContent video={course?.videoId} interactions={course?.lectureInteracts ?? []} />
      </div>
    </div>
  );
}
