'use client';
import React from 'react';

const useVideoPlayer = (videoRef: React.RefObject<HTMLVideoElement | null>) => {
  const [isPlaying, setIsPlaying] = React.useState(false);
  const [isMuted, setIsMuted] = React.useState(false);
  const [currentTime, setCurrentTime] = React.useState(0);

  const video = videoRef?.current;

  const handlePlay = async () => {
    if (!video) return;

    await video.play();
    setIsPlaying(true);
  };

  const handlePause = () => {
    if (!video) return;

    video.pause();
    setIsPlaying(false);
  };

  const toggleMute = () => {
    if (!video) return;

    const newMutedState = !isMuted;
    video.muted = newMutedState;
    setIsMuted(newMutedState);
  };

  const handleTimeUpdate = (playedSeconds: number) => {
    if (video) {
      setCurrentTime(playedSeconds);
    }
  };

  const handleSeek = (value: number) => {
    if (video) {
      video.currentTime = value;
      setCurrentTime(value);
    }
  };

  const handleFullScreen = () => {
    if (video) {
      video.requestFullscreen();
    }
  };

  return {
    isPlaying,
    isMuted,
    currentTime,

    onPlay: handlePlay,
    onPause: handlePause,
    toggleMute,
    onTimeUpdate: handleTimeUpdate,
    onSeek: handleSeek,
    onFullScreen: handleFullScreen,
  };
};

export default useVideoPlayer;
