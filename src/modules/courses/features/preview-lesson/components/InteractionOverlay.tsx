'use client';

import { But<PERSON>, Typography } from '@/components/ui';
import { InteractionType } from '@/modules/courses/constants/course.const';
import ExploreContainer from '@/modules/courses/features/preview-lesson/components/explore/ExploreContainer';
import QuestionContainer from '@/modules/courses/features/preview-lesson/components/question/QuestionContainer';
import { LectureInteract } from '@/modules/courses/types/interaction.type';
import { ChevronRightIcon } from '@heroicons/react/16/solid';
import React from 'react';
import { match } from 'ts-pattern';

type InteractionOverlayProps = {
  isVisible: boolean;
  currentInteraction: LectureInteract | null;
  onClose: () => void;
};

const renderTitle = (type: InteractionType | undefined) => {
  return match(type)
    .with(InteractionType.Explore, () => 'Khám phá')
    .with(InteractionType.QuickQuestion, () => 'Câu hỏi nhanh')
    .otherwise(() => null);
};

function InteractionOverlay(props: InteractionOverlayProps) {
  const { isVisible, currentInteraction, onClose } = props;

  const [showCloseButton, setShowCloseButton] = React.useState(true);

  if (!isVisible) return null;
  const canSkip = !!currentInteraction?.question?.questionRequired;
  return (
    <div className="absolute inset-0 z-30 m-4 h-[calc(100vh-100px)] rounded-lg bg-white p-8 transition-all">
      <div>
        <div className="flex w-full items-center justify-between">
          <Typography variant="headlineXs">{renderTitle(currentInteraction?.interactType)}</Typography>

          {showCloseButton && !canSkip && (
            <Button
              size="large"
              onClick={onClose}
              endIcon={<ChevronRightIcon className="size-6" />}
              className="text-label-lg text-white"
            >
              Tiếp tục bài học
            </Button>
          )}
        </div>

        {match(currentInteraction?.interactType)
          .with(InteractionType.Explore, () => (
            <ExploreContainer
              currentInteraction={currentInteraction!}
              onShowCloseButton={() => setShowCloseButton(true)}
            />
          ))
          .with(InteractionType.QuickQuestion, () => (
            <QuestionContainer
              interaction={currentInteraction!}
              onClose={onClose}
              onShowCloseButton={() => setShowCloseButton(true)}
            />
          ))
          .otherwise(() => null)}
      </div>
    </div>
  );
}

export default InteractionOverlay;
