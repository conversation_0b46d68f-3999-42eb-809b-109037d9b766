'use client';

import { Button } from '@/components/ui';
import { routePaths } from '@/config';
import { useSafeSearchParams } from '@/hooks';
import { formatApiUrl } from '@/utils/url.util';
import { useParams, useRouter } from 'next/navigation';
import queryString from 'query-string';

interface QueryParams {
  sectionId: string;
  lessonId: string;
}

export default function BackEditScreenButton() {
  const { courseId } = useParams<{ courseId: string }>();
  const { parsedQueryParams } = useSafeSearchParams<QueryParams>();
  const router = useRouter();

  const navigateToEditMode = (): void => {
    const editModeUrl = queryString.stringifyUrl({
      url: formatApiUrl(routePaths.course.design, { courseId }),
      query: {
        sectionId: parsedQueryParams.sectionId,
        lessonId: parsedQueryParams.lessonId,
      },
    });

    router.push(editModeUrl);
  };

  return (
    <Button size="large" variant="secondary" onClick={navigateToEditMode} aria-label="Switch to edit mode">
      <PERSON><PERSON><PERSON><PERSON> về chế độ chỉnh sửa
    </Button>
  );
}
