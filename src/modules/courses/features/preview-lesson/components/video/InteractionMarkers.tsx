import { cn } from '@/lib/utils';
import { InteractionType } from '@/modules/courses/constants/course.const';
import { calculatePositionInPercent } from '@/modules/courses/features/create-course/design-step/utils/video.util';
import { LectureInteract } from '@/modules/courses/types/interaction.type';

type InteractionMarkersProps = {
  interactions: LectureInteract[];
  duration: number;
};

function InteractionMarkers(props: InteractionMarkersProps) {
  const { interactions, duration } = props;

  return (
    <>
      <div className="absolute top-1/2 w-full -translate-y-1/2">
        {interactions.map((interaction) => {
          const position = calculatePositionInPercent({ time: interaction.startAt, duration });
          return (
            <div
              key={interaction.id}
              className={cn('absolute h-3 w-3 -translate-x-1/3 -translate-y-1/3 cursor-pointer rounded-full', {
                'bg-pink-500': interaction.interactType === InteractionType.QuickQuestion,
                'bg-primary-500': interaction.interactType === InteractionType.Explore,
              })}
              style={{ left: `${position}%` }}
            />
          );
        })}
      </div>
    </>
  );
}

export default InteractionMarkers;
