'use client';

import { usePreventHydration } from '@/hooks';
import { InteractionType } from '@/modules/courses/constants/course.const';
import useVideoInteractions from '@/modules/courses/features/create-course/design-step/hooks/useVideoInteractions';
import InteractionOverlay from '@/modules/courses/features/preview-lesson/components/InteractionOverlay';
import InteractionMarkers from '@/modules/courses/features/preview-lesson/components/video/InteractionMarkers';
import VideoControls from '@/modules/courses/features/preview-lesson/components/video/VideoControls';
import VideoProgress from '@/modules/courses/features/preview-lesson/components/video/VideoProgress';
import useVideoPlayer from '@/modules/courses/features/preview-lesson/hooks/useVideoPlayer';
import { LectureInteract } from '@/modules/courses/types/interaction.type';
import React from 'react';
import { Video } from '../../../../types/course.type';

type VideoContentProps = {
  video: Video | undefined;
  interactions?: LectureInteract[];
};

const mapInteractions = (interactions: LectureInteract[]) => {
  return interactions.map((interaction) => ({
    ...interaction,
    position: Math.floor(interaction.startAt),
    interactType: interaction.interactTypeId === 2 ? InteractionType.QuickQuestion : InteractionType.Explore,
  }));
};

export default function VideoContent(props: VideoContentProps) {
  const { video, interactions = [] } = props;
  const videoRef = React.useRef<HTMLVideoElement | null>(null);

  const { isPlaying, isMuted, currentTime, onPause, onPlay, toggleMute, onTimeUpdate, onSeek, onFullScreen } =
    useVideoPlayer(videoRef);

  const mappedInteractions = mapInteractions(interactions);

  const { selectedInteraction, isShowInteraction, onCloseInteraction } = useVideoInteractions({
    interactions: mappedInteractions,
    currentTime,
    onPause,
  });

  usePreventHydration();

  const videoUrl = video?.fileUrl || '';
  const duration = video?.fileDuration || 0;

  const handleTogglePlay = async () => {
    if (isShowInteraction) return;
    if (isPlaying) {
      onPause();
    } else {
      await onPlay();
    }
  };

  return (
    <div className="relative size-full select-none">
      <video
        className="size-full object-cover"
        preload="auto"
        src={videoUrl}
        ref={videoRef}
        height={100}
        width={100}
        onTimeUpdate={(e: React.ChangeEvent<HTMLVideoElement>) => onTimeUpdate(e.target.currentTime)}
        onClick={handleTogglePlay}
      >
        <source src={videoUrl} type="video/mp4" />
        <source src={videoUrl} type="video/mov" />
        Your browser does not support the video tag.
      </video>

      <InteractionOverlay
        isVisible={isShowInteraction}
        currentInteraction={selectedInteraction}
        onClose={() => {
          onCloseInteraction();
          onPlay();
        }}
      />

      <div className="absolute bottom-0 z-20 h-fit w-full">
        <div className="flex items-center gap-4">
          <div className="relative w-full">
            <VideoProgress
              currentTime={currentTime}
              duration={duration}
              onSeek={isShowInteraction ? () => {} : onSeek}
            />
            <InteractionMarkers duration={duration} interactions={mappedInteractions} />
          </div>
        </div>

        <VideoControls
          isPlaying={isPlaying}
          isMuted={isMuted}
          currentTime={currentTime}
          duration={duration}
          togglePlay={handleTogglePlay}
          toggleMute={toggleMute}
          onFullScreen={isShowInteraction ? () => {} : onFullScreen}
        />
      </div>
    </div>
  );
}
