import { Icon } from '@/components/client';
import { Typography } from '@/components/ui';
import { ArrowsPointingOutIcon, SpeakerWaveIcon, SpeakerXMarkIcon } from '@heroicons/react/24/outline';
import { PauseIcon, PlayIcon } from '@heroicons/react/24/solid';

type VideoControlsProps = {
  isPlaying: boolean;
  isMuted: boolean;
  currentTime: number;
  duration: number;

  togglePlay: () => void;
  toggleMute: () => void;
  onFullScreen: () => void;
};

const formatTime = (time: number) => {
  const minutes = Math.floor(time / 60);
  const seconds = Math.floor(time % 60);
  return `${minutes}:${seconds < 10 ? `0${seconds}` : seconds}`;
};

export default function VideoControls(props: VideoControlsProps) {
  const { isPlaying, isMuted, currentTime, duration, togglePlay, toggleMute, onFullScreen } = props;

  return (
    <div className="flex justify-between px-4 pb-4">
      <div className="flex items-end gap-4">
        <Icon
          icon={isPlaying ? <PauseIcon className="size-6" /> : <PlayIcon className="size-6" />}
          className="cursor-pointer text-neutral-50"
          onClick={togglePlay}
        />

        <Typography className="text-ink-white" variant="labelMd">
          {formatTime(currentTime)} / {formatTime(duration)}
        </Typography>
      </div>

      <div className="flex justify-end gap-4 pr-10">
        <Icon
          icon={isMuted ? <SpeakerXMarkIcon className="size-6" /> : <SpeakerWaveIcon className="size-6" />}
          className="cursor-pointer text-white"
          onClick={toggleMute}
        />

        <Icon
          icon={<ArrowsPointingOutIcon className="size-6" />}
          className="cursor-pointer text-white"
          onClick={onFullScreen}
        />
      </div>
    </div>
  );
}
