import { cn } from '@/lib/utils';
import { <PERSON><PERSON><PERSON> } from 'antd';
import React from 'react';

type VideoProgressProps = {
  currentTime: number;
  duration: number;
  onSeek: (time: number) => void;
};

export default function VideoProgress(props: VideoProgressProps) {
  const { currentTime, duration, onSeek } = props;

  return (
    <React.Fragment>
      <Slider
        className="h-1"
        min={0}
        max={duration || 100}
        step={1}
        value={currentTime}
        tooltip={{ formatter: null }}
        onChange={onSeek}
        classNames={{
          handle: cn(
            'after:bg-pink-500 after:shadow-none',
            'after:border-[3px] after:border-white',
            'after:outline-[3px] after:outline-primary-400',
          ),
          track: cn('rounded-lg bg-primary-400'),
          rail: 'bg-white/50  rounded-lg',
        }}
      />
    </React.Fragment>
  );
}
