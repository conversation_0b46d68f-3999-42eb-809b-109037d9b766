import React from 'react';

type VideoPlayerProps = {
  videoUrl: string | undefined;
  videoRef: React.RefObject<HTMLVideoElement | null>;
  onPlay: () => void;
  onPause: () => void;
};

export default function VideoPlayer(props: VideoPlayerProps) {
  const { videoUrl, videoRef, onPlay, onPause } = props;

  if (!videoUrl) return null;

  return (
    <div className="aspect-video w-full overflow-hidden px-4 pt-4">
      <video
        playsInline
        ref={videoRef}
        src={videoUrl}
        className="size-full rounded-lg object-cover"
        onPlay={onPlay}
        onPause={onPause}
        onError={(e) => {
          const video = e.target as HTMLVideoElement;
          if (video.error?.code === 20) {
            video.load();
          }
        }}
      >
        <source src={videoUrl} type="video/mp4" />
        <source src={videoUrl} type="video/mov" />
        Your browser does not support the video tag.
      </video>
    </div>
  );
}
