'use client';

import { Pagination, Typography } from '@/components/ui';
import { palette } from '@/config/theme';
import { Comment } from '@/features/courses';
import { CourseInfo } from '@/modules/courses/types/course.type';
import { StarIcon } from '@heroicons/react/24/solid';
import Progress from 'antd/es/progress';
import { useState } from 'react';
import CommentItem from './CommentItem';

const RatingSection = ({ stars }: { stars: { star: number; percent: number }[] }) => {
  return (
    <div className="w-full rounded-lg bg-neutral-50 p-6">
      {stars.map((star) => (
        <div key={star.star} className="flex w-full items-center gap-2">
          <div className="flex items-center gap-1">
            <Typography variant="labelMd">{star.star}</Typography>
            <StarIcon width={12} className="text-yellow-500" />
          </div>

          <Progress
            showInfo={false}
            strokeColor={palette.yellow[500]}
            trailColor={palette.base['white-100']}
            size={{ height: 12 }}
            className="w-full"
            status="normal"
            percent={star.percent}
          />

          <div className="flex min-w-10 justify-end">
            <Typography className="text-neutral-800" variant="labelMd">
              {Math.round(star.percent)}%
            </Typography>
          </div>
        </div>
      ))}
    </div>
  );
};

const getRatingData = (courseInfo: CourseInfo) => {
  const totalRating = Number(courseInfo.totalRating);
  const totalRating1 = Number(courseInfo.totalRating1);
  const totalRating2 = Number(courseInfo.totalRating2);
  const totalRating3 = Number(courseInfo.totalRating3);
  const totalRating4 = Number(courseInfo.totalRating4);
  const totalRating5 = Number(courseInfo.totalRating5);

  const starOnePercent = (totalRating1 * 100) / totalRating || 0;
  const starTowPercent = (totalRating2 * 100) / totalRating || 0;
  const starThreePercent = (totalRating3 * 100) / totalRating || 0;
  const starFourPercent = (totalRating4 * 100) / totalRating || 0;
  const starFivePercent = (totalRating5 * 100) / totalRating || 0;

  const stars = [
    {
      star: 5,
      percent: starFivePercent,
    },
    {
      star: 4,
      percent: starFourPercent,
    },
    {
      star: 3,
      percent: starThreePercent,
    },
    {
      star: 2,
      percent: starTowPercent,
    },
    {
      star: 1,
      percent: starOnePercent,
    },
  ];

  return stars;
};

const usePagination = ({ comments }: { comments: Comment[] }) => {
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 5;

  const paginatedComments = comments.slice((currentPage - 1) * pageSize, currentPage * pageSize);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  return {
    paginatedComments,
    currentPage,
    pageSize,

    handlePageChange,
  };
};

const CourseDetailReview = ({ courseInfo }: Readonly<{ courseInfo: CourseInfo }>) => {
  const { comments } = courseInfo;

  const { paginatedComments, currentPage, pageSize, handlePageChange } = usePagination({ comments });

  const stars = getRatingData(courseInfo);

  return (
    <div className="flex w-full flex-col gap-4">
      <Typography variant="headlineXs" className="text-ink-black">
        Đánh giá về khóa học
      </Typography>

      <RatingSection stars={stars} />

      {paginatedComments.map((comment) => (
        <CommentItem key={comment.id} commentData={comment} />
      ))}

      {paginatedComments.length > pageSize && (
        <div className="flex w-full justify-start">
          <Pagination
            current={currentPage}
            total={comments.length}
            size="small"
            pageSize={pageSize}
            showSizeChanger={false}
            onChange={handlePageChange}
          />
        </div>
      )}
    </div>
  );
};

export default CourseDetailReview;
