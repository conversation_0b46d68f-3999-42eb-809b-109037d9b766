'use client';

import { Icon } from '@/components/client';
import { Typography } from '@/components/ui';
import { routePaths } from '@/config';
import { useFavoriteCreator } from '@/features/courses';
import { StarBorderOutlined } from '@/icons';
import LineDescription from '@/icons/LineDescription';
import { cn } from '@/lib/utils';
import CourseDetailOverview from '@/modules/courses/features/course-detail/components/CourseDetailOverview';
import { CourseInfo } from '@/modules/courses/types/course.type';
import { useUserInfoProvider } from '@/utils/providers/UserInfoProvider';
import { StarOutlined } from '@ant-design/icons';
import { StarIcon } from '@heroicons/react/16/solid';
import { ChevronLeftIcon } from '@heroicons/react/24/outline';
import Image from 'next/image';
import Link from 'next/link';

type CourseDetailHeaderProps = {
  courseInfo: CourseInfo;
  className?: string;
};

const CourseDetailHeaderLayout = (props: { children: React.ReactNode }) => {
  const { children } = props;
  return (
    <div className={cn('xl:pl-[114px] relative min-h-[276px] bg-primary-900 px-8 py-4 text-white')}>
      <div className={'xl:max-w-[calc(100vw-220px)] m-auto'}>
        <div className="absolute left-0 top-0">
          <svg width="37" height="37" viewBox="0 0 37 37" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M0.191277 37C-1.99266 33.0993 -4.03154 29.4048 -6.11877 25.7385C-9.22141 20.2978 -7.81112 21.0415 -13.2629 22.6705C-17.3487 23.8912 -21.4345 25.0999 -25.9757 26.4499C-25.339 24.1297 -24.8152 22.2178 -24.2874 20.3058C-23.3928 17.064 -22.5587 13.806 -21.5635 10.5965C-21.1565 9.28688 -21.4789 8.68864 -22.6595 8.08232C-26.5438 6.09357 -30.3677 3.9876 -34.2037 1.90993C-35.0177 1.46933 -35.7994 0.960014 -36.9961 0.240509C-31.5725 -2.81941 -26.4753 -5.69744 -21.2532 -8.64418C-22.9375 -14.3032 -24.5936 -19.8733 -26.4149 -25.9932C-24.3236 -25.4475 -22.732 -25.0635 -21.1605 -24.6188C-17.6791 -23.6285 -14.1816 -22.6746 -10.7365 -21.563C-9.33827 -21.1102 -8.6855 -21.4942 -8.05289 -22.7352C-5.90522 -26.9269 -3.66084 -31.0661 -1.44064 -35.2174C-1.17067 -35.7227 -0.816078 -36.1794 -0.280167 -37C1.0858 -34.6151 2.28254 -32.5496 3.45509 -30.4679C4.84121 -28.0062 6.34015 -25.597 7.52882 -23.0424C8.29441 -21.3972 9.13252 -21.373 10.6556 -21.8661C15.5594 -23.4587 20.5196 -24.8897 25.9513 -26.5348C24.267 -20.5362 22.6713 -14.8408 21.0314 -8.98372C26.2535 -6.16229 31.4312 -3.3651 37.0039 -0.353687C34.1672 1.25914 31.6408 2.69815 29.1103 4.12908C27.007 5.31747 24.9479 6.60288 22.7721 7.64576C21.3295 8.34102 21.2449 9.15754 21.6841 10.54C23.2717 15.528 24.7505 20.5484 26.4026 25.9972C25.0567 25.6536 24.0534 25.4192 23.0662 25.1403C18.9401 23.964 14.8019 22.8241 10.704 21.5549C9.2534 21.1062 8.54826 21.4902 7.87535 22.816C5.71156 27.0724 3.42688 31.2682 1.17848 35.4801C0.936714 35.9329 0.622421 36.3452 0.195301 37.004L0.191277 37Z"
              fill="#FFC31E"
            />
          </svg>
        </div>
        <div className="absolute left-0 top-0 hidden md:block">
          <LineDescription />
        </div>
        <div className="absolute bottom-0 left-2">
          <svg width="52" height="26" viewBox="0 0 52 26" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M9.98498 19.0776C7.68296 16.8661 5.38094 14.6576 3.18116 12.5425C6.34499 9.39623 9.33352 6.42522 12.4535 3.32567C14.5043 5.44364 16.7508 7.76319 18.9944 10.0827C19.1346 9.99803 19.2778 9.91039 19.418 9.82567C19.4531 9.4196 19.5173 9.01353 19.5203 8.60746C19.529 6.12432 19.5436 3.64117 19.5144 1.16095C19.5057 0.389712 19.7014 0.00117275 20.572 0.00701545C24.174 0.0391503 27.776 0.044993 31.378 0.0040941C32.3859 -0.00759129 32.4502 0.527015 32.4443 1.29533C32.4239 4.10567 32.4355 6.91308 32.7627 9.92207C35.0443 7.67263 37.3288 5.4232 39.5812 3.20297C42.6223 6.30836 45.5758 9.32027 48.6724 12.4812C46.5719 14.5904 44.3021 16.869 42.0293 19.1506C42.0789 19.2616 42.1286 19.3726 42.1782 19.4836H51.8625C51.8976 20.1789 51.9472 20.696 51.9472 21.2131C51.9531 24.523 51.9151 27.8329 51.9706 31.1427C51.9881 32.1857 51.6259 32.4836 50.6239 32.4574C48.2897 32.396 45.9497 32.434 43.6155 32.4427C43.1481 32.4427 42.6807 32.4895 41.927 32.5275C44.4481 34.8792 46.7677 37.0439 48.9937 39.1209C45.5787 42.5068 42.5609 45.5012 39.3533 48.6825C37.358 46.5762 35.167 44.2625 32.976 41.9459C32.8358 42.0189 32.6955 42.089 32.5553 42.1621C32.5173 42.5594 32.4472 42.9538 32.4443 43.3511C32.4326 45.7845 32.4122 48.218 32.4531 50.6515C32.4677 51.5425 32.272 51.9778 31.2524 51.9632C27.7468 51.9135 24.2412 51.9165 20.7385 51.9632C19.7481 51.9778 19.4969 51.598 19.5115 50.6749C19.5553 47.8645 19.529 45.0542 19.1843 42.0365C16.9057 44.283 14.627 46.5295 12.4243 48.703C9.38027 45.6385 6.40925 42.647 3.29218 39.5095C5.43937 37.4558 7.7706 35.2297 10.1018 33.0007C9.99959 32.8138 9.89734 32.6239 9.79802 32.4369C9.24881 32.4369 8.70251 32.4369 8.1533 32.4369C5.81622 32.4369 3.47914 32.4165 1.14498 32.4486C0.356221 32.4603 -0.000183556 32.2295 0.00858049 31.3765C0.0407153 27.7744 0.0407153 24.1724 0.00858049 20.5704C-0.000183556 19.6911 0.408805 19.5129 1.17128 19.5216C3.5551 19.5479 5.94184 19.5304 8.32566 19.5304C8.796 19.5304 9.26925 19.5304 9.73959 19.5304C9.82431 19.3785 9.90903 19.2295 9.99375 19.0776H9.98498Z"
              fill="#E44C10"
            />
          </svg>
        </div>
        <div className="absolute right-0 top-1/2 -translate-y-1/2">
          <svg width="33" height="66" viewBox="0 0 33 66" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M32.7581 5.38882e-05C34.5929 3.26767 36.327 6.33375 38.0431 9.40703C40.0003 12.9122 41.8963 16.4569 43.9326 19.9152C44.4147 20.7321 45.2565 21.4627 46.102 21.9233C52.0958 25.2017 58.1327 28.3973 64.1517 31.6254C64.677 31.9061 65.1878 32.2119 66.0081 32.6762C64.7273 33.4175 63.6408 34.0653 62.5363 34.6806C56.9887 37.7755 51.4122 40.8236 45.9041 43.9833C45.091 44.4511 44.4075 45.3436 43.947 46.1929C40.7594 52.0335 37.651 57.9138 34.5102 63.7761C34.154 64.4382 33.7654 65.086 33.2438 65.9965C30.747 61.5413 28.4084 57.3776 26.0807 53.2067C24.7352 50.7992 23.476 48.3413 22.0405 45.9913C21.5404 45.178 20.7165 44.4439 19.8711 43.9797C13.8773 40.7013 7.84034 37.502 1.82137 34.274C1.2961 33.9933 0.792422 33.6766 0.0117188 33.2232C0.824801 32.7374 1.44721 32.3379 2.09479 31.978C7.75039 28.8292 13.388 25.6515 19.076 22.5603C20.5618 21.7505 21.5836 20.6961 22.3679 19.1847C25.4367 13.2828 28.6171 7.43855 31.7651 1.57988C32.017 1.11565 32.3264 0.680207 32.7653 -0.0107422L32.7581 5.38882e-05Z"
              fill="#FF96D2"
            />
          </svg>
        </div>

        {children}
      </div>
    </div>
  );
};

const CourseDetailHeader = (props: CourseDetailHeaderProps) => {
  const { isLoggedIn } = useUserInfoProvider();

  const { onFavorite } = useFavoriteCreator();

  const { courseInfo } = props;

  const author = courseInfo.createdBy;

  const isAuthorFollowed = Boolean(courseInfo?.createdBy?.isFollowing);

  return (
    <CourseDetailHeaderLayout>
      <div className="relative z-10 flex flex-col gap-6">
        <Link href={routePaths.profile.path} prefetch={false} className="flex w-fit items-center gap-2 px-4 py-2.5">
          <Icon className="size-5" icon={<ChevronLeftIcon className="text-white" />} />
          <Typography variant="labelMd" className="text-white">
            Trở về trang trước
          </Typography>
        </Link>

        <div className={'flex flex-col gap-12'}>
          <div className="flex flex-col gap-2">
            <Typography variant="headlineMd" className="text-ink-white">
              {courseInfo?.courseName || ''}
            </Typography>

            <div className="flex items-center gap-1">
              <div className="flex items-center gap-0.5">
                {Array.from({ length: 5 }).map((_, index) => (
                  <Icon key={index} size="sm" icon={<StarIcon className="text-yellow-500" />} />
                ))}
              </div>

              <Typography variant="bodyMd" className="text-ink-white">
                {courseInfo.avgRating}
              </Typography>

              <Typography variant="bodyMd" className="text-ink-white">
                ({courseInfo.totalRating ?? 0})
              </Typography>
            </div>
          </div>

          <div className="flex gap-3">
            <div className="size-12 rounded-lg bg-neutral-100">
              <Image
                src={author?.avatar || '/images/avatar.png'}
                alt={author?.avatar || 'creator avatar'}
                width={48}
                height={48}
                className={'size-12 rounded-lg object-cover'}
              />
            </div>

            <div className="flex flex-col gap-2">
              <div className="flex flex-col gap-2 text-sm md:flex-row md:items-center">
                <Link
                  className={'text-xs text-white hover:underline md:text-sm'}
                  href={`${isLoggedIn ? routePaths.profile.children.creator.path : routePaths.creator.path}/${author?.id}`}
                >
                  <Typography variant="bodyMd" className="text-white">
                    {author?.name}
                  </Typography>
                </Link>

                <div className={'hidden md:block'}>
                  <StarBorderOutlined fill={'white'} />
                </div>

                <span
                  onClick={() => onFavorite({ authorId: author?.id, isFavorite: !isAuthorFollowed })}
                  className={'md:center-x-y flex cursor-pointer items-center'}
                >
                  <Typography variant="bodyMd" className="mr-2 cursor-pointer text-secondary-500 md:text-sm">
                    {isAuthorFollowed ? 'Ngừng theo dõi' : 'Theo dõi'}
                  </Typography>

                  <div className="mb-px hidden md:block">
                    <Icon
                      size="sm"
                      className="text-secondary-500"
                      icon={isAuthorFollowed ? <StarIcon /> : <StarOutlined />}
                    />
                  </div>
                </span>
              </div>
              <Typography variant="bodySm" className="text-ink-200">
                Giảng viên {courseInfo?.topic?.topicName ?? ''}
              </Typography>
            </div>
          </div>
        </div>
      </div>

      <div className="absolute right-[32px] top-24 z-[9] hidden rounded-lg bg-white shadow-xl md:block">
        <CourseDetailOverview courseInfo={courseInfo} />
      </div>
    </CourseDetailHeaderLayout>
  );
};

export default CourseDetailHeader;
