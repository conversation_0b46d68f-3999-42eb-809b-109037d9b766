'use client';

import { Icon } from '@/components/client';
import { Typography } from '@/components/ui';
import { REGEX } from '@/constants/regrex';
import { cn } from '@/lib/utils';
import { ChevronDownIcon } from '@heroicons/react/24/outline';
import React from 'react';

type CourseDescriptionProps = {
  description: string;
};

const CourseDescription = ({ description }: CourseDescriptionProps) => {
  const [isExpanded, setIsExpanded] = React.useState(false);

  const plainDescription = description.replace(REGEX.HTML_TAG, '');

  const shouldExpandDescription = plainDescription.length > 300;

  return (
    <div className="flex flex-col gap-2">
      <Typography variant="headlineXs">Mô tả khóa học</Typography>

      <div className="relative">
        <div
          className={cn('overflow-hidden transition-all duration-300', isExpanded ? 'max-h-none' : 'max-h-[100px]')}
          dangerouslySetInnerHTML={{ __html: description }}
        />

        {!isExpanded && shouldExpandDescription && (
          <div className="absolute bottom-0 left-0 h-12 w-full bg-gradient-to-t from-white to-transparent" />
        )}
      </div>

      {shouldExpandDescription && (
        <div className="flex w-fit cursor-pointer items-center gap-2" onClick={() => setIsExpanded(!isExpanded)}>
          <Typography variant="labelLg" className="text-primary-500">
            {isExpanded ? 'Thu gọn' : 'Xem thêm'}
          </Typography>

          <Icon
            size="sm"
            icon={<ChevronDownIcon className={cn('transition-transform', isExpanded && 'rotate-180')} />}
          />
        </div>
      )}
    </div>
  );
};

export default CourseDescription;
