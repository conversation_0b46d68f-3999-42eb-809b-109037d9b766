'use client';

import { Collapse, Typography } from '@/components/ui';
import { FAQ } from '../../../types/course.type';

type CourseDetailFaqProps = {
  faqs: FAQ[];
};

const CourseDetailFaq = (props: CourseDetailFaqProps) => {
  const { faqs = [] } = props;

  if (!faqs.length) {
    return null;
  }

  return (
    <div className="flex flex-col gap-4">
      <Typography variant="headlineXs">Câu hỏi thường gặp</Typography>

      {faqs.map((faq) => {
        return (
          <div key={faq.id}>
            <Collapse
              items={[
                {
                  label: <div className="text-label-lg" dangerouslySetInnerHTML={{ __html: faq.question }} />,
                  children: <div className="p-4 text-body-lg" dangerouslySetInnerHTML={{ __html: faq.answer }} />,
                  classNames: { header: 'p-4' },
                },
              ]}
              expandIconPosition="end"
            />
          </div>
        );
      })}
    </div>
  );
};

export default CourseDetailFaq;
