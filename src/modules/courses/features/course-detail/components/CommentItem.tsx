import { Icon } from '@/components/client';
import { Typography } from '@/components/ui';
import { Comment } from '@/features/courses';
import { StarIcon } from '@heroicons/react/16/solid';
import Avatar from 'antd/es/avatar';
import Space from 'antd/es/space';
import dayjs from 'dayjs';

const genLabelCommentTime = (comment: Comment) => {
  const updatedBy = comment.updatedBy;

  const dateCreated = dayjs().diff(dayjs(updatedBy), 'second');

  const ONE_MINUTE = 60;

  const ONE_HOUR = 3600;

  const ONE_MONTH = 2592000;

  const ONE_DAY = 86400;

  if (!updatedBy) return '0 giây trước';

  if (dateCreated >= ONE_MONTH) {
    return dayjs(updatedBy).format('DD/MM/YYYY lúc HH:mm');
  }

  if (dateCreated >= ONE_DAY) {
    return `${Math.round(dateCreated / ONE_DAY)} ngày trước`;
  }

  if (dateCreated >= ONE_HOUR) {
    return `${Math.round(dateCreated / ONE_HOUR)} giờ trước`;
  }

  if (dateCreated >= ONE_MINUTE) {
    return `${Math.round(dateCreated / ONE_MINUTE)} phút trước`;
  }

  return `${Math.round(dateCreated)} giây trước`;
};

const CommentItem = ({ commentData }: { commentData: Comment }) => {
  return (
    <div className="rounded-lg bg-neutral-50 p-6">
      <Space className="w-full" direction="vertical" size={16}>
        <Space align="start">
          <Avatar
            shape="square"
            size={48}
            src={commentData.user.avatar || '/images/avatar.png'}
            alt={commentData.user.name}
          />

          <Space align="start" direction="vertical" size={4}>
            <div className="font-semibold">{commentData.user.name}</div>

            <Space className="w-full" align="center">
              <Space size={2}>
                <Typography variant="labelMd">{commentData.rating}</Typography>
                <Icon size="sm" icon={<StarIcon className="text-yellow-500" />} />
              </Space>

              <Typography variant="bodyMd" className="text-ink-800">
                {genLabelCommentTime(commentData)}
              </Typography>
            </Space>
          </Space>
        </Space>

        <Typography variant="bodyLg">{commentData.comment}</Typography>
      </Space>
    </div>
  );
};

export default CommentItem;
