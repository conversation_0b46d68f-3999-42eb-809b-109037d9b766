import { Typography } from '@/components/ui';
import { cn } from '@/lib/utils';
import CourseSectionsSummary from '../../../components/course-sections-summary/CourseSectionsSummary';
import CourseSummaryInfo from '../../../components/course-sections-summary/CourseSummaryInfo';
import { CourseInfo } from '../../../types/course.type';
import CourseDescription from '../components/CourseDescription';
import CourseDetailFaq from '../components/CourseDetailFaq';
import CourseDetailHeader from '../components/CourseDetailHeader';
import CourseDetailOverview from '../components/CourseDetailOverview';
import CourseDetailReview from '../components/CourseDetailReview';

export const CourseDetailPage = async ({ courseInfo }: { courseInfo: CourseInfo }) => {
  return (
    <div>
      <CourseDetailHeader courseInfo={courseInfo} />

      <div className={cn('flex w-2/3 flex-col gap-6 p-8')}>
        <CourseDescription description={courseInfo.courseDescription} />

        <div className="flex size-full flex-col gap-4">
          <div className="flex w-full justify-between">
            <Typography variant="headlineXs" className="w-fit">
              Nội dung khóa học
            </Typography>

            <CourseSummaryInfo
              totalSections={courseInfo.sections?.length ?? 0}
              totalDuration={Math.round(Number(courseInfo.courseDuration ?? 0))}
            />
          </div>

          <CourseSectionsSummary courseDetail={courseInfo} />
        </div>

        <div className="flex flex-col gap-4 pb-12 min-[1200px]:max-w-[752px]">
          <div className={'flex w-full justify-center md:hidden'}>
            <div className={'relative m-auto w-full max-w-[320px] rounded-lg border border-[#f0f0f0]'}>
              <CourseDetailOverview courseInfo={courseInfo} />
            </div>
          </div>

          <CourseDetailReview courseInfo={courseInfo} />

          <div className="my-8 h-px w-full bg-ink-100" />

          <CourseDetailFaq faqs={courseInfo?.courseFaq} />
        </div>
      </div>
    </div>
  );
};

export default CourseDetailPage;
