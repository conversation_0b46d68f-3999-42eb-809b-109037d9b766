'use client';

import { Button, Typography } from '@/components/ui';
import { routePaths } from '@/config';
import { ERROR_MESSAGES } from '@/modules/courses/constants/messages.const';
import { Result } from 'antd';
import { useRouter } from 'next/navigation';

export default function ExpiredEditCoursePage() {
  const router = useRouter();

  return (
    <div className="flex items-center justify-center">
      <Result
        status="403"
        title={<Typography variant="headlineXs">{ERROR_MESSAGES.EDIT_COURSE_EXPIRED}</Typography>}
        extra={
          <div className="flex justify-center">
            <Button variant="primary" onClick={() => router.push(routePaths.profile.children.creator.path)}>
              Trở về danh sách khóa học
            </Button>
          </div>
        }
      />
    </div>
  );
}
