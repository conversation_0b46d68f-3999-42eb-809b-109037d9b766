'use client';

import { Button, Typography } from '@/components/ui';
import { routePaths } from '@/config';
import { HttpStatusCode } from '@/constants/api';
import { useNotification, useSafeSearchParams } from '@/hooks';
import { handleAsync } from '@/lib/handle-async';
import CourseDraftService from '@/modules/courses/services/course-draft.service';
import { useParams, useRouter } from 'next/navigation';
import React from 'react';

const getErrorMessage = (statusCode: number | undefined) => {
  if (!statusCode) return 'Xác nhận chỉnh sửa khóa học thất bại';

  const errorMapping = {
    [HttpStatusCode.CONFLICT]: 'Khóa học đã được xác nhận trước đó hoặc đã hết hạn chỉnh sửa.',
  } as Record<number, string>;

  const errorMessage = errorMapping[statusCode] || 'Xác nhận chỉnh sửa khóa học thất bại';
  return errorMessage;
};

export default function ConfirmEditCoursePage() {
  const notification = useNotification();
  const { parsedQueryParams } = useSafeSearchParams<{ edit_more: string; proceed: string }>();

  const params = useParams<{ courseId: string }>();
  const courseId = params.courseId;

  const router = useRouter();

  React.useEffect(function handleProceedCourseDraftConfirmation() {
    if (CourseDraftService.isProceed(parsedQueryParams.proceed)) {
      (async () => {
        const confirmState = CourseDraftService.getConfirmationState(parsedQueryParams);
        const [err] = await handleAsync(CourseDraftService.handleConfirmDraftChanges({ courseId, confirmState }));

        const errorMessage = getErrorMessage(err?.status);
        const message = err && err?.status ? errorMessage : 'Xác nhận chỉnh sửa khóa học thành công';
        const notificationType = err ? 'error' : 'success';
        notification[notificationType]({ message });
      })();
    }
  }, []);

  return (
    <div className="flex size-full items-center justify-center bg-white p-6">
      <Typography variant="headlineXs">Xác nhận chỉnh sửa khóa học</Typography>

      <Button variant="primary" onClick={() => router.push(routePaths.profile.children.creator.path)}>
        Trở về danh sách khóa học
      </Button>
    </div>
  );
}
