import { Typography } from '@/components/ui';
import Progress from 'antd/es/progress';

export default function CourseProgress({ progressPercent }: { progressPercent: number | undefined }) {
  return (
    <div className="flex flex-col gap-2">
      {<Progress percent={progressPercent ?? 0} status="normal" showInfo={false} />}
      <Typography variant="labelMd" className="text-secondary_text">
        {Math.floor(progressPercent ?? 0)}% <PERSON><PERSON><PERSON> thành
      </Typography>
    </div>
  );
}
