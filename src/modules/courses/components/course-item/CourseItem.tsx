'use client';
import { Icon, SmartLink } from '@/components/client';
import { Typography } from '@/components/ui';
import { routePaths } from '@/config';
import { cn } from '@/lib/utils';
import { formatApiUrl } from '@/utils/url.util';
import { PlayIcon, StarIcon } from '@heroicons/react/24/solid';
import Image from 'next/image';
import React from 'react';

export type CourseItemProps = {
  courseData: { courseName: string; courseId: string; courseThumbnail: string };

  isFavorite?: boolean;
  creatorName: string;
  topicName?: string;
  footer?: React.ReactNode;

  onFavorite?: (isFavorite: boolean) => void;
};

function CourseItem(props: Readonly<CourseItemProps>) {
  const { courseData, isFavorite, creatorName, topicName, footer, onFavorite = () => {} } = props;

  const [isHoverImage, setIsHoverImage] = React.useState(false);

  const courseDetailUrl = formatApiUrl(routePaths.profile.children.course.children.detail.path, {
    id: courseData.courseId,
  });

  return (
    <SmartLink className="w-full" href={courseDetailUrl} scroll={true}>
      <div
        className={cn(
          'flex cursor-pointer flex-col gap-3 p-3',
          'rounded-xl border border-neutral-100',
          'hover:border-neutral-100 hover:shadow-xl',
        )}
      >
        <div
          className="relative"
          onMouseEnter={() => setIsHoverImage(true)}
          onMouseLeave={() => setIsHoverImage(false)}
        >
          <Image
            src={courseData.courseThumbnail}
            alt="image_course"
            height={200}
            width={238}
            className={cn('h-[180px] w-full rounded-lg object-cover')}
          />
          <div
            className={cn(
              'absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2',
              'rounded-full bg-base-white-50 p-2 transition-all duration-300',
              !isHoverImage && 'hidden',
            )}
          >
            <Icon icon={<PlayIcon className="text-white" />} />
          </div>

          {isFavorite && (
            <div
              className={cn(
                'absolute right-0 top-0 z-10 -translate-x-[8px] translate-y-[8px]',
                'rounded-full bg-base-black-50 p-2 transition-all duration-300',
              )}
              onClick={() => onFavorite(!isFavorite)}
            >
              <Icon icon={<StarIcon className="text-yellow-500" />} />
            </div>
          )}
        </div>

        <div className="flex size-full flex-col gap-3">
          <div className="flex flex-col gap-1">
            {topicName && (
              <Typography variant="labelSm" title={topicName} className="text-primary-500">
                {topicName}
              </Typography>
            )}
            <Typography variant="labelLg">{courseData.courseName}</Typography>
            <Typography variant="labelMd" className="text-secondary_text">
              {creatorName}
            </Typography>
          </div>

          {footer}
        </div>
      </div>
    </SmartLink>
  );
}

export default CourseItem;
