import { Icon } from '@/components/client';
import { Typography } from '@/components/ui';
import { StarIcon } from '@heroicons/react/16/solid';

type CourseRatingProps = {
  reviewCount: number;
  rating: number;
};

const CourseRating = (props: CourseRatingProps) => {
  const { reviewCount, rating } = props;

  return (
    <div className="flex items-center gap-1">
      <div className="flex items-center gap-0.5">
        <Icon size="sm" icon={<StarIcon className="text-yellow-500" />} />
        <Typography variant="labelSm" className="text-ink-black">
          {rating}
        </Typography>
      </div>

      <Typography variant="labelSm" className="text-secondary_text">
        ({reviewCount} reviews)
      </Typography>
    </div>
  );
};

export default CourseRating;
