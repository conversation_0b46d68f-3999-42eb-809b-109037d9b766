import { Icon } from '@/components/client';
import { Typography } from '@/components/ui';
import { StarIcon } from '@/icons';

type CourseOverviewProps = {
  sectionsCount: number;
  duration: number;
};

function secondsToHour(seconds: number) {
  return Math.floor(seconds / 3600);
}

const CourseOverview = (props: CourseOverviewProps) => {
  const { duration, sectionsCount } = props;

  return (
    <div className="flex items-center gap-1">
      <Typography variant="labelSm" className="text-secondary_text">
        {sectionsCount} chương
      </Typography>
      <Icon className="size-2 text-secondary_text" icon={<StarIcon />} />
      <Typography variant="labelSm" className="text-secondary_text">
        {secondsToHour(duration)} giờ học
      </Typography>
    </div>
  );
};

export default CourseOverview;
