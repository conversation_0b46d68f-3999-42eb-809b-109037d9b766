'use client';
import { Icon } from '@/components/client';
import { Button, Input, InputProps } from '@/components/ui';
import { cn } from '@/lib/utils';
import { CheckIcon, XMarkIcon } from '@heroicons/react/16/solid';
import { Popover, PopoverProps } from 'antd';
import React from 'react';

export default function InputEditor(props: {
  value?: string;
  placeholder?: string;
  rootProps?: PopoverProps;
  wrapProps?: React.HTMLAttributes<HTMLDivElement>;
  inputProps?: InputProps;
  ref?: React.Ref<HTMLDivElement>;

  onCancel?: () => void;
  onEdit?: (value: string) => void;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
}) {
  const {
    value = '',
    placeholder = '',
    ref,
    rootProps,
    inputProps,
    wrapProps,
    onEdit = () => {},
    onCancel = () => {},
    onChange = () => {},
  } = props;

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange?.(e);
  };

  const handleEdit = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation();
    onEdit?.(value);
  };

  const handleCancel = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation();
    onCancel?.();
  };

  return (
    <Popover placement="bottom" arrow={false} {...rootProps}>
      <div
        {...wrapProps}
        className={cn(
          'relative z-10 flex w-full items-center space-x-2 rounded-md bg-white p-2 shadow-light',
          wrapProps?.className,
        )}
        ref={ref as React.Ref<HTMLDivElement>}
      >
        <Input
          {...inputProps}
          placeholder={placeholder}
          value={value}
          onClick={(e) => {
            e.stopPropagation();
            inputProps?.onClick?.(e);
          }}
          onChange={handleChange}
          className={cn(
            'w-full rounded-lg border border-blue-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200',
            inputProps?.className,
          )}
        />
        <div className="absolute -bottom-14 right-0 flex gap-2">
          <Button
            size="small"
            className="border border-neutral-100 shadow-light"
            variant="tertiary"
            onClick={handleEdit}
            disabled={!value}
          >
            <Icon icon={<CheckIcon />} className={cn(!value && 'text-disabled_text')} />
          </Button>
          <Button
            size="small"
            variant="tertiary"
            className="border border-neutral-100 shadow-light"
            onClick={handleCancel}
          >
            <Icon icon={<XMarkIcon />} />
          </Button>
        </div>
      </div>
    </Popover>
  );
}
