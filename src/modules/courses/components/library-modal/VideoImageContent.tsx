import CardContentSkeleton from '@/modules/courses/components/library-modal/CardContentSkeleton';
import EmptyData from '@/modules/courses/components/library-modal/EmptyData';
import { useLibraryModalProvider } from '@/modules/courses/components/library-modal/LibraryModalProvider';
import LibraryCard from './LibraryCard';

type Props = {
  selectMode?: 'single' | 'multiple';
  selected: Set<string>;
  onSelect: (id: string) => void;
  onSelectMultiple?: (ids: string[]) => void;
};

const VideoImageContent = ({ selectMode, selected, onSelect, onSelectMultiple }: Props) => {
  const { files, isLoading, limitSize } = useLibraryModalProvider();

  if (isLoading) {
    return <CardContentSkeleton />;
  }

  if (!files.length) {
    return <EmptyData />;
  }

  const handleSelect = (id: string) => {
    if (selectMode === 'multiple' && onSelectMultiple) {
      const newSelected = new Set(selected);
      if (newSelected.has(id)) {
        newSelected.delete(id);
      } else {
        newSelected.add(id);
      }
      onSelectMultiple(Array.from(newSelected));
    } else {
      onSelect(id);
    }
  };

  return (
    <div className="flex flex-wrap gap-4">
      {files?.map((file) => {
        const fileSizeInKB = file.fileSize / 1024;
        const exceedsLimit = limitSize ? fileSizeInKB > limitSize : false;

        return (
          <div key={file.id}>
            <LibraryCard
              selected={selected.has(file.id)}
              onSelect={() => !exceedsLimit && handleSelect(file.id)}
              fileData={file}
              disabled={exceedsLimit}
            />
          </div>
        );
      })}
    </div>
  );
};

export default VideoImageContent;
