import { Typography } from '@/components/ui';
import Image from 'next/image';

import { useLibraryModalProvider } from '@/modules/courses/components/library-modal/LibraryModalProvider';
import { LibraryFileType, libraryFileTypeName } from '@/modules/courses/constants/file.const';
import { match } from 'ts-pattern';

function EmptyData() {
  const { selectedType } = useLibraryModalProvider();

  const typeName = libraryFileTypeName[selectedType] || '';

  const emptyDataBackgroundImage = match(selectedType)
    .with(LibraryFileType.VIDEO, () => (
      <Image src={'/upload/light-bg-video.svg'} width={80} height={80} alt="background video" />
    ))
    .with(LibraryFileType.IMAGE, () => (
      <Image src={'/upload/light-bg-image.svg'} width={80} height={80} alt="background image" />
    ))
    .with(LibraryFileType.DOCUMENT, () => (
      <Image src={'/upload/light-bg-document.svg'} width={80} height={80} alt="background documents" />
    ))
    .exhaustive();

  return (
    <div className="flex h-full flex-col items-center justify-center gap-2">
      {emptyDataBackgroundImage}
      <Typography variant="labelLg" className="text-ink-black">
        Thư viện của bạn đang trống!
      </Typography>
      <Typography variant="bodyMd" className="text-ink-black">
        Hãy bắt đầu bằng cách tải lên {typeName} để sử dụng trong bài giảng.
      </Typography>
    </div>
  );
}

export default EmptyData;
