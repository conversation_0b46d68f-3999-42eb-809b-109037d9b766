'use client';

import { Icon } from '@/components/client/icon';
import { <PERSON>ton, Modal, Typography } from '@/components/ui';
import { cn } from '@/lib/utils';
import { InputEditor } from '@/modules/courses/components/input-editor';
import { useLibraryModalProvider } from '@/modules/courses/components/library-modal/LibraryModalProvider';
import { useFileActions } from '@/modules/courses/components/library-modal/useFileActions';
import { LibraryFileType, libraryFileTypeName } from '@/modules/courses/constants/file.const';
import { LibraryFile } from '@/modules/courses/types/file.type';
import { convertFileSize, downloadFile } from '@/utils';
import { stopPropagationHandler } from '@/utils/event';
import { CheckCircleIcon } from '@heroicons/react/16/solid';
import { ArrowDownIcon, EllipsisHorizontalIcon, PencilIcon, TrashIcon } from '@heroicons/react/24/outline';
import { Dropdown } from 'antd';
import { MenuProps } from 'antd/lib';
import dayjs from 'dayjs';
import Image from 'next/image';
import React from 'react';

const renderDeleteButtonText = (selectedType: LibraryFileType) => {
  if (selectedType === LibraryFileType.IMAGE) {
    return 'Xóa ảnh';
  }
  if (selectedType === LibraryFileType.VIDEO) {
    return 'Xóa video';
  }

  return 'Xóa';
};

const DeleteConfirmModal = ({
  title,
  open,
  content,
  isDeleting,
  onClose,
  onDelete,
}: {
  title: string;
  open: boolean;
  content: React.ReactNode;
  isDeleting: boolean;
  onClose: () => void;
  onDelete: () => void;
}) => {
  const { selectedType } = useLibraryModalProvider();

  return (
    <Modal
      open={open}
      height={300}
      title={title}
      onClose={onClose}
      footer={
        <div className="flex justify-end gap-4 p-6">
          <Button variant="tertiary" size="large" onClick={onClose}>
            Hủy
          </Button>
          <Button variant="error" size="large" onClick={onDelete} loading={isDeleting}>
            {renderDeleteButtonText(selectedType)}
          </Button>
        </div>
      }
    >
      <div className="px-6 py-4">{content}</div>
    </Modal>
  );
};

type LibraryCardProps = {
  fileData: LibraryFile;
  selected: boolean;
  disabled?: boolean;
  onSelect: () => void;
};

function LibraryCard(props: LibraryCardProps) {
  const { fileData, selected, disabled = false, onSelect } = props;

  const { selectedType, refetch } = useLibraryModalProvider();

  const { deleteFileMutation, updateFileNameMutation } = useFileActions();

  const cardRef = React.useRef<HTMLDivElement>(null);

  const [isHover, setIsHover] = React.useState(false);
  const [openMenu, setOpenMenu] = React.useState(false);
  const [fileName, setFileName] = React.useState(fileData.fileName);

  const [openEditFileNameModal, setOpenEditFileNameModal] = React.useState(false);
  const [openDeleteConfirmModal, setOpenDeleteConfirmModal] = React.useState(false);

  const libraryCardActions = [
    {
      key: 'download',
      label: <Typography variant="bodyLg">Tải về</Typography>,
      icon: <Icon size="sm" icon={<ArrowDownIcon />} />,
      onClick: stopPropagationHandler(async () => {
        await downloadFile(fileData.fileUrl, fileData.fileName);
        setOpenMenu(false);
      }),
    },
    {
      key: 'edit',
      label: <Typography variant="bodyLg">Chỉnh sửa tên</Typography>,
      icon: <Icon size="sm" icon={<PencilIcon />} />,
      onClick: stopPropagationHandler(() => {
        setOpenEditFileNameModal(true);
      }),
    },
    {
      key: 'delete',
      label: <Typography variant="bodyLg">Xóa</Typography>,
      icon: <Icon size="sm" icon={<TrashIcon />} />,
      onClick: stopPropagationHandler(() => setOpenDeleteConfirmModal(true)),
    },
  ] satisfies MenuProps['items'];

  const handleEditFileName = (fileName: string) => {
    if (fileName !== fileData.fileName) {
      updateFileNameMutation.mutate(
        { id: fileData.id, fileName },
        {
          onSuccess: () => {
            refetch();
          },
        },
      );
    }

    setOpenEditFileNameModal(false);
  };

  const handleDeleteFile = () => {
    deleteFileMutation.mutate(
      { id: fileData.id },
      {
        onSuccess: () => {
          refetch();
        },
      },
    );
    setOpenDeleteConfirmModal(false);
  };

  const renderThumbnail = () => {
    if (fileData.fileType === LibraryFileType.VIDEO) {
      return (
        <video width="300" height="200" controls={false} className="h-[180px] rounded-lg object-scale-down">
          <source src={fileData.fileUrl} type="video/mp4" />
        </video>
      );
    }

    return (
      <Image
        src={fileData.fileUrl}
        alt={fileData.fileName || 'file uploaded'}
        className="h-[180px] rounded-lg object-scale-down"
        width={300}
        height={200}
        sizes="(max-width: 300px) 100vw, 235px"
        priority={false}
        unoptimized={false}
        loading="lazy"
      />
    );
  };

  return (
    <React.Fragment>
      <div
        ref={cardRef}
        className={cn(
          'relative h-full w-full max-w-[320px] rounded-lg border border-neutral-100',
          'transition-300 transition-all',
          disabled ? 'cursor-not-allowed opacity-50' : 'cursor-pointer hover:shadow-lg',
        )}
        onClick={disabled ? undefined : onSelect}
        onMouseEnter={() => !disabled && setIsHover(true)}
        onMouseLeave={() => !disabled && setIsHover(false)}
      >
        <div className="flex size-full flex-col gap-2 p-2">
          {renderThumbnail()}

          <div className="flex h-full flex-col justify-end gap-2">
            <Typography variant="labelMd" className="h-10 truncate" title={fileData.fileName}>
              {fileData.fileName}
            </Typography>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-1">
                <Typography variant="bodySm">{convertFileSize(fileData.fileSize)}</Typography>

                <span className="text-secondary_text">&#8226;</span>
                <Typography variant="bodySm">{dayjs(fileData.updatedAt).format('DD/MM/YYYY')}</Typography>
              </div>

              {!disabled && (
                <Dropdown
                  trigger={['click']}
                  open={openMenu}
                  menu={{ items: libraryCardActions }}
                  onOpenChange={(open) => setOpenMenu(open)}
                >
                  <div className="cursor-pointer">
                    <Icon
                      icon={<EllipsisHorizontalIcon />}
                      onClick={(e) => {
                        e.stopPropagation();
                        setOpenMenu((prev) => !prev);
                      }}
                    />
                  </div>
                </Dropdown>
              )}
            </div>
          </div>
        </div>

        {openDeleteConfirmModal && (
          <DeleteConfirmModal
            title={`Xóa ${libraryFileTypeName[selectedType]}`}
            open={openDeleteConfirmModal}
            isDeleting={deleteFileMutation.isLoading}
            content={<div>Bạn có chắc chắn muốn xóa {libraryFileTypeName[selectedType]} này không?</div>}
            onClose={() => setOpenDeleteConfirmModal(false)}
            onDelete={handleDeleteFile}
          />
        )}

        {openEditFileNameModal && (
          <div className={cn('absolute top-56 z-50')} style={{ width: cardRef.current?.clientWidth }}>
            <InputEditor
              key={fileData.id}
              value={fileName}
              onCancel={() => setOpenEditFileNameModal(false)}
              onEdit={handleEditFileName}
              onChange={(e) => setFileName(e.target.value)}
            />
          </div>
        )}

        {!disabled && !selected && isHover && (
          <div className="absolute right-4 top-4">
            <div className="size-4 rounded-full border border-white bg-transparent" />
          </div>
        )}

        {!disabled && selected && (
          <div className="absolute right-3 top-3">
            <Icon icon={<CheckCircleIcon className="rounded-full bg-black" color="#FFFFFF" />} />
          </div>
        )}
      </div>
    </React.Fragment>
  );
}

export default LibraryCard;
