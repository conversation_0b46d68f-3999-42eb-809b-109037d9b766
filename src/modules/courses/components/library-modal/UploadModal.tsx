'use client';

import { Icon } from '@/components/client/icon';
import { Button, Modal, Typography, Upload } from '@/components/ui';
import { PaperClipIcon, XMarkIcon } from '@heroicons/react/16/solid';
import { XCircleIcon } from '@heroicons/react/24/outline';
import Image from 'next/image';
import React from 'react';

import useS3Uploader from '@/hooks/useS3Uploader';

import { palette } from '@/config/theme';
import { usePreventHydration } from '@/hooks';
import {
  ALLOWED_DOC_TYPES,
  ALLOWED_IMAGE_TYPES,
  ALLOWED_VIDEO_TYPES,
  validateDocumentFile,
  validateImageFile,
  validateVideoFile,
} from '@/lib/helpers/fileValidation';
import { cn } from '@/lib/utils';
import { useLibraryModalProvider } from '@/modules/courses/components/library-modal/LibraryModalProvider';
import { useFileActions } from '@/modules/courses/components/library-modal/useFileActions';
import { LibraryFileType } from '@/modules/courses/constants/file.const';
import { generateThumbnail, getDurationFromVideoFile } from '@/utils';
import { Button as AntButton, App, notification, Progress } from 'antd';
import { match } from 'ts-pattern';

const fileValidationMapping = {
  [LibraryFileType.VIDEO]: ALLOWED_VIDEO_TYPES,
  [LibraryFileType.IMAGE]: ALLOWED_IMAGE_TYPES,
  [LibraryFileType.DOCUMENT]: ALLOWED_DOC_TYPES,
};

const FileTypeDescription = () => {
  const { selectedType } = useLibraryModalProvider();

  const renderFileTypeDescription = () => {
    return match(selectedType)
      .with(LibraryFileType.VIDEO, () => (
        <React.Fragment>
          <div className="flex items-center gap-1 text-ink-700">
            <span>&#8226;</span>
            <Typography variant="bodySm">Tỉ lệ khung video 16:9</Typography>
          </div>
          <div className="flex items-center gap-1 text-ink-700">
            <span>&#8226;</span>
            <Typography variant="bodySm">Chỉ hỗ trợ tệp có đuôi .MP4 .MOV</Typography>
          </div>
        </React.Fragment>
      ))
      .with(LibraryFileType.IMAGE, () => (
        <div className="flex w-full items-center justify-center gap-1">
          <Typography variant="bodySm" className="text-ink-700">
            Chỉ hỗ trợ tệp có đuôi .PNG, .JPEG
          </Typography>
        </div>
      ))
      .with(LibraryFileType.DOCUMENT, () => (
        <div className="flex w-full items-center justify-center gap-1">
          <Typography variant="bodySm" className="text-ink-700">
            Chỉ hỗ trợ tệp có đuôi .PDF, .DOC, .DOCX
          </Typography>
        </div>
      ))
      .exhaustive();
  };

  return (
    <React.Fragment>
      <div className="flex w-full justify-center gap-4">{renderFileTypeDescription()}</div>
    </React.Fragment>
  );
};

const FileSizeDescription = ({ limitSize }: { limitSize?: number }) => {
  const { selectedType } = useLibraryModalProvider();

  const formatFileSize = (size: number) => {
    if (size >= 1024 * 1024) {
      return `${(size / (1024 * 1024)).toFixed(1)}GB`;
    } else if (size >= 1024) {
      return `${(size / 1024).toFixed(1)}MB`;
    }
    return `${size}KB`;
  };

  const fileSizeDescription = limitSize
    ? `(Dung lượng tối đa ${formatFileSize(limitSize)})`
    : match(selectedType)
        .with(LibraryFileType.VIDEO, () => '(Dung lượng tối đa 2GB)')
        .with(LibraryFileType.IMAGE, () => '(Dung lượng tối đa 20MB)')
        .with(LibraryFileType.DOCUMENT, () => '(Dung lượng tối đa 20MB)')
        .exhaustive();

  return (
    <React.Fragment>
      <Typography variant="bodySm" className="text-ink-700">
        {fileSizeDescription}
      </Typography>
    </React.Fragment>
  );
};

const BackgroundImageByType = () => {
  const { selectedType } = useLibraryModalProvider();

  const backgroundImage = match(selectedType)
    .with(LibraryFileType.VIDEO, () => (
      <Image
        src={'/upload/light-bg-video.svg'}
        alt="background video"
        width={100}
        height={100}
        style={{ width: 'auto', height: 'auto' }}
      />
    ))
    .with(LibraryFileType.IMAGE, () => (
      <Image
        src={'/upload/light-bg-image.svg'}
        alt="background image"
        width={100}
        height={100}
        style={{ width: 'auto', height: 'auto' }}
      />
    ))
    .with(LibraryFileType.DOCUMENT, () => (
      <Image
        src={'/upload/light-bg-document.svg'}
        alt="background documents"
        width={100}
        height={100}
        style={{ width: 'auto', height: 'auto' }}
      />
    ))
    .otherwise(() => null);

  return <React.Fragment>{backgroundImage}</React.Fragment>;
};

function UploadModal(props: { open: boolean; onClose: () => void; limitSize?: number }) {
  const { open, onClose, limitSize } = props;

  usePreventHydration();

  const { message: messageApp, notification: notificationApp } = App.useApp();

  const { selectedType, refetch } = useLibraryModalProvider();

  const { uploadFileMutation } = useFileActions();
  const { isUploading: isVideoUploading, progress, uploadFile: uploadVideo, abortUpload } = useS3Uploader();

  const videoRef = React.useRef<HTMLVideoElement>(null);

  const [imageFile, setImageFile] = React.useState<File | null>(null);
  const [videoFile, setVideoFile] = React.useState<File | null>(null);
  const [documentFile, setDocumentFile] = React.useState<File | null>(null);
  const [thumbnail, setThumbnail] = React.useState<string | null>(null);

  const shouldShowPreviewImage = imageFile || thumbnail;
  const shouldShowDocumentPreview = documentFile;
  const fileAccepts = fileValidationMapping[selectedType];

  const loading = uploadFileMutation.isLoading || isVideoUploading;

  const validateFile = (file: File) => {
    // Use limitSize if provided, otherwise use default limits
    const fileSizeInKB = file.size / 1024;

    if (limitSize && fileSizeInKB > limitSize) {
      messageApp.warning(`Tệp không được vượt quá ${limitSize}KB.`);
      return false;
    }

    // Default validation for file types and standard size limits
    const fileValidator = {
      [LibraryFileType.VIDEO]: () => validateVideoFile(file, 2048),
      [LibraryFileType.IMAGE]: () => validateImageFile(file, 20),
      [LibraryFileType.DOCUMENT]: () => validateDocumentFile(file, 20),
    };

    const validator = fileValidator[selectedType];
    const result = validator();

    const message = match(selectedType)
      .with(LibraryFileType.VIDEO, () => 'Video không được vượt quá 2GB.')
      .with(LibraryFileType.IMAGE, () => 'Ảnh không được vượt quá 20MB.')
      .with(LibraryFileType.DOCUMENT, () => 'Tài liệu không được vượt quá 20MB.')
      .exhaustive();

    if (!result?.isValid) {
      messageApp.warning(message);
      return false;
    }

    return true;
  };

  const resetFileData = () => {
    if (isVideoUploading) {
      abortUpload();
    }

    setImageFile(null);
    setVideoFile(null);
    setDocumentFile(null);
    setThumbnail(null);
  };

  const handleAddToLibrary = async () => {
    if (selectedType === LibraryFileType.IMAGE && imageFile) {
      const formData = new FormData();
      formData.append('file', imageFile);

      uploadFileMutation.mutate(
        { payload: formData },
        {
          onSuccess: () => {
            refetch();
            onClose();
          },
        },
      );
      return;
    }

    if (selectedType === LibraryFileType.VIDEO && videoFile) {
      const duration = await getDurationFromVideoFile(videoFile);

      await uploadVideo(
        { file: videoFile, fileDuration: Math.ceil(duration) },
        {
          onSuccess: () => {
            notificationApp.success({ message: 'Upload video thành công' });
            refetch();
            onClose();
          },
          onError: (error) => {
            const err = error as ErrorOptions & { cause: { aborted: boolean } };
            if (err.cause?.aborted) return;
            onClose();
            notificationApp.error({ message: 'Upload video thất bại' });
          },
        },
      );
    }

    if (selectedType === LibraryFileType.DOCUMENT && documentFile) {
      const formData = new FormData();
      formData.append('file', documentFile);

      uploadFileMutation.mutate(
        { payload: formData },
        {
          onSuccess: () => {
            notification.success({ message: 'Upload tài liệu thành công' });
            refetch();
            onClose();
          },
          onError: () => {
            notification.error({ message: 'Upload tài liệu thất bại' });
            onClose();
          },
        },
      );
      return;
    }
  };

  const handleChangeFile = async (file: File) => {
    if (selectedType === LibraryFileType.IMAGE) {
      setImageFile(file);
    }

    if (selectedType === LibraryFileType.VIDEO && file && videoRef.current) {
      await generateThumbnail({ file, video: videoRef.current, onSuccess: (thumbnail) => setThumbnail(thumbnail) });
      setVideoFile(file);
    }

    if (selectedType === LibraryFileType.DOCUMENT) {
      setDocumentFile(file);
    }
  };

  return (
    <Modal
      open={open}
      title="Tải tệp lên từ máy tính"
      footer={
        shouldShowPreviewImage || shouldShowDocumentPreview ? (
          <div className="flex justify-end px-6 pb-6 pt-3">
            <Button onClick={handleAddToLibrary} loading={loading}>
              Thêm vào thư viện
            </Button>
          </div>
        ) : null
      }
      onClose={() => {
        resetFileData();
        onClose();
      }}
    >
      <div className={cn('size-full px-6 py-6 pt-2')}>
        {shouldShowPreviewImage ? (
          <div className="relative flex size-full items-center justify-center">
            <Image
              width={500}
              height={300}
              className={cn('h-[300px] w-full rounded-lg object-cover', loading && 'opacity-50')}
              src={imageFile ? URL.createObjectURL(imageFile) : thumbnail!}
              alt="thumbnail uploaded"
            />

            {loading && (
              <div className="absolute inset-x-0 bottom-0 translate-y-1/4">
                <Progress
                  percent={progress}
                  size="small"
                  strokeLinecap="round"
                  trailColor={palette.primary[200]}
                  strokeColor={palette.primary[300]}
                  showInfo={false}
                />
              </div>
            )}

            <div className="absolute right-2 top-2 rounded-full bg-primary_text p-1">
              <Icon
                size="md"
                className="cursor-pointer text-white"
                icon={<XMarkIcon className="text-white" />}
                onClick={resetFileData}
              />
            </div>
          </div>
        ) : shouldShowDocumentPreview ? (
          <div className="relative flex h-72 flex-col items-center justify-center rounded-lg border border-neutral-200 bg-neutral-50 p-4">
            <AntButton
              type="link"
              size="small"
              icon={<Icon size="lg" icon={<XCircleIcon fill="#0C0C0C" color="#fff" />} />}
              className="absolute right-2 top-2"
              onClick={resetFileData}
            />

            <Typography variant="labelLg">Tải lên thành công!</Typography>
            <div
              className="flex cursor-pointer items-center gap-2"
              onClick={() => window.open(URL.createObjectURL(documentFile), '_blank')}
            >
              <Icon icon={<PaperClipIcon className="text-neutral-500" />} />
              <Typography variant="labelMd" className="text-blue-500">
                {documentFile.name}
              </Typography>
            </div>
          </div>
        ) : (
          <Upload.Dragger
            className="rounded-lg bg-neutral-50"
            accept={fileAccepts as unknown as string[]}
            showUploadList={false}
            multiple={false}
            customRequest={(options) => {
              const file = options.file as File;
              const isValid = validateFile(file);
              if (!isValid) return;

              handleChangeFile(file);
            }}
          >
            {/* Hidden video element for thumbnail generation */}
            <video ref={videoRef} style={{ display: 'none' }} />

            <div className="flex flex-col gap-7 p-5">
              <div className="flex flex-col items-center justify-center gap-3">
                <BackgroundImageByType />
                <div className="flex flex-col items-center justify-center gap-2">
                  <Typography variant="labelLg">Kéo và thả tệp của bạn vào đây, hoặc</Typography>

                  <Button size="small" variant="primary">
                    Chọn tệp từ máy tính
                  </Button>

                  <FileSizeDescription limitSize={limitSize} />
                </div>
              </div>

              <FileTypeDescription />
            </div>
          </Upload.Dragger>
        )}
      </div>
    </Modal>
  );
}

export default UploadModal;
