import { Skeleton, Table } from 'antd';
import { ColumnsType } from 'antd/es/table';

// Skeleton data for table rows
const skeletonData = Array.from({ length: 5 }, (_, index) => ({
  id: `skeleton-${index}`,
  fileName: '',
  createdAt: '',
  fileSize: 0,
}));

// Table columns with skeleton content
const skeletonColumns: ColumnsType<any> = [
  {
    title: 'Tên tệp',
    dataIndex: 'fileName',
    key: 'fileName',
    render: () => (
      <div className="flex items-center gap-1">
        <Skeleton.Avatar shape="square" size="small" active />
        <Skeleton.Input style={{ width: 200, height: 16 }} active size="small" />
      </div>
    ),
  },
  {
    title: 'Ng<PERSON>y tải lên',
    dataIndex: 'createdAt',
    key: 'createdAt',
    render: () => <Skeleton.Input style={{ width: 80, height: 16 }} active size="small" />,
  },
  {
    title: 'Dung lượng',
    dataIndex: 'fileSize',
    key: 'fileSize',
    render: () => <Skeleton.Input style={{ width: 60, height: 16 }} active size="small" />,
  },
  {
    title: 'Thao tác',
    key: 'actions',
    render: () => (
      <div className="flex items-center gap-2">
        <Skeleton.Button active size="small" />
        <Skeleton.Button active size="small" />
        <Skeleton.Button active size="small" />
      </div>
    ),
  },
];

const DocumentContentSkeleton = () => {
  return (
    <Table
      columns={skeletonColumns}
      dataSource={skeletonData}
      rowKey="id"
      rowSelection={{
        type: 'checkbox',
        selectedRowKeys: [],
        getCheckboxProps: () => ({ disabled: true }),
      }}
      size="small"
      pagination={false}
      loading={false} // Don't use default loading, we have custom skeleton
    />
  );
};

export default DocumentContentSkeleton;
