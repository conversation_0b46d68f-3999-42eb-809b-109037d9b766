'use client';
import { useClient } from '@/hooks/useClient';
import { LibraryFileType } from '@/modules/courses/constants/file.const';
import { LibraryFile } from '@/modules/courses/types/file.type';
import React from 'react';

type LibraryModalProviderState = {
  selectedType: LibraryFileType;
  isLoading: boolean;
  files: LibraryFile[];

  enabledTypes: LibraryFileType[];
  limitSize?: number;

  refetch: () => void;
};

const LibraryModalContext = React.createContext<LibraryModalProviderState | null>(null);

type LibraryModalProviderProps = {
  children: React.ReactNode;
  value: LibraryModalProviderState | null;
};

export const useLibraryModalProvider = () => {
  const context = React.useContext(LibraryModalContext);

  if (!context) {
    throw new Error('useLibraryModal must be used within a LibraryModalProvider');
  }

  return context as LibraryModalProviderState;
};

export const LibraryModalProvider = ({ children, value }: LibraryModalProviderProps) => {
  const { isClient } = useClient();

  if (!isClient) return null;

  return <LibraryModalContext.Provider value={value}>{children}</LibraryModalContext.Provider>;
};
