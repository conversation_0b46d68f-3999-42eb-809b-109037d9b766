'use client';

import { Icon } from '@/components/client/icon';
import { Button, Typography } from '@/components/ui';
import { cn } from '@/lib/utils';
import { useLibraryModalProvider } from '@/modules/courses/components/library-modal/LibraryModalProvider';
import { ArrowUpTrayIcon, PaperClipIcon, PhotoIcon, VideoCameraIcon } from '@heroicons/react/24/outline';
import React from 'react';
import { LibraryFileType } from '../../constants/file.const';

const libraryTypes = {
  [LibraryFileType.IMAGE]: {
    id: LibraryFileType.IMAGE,
    label: 'Hình ảnh',
    icon: <PhotoIcon />,
  },
  [LibraryFileType.VIDEO]: {
    id: LibraryFileType.VIDEO,
    label: 'Video',
    icon: <VideoCameraIcon />,
  },
  [LibraryFileType.DOCUMENT]: {
    id: LibraryFileType.DOCUMENT,
    label: '<PERSON>ọc liệu',
    icon: <PaperClipIcon />,
  },
};

function LibraryTypes({ onSelectType }: { onSelectType: (type: LibraryFileType) => void }) {
  const { enabledTypes, selectedType } = useLibraryModalProvider();

  return (
    <React.Fragment>
      <Typography variant="labelMd" className="flex h-10 items-center px-3">
        Thư viện
      </Typography>

      {enabledTypes?.map((enabledType) => {
        const item = libraryTypes[enabledType];
        if (!item) return null;

        return (
          <div
            key={item.id}
            className={cn(
              'flex w-full items-center gap-2 rounded-lg p-3',
              'cursor-pointer hover:bg-neutral-100',
              selectedType === item.id && 'cursor-default bg-neutral-100',
            )}
            onClick={() => onSelectType(item.id)}
          >
            <Icon icon={item.icon} />
            <Typography variant="labelMd">{item.label}</Typography>
          </div>
        );
      })}
    </React.Fragment>
  );
}

function LibrarySidebar({
  onUpload,
  onSelectType,
}: {
  onUpload: () => void;
  onSelectType: (type: LibraryFileType) => void;
}) {
  return (
    <div className="w-60 border-r border-neutral-200 bg-neutral-50 p-4">
      <div className="flex flex-col gap-2">
        <Button variant="primary" color="primary" className="w-full" onClick={onUpload}>
          <div className="flex items-center gap-2 px-4">
            <Icon icon={<ArrowUpTrayIcon />} />
            <Typography variant="labelMd" className="text-white">
              Tải tệp lên
            </Typography>
          </div>
        </Button>

        <LibraryTypes onSelectType={onSelectType} />
      </div>
    </div>
  );
}

export default LibrarySidebar;
