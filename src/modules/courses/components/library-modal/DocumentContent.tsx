'use client';

import { Icon } from '@/components/client';
import { Button, Input, Typography } from '@/components/ui';
import CardContentSkeleton from '@/modules/courses/components/library-modal/CardContentSkeleton';
import EmptyData from '@/modules/courses/components/library-modal/EmptyData';
import { useLibraryModalProvider } from '@/modules/courses/components/library-modal/LibraryModalProvider';
import { useFileActions } from '@/modules/courses/components/library-modal/useFileActions';
import { LibraryFile } from '@/modules/courses/types/file.type';
import { convertFileSize, downloadFile } from '@/utils';
import { PaperClipIcon } from '@heroicons/react/16/solid';
import { ArrowDownTrayIcon, CheckIcon, PencilIcon, TrashIcon, XMarkIcon } from '@heroicons/react/24/outline';
import { App, Table, Tooltip } from 'antd';
import { ColumnsType, TableProps } from 'antd/es/table';
import dayjs from 'dayjs';
import { useState } from 'react';

type Props = {
  selectMode?: 'single' | 'multiple';
  selected: Set<string>;
  onSelect: (id: string) => void;
  onSelectMultiple: (ids: string[]) => void;
};

const DocumentContent = ({ selectMode = 'multiple', selected, onSelect, onSelectMultiple }: Props) => {
  const { isLoading, files, limitSize, refetch } = useLibraryModalProvider();

  const { deleteFileMutation, updateFileNameMutation } = useFileActions();

  const [editingId, setEditingId] = useState<string | null>(null);
  const [editedValue, setEditedValue] = useState<string>('');

  const { message } = App.useApp();

  if (isLoading) {
    return <CardContentSkeleton />;
  }

  if (!files.length) {
    return <EmptyData />;
  }

  const handleDownload = async (record: LibraryFile) => {
    await downloadFile(record.fileUrl, record.fileName);
  };

  const handleStartEdit = (record: LibraryFile) => {
    setEditingId(record.id);
    setEditedValue(record.fileName);
  };

  const handleSaveEdit = (record: LibraryFile) => {
    updateFileNameMutation.mutate(
      { id: record.id, fileName: editedValue },
      {
        onSuccess: () => {
          refetch();
          handleCancelEdit();
        },
        onError: () => {
          message.error('Có lỗi xảy ra, vui lòng thử lại sau');
        },
      },
    );
  };

  const handleCancelEdit = () => {
    setEditingId(null);
    setEditedValue('');
  };

  const handleDelete = (record: LibraryFile) => {
    deleteFileMutation.mutate(
      { id: record.id },
      {
        onSuccess: () => {
          refetch();
        },
        onError: () => {
          message.error('Có lỗi xảy ra, vui lòng thử lại sau');
        },
      },
    );
  };

  const columns: ColumnsType<LibraryFile> = [
    {
      title: 'Tên tệp',
      dataIndex: 'fileName',
      key: 'fileName',
      render: (fileName: string, record) => (
        <div className="flex cursor-pointer items-center gap-1" onClick={() => handleDownload(record)}>
          <Tooltip title="Mở tệp">
            <Icon icon={<PaperClipIcon className="text-neutral-500" />} />
          </Tooltip>
          {editingId === record.id ? (
            <Input
              autoFocus
              size="sm"
              value={editedValue}
              className="text-sm"
              onChange={(e) => setEditedValue(e.target.value)}
              onPressEnter={() => handleSaveEdit(record)}
              onClick={(e) => e.stopPropagation()}
            />
          ) : (
            <Typography ellipsis={{ rows: 1, tooltip: true }} className="!mb-0 w-full text-sm">
              {fileName}
            </Typography>
          )}
        </div>
      ),
    },
    {
      title: 'Ngày tải lên',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (value) => <Typography>{dayjs(value).format('DD/MM/YYYY')}</Typography>,
    },
    {
      title: 'Dung lượng',
      dataIndex: 'fileSize',
      key: 'fileSize',
      render: (value) => <Typography>{convertFileSize(value)}</Typography>,
    },
    {
      title: 'Thao tác',
      key: 'actions',
      render: (_, record) => (
        <div className="flex items-center gap-2">
          {editingId !== record.id && (
            <>
              <Tooltip title="Tải xuống">
                <Button
                  variant="ghost-reversed"
                  size="small"
                  icon={<Icon icon={<ArrowDownTrayIcon className="text-primary_text" />} />}
                  className="flex items-center justify-center"
                  onClick={() => handleDownload(record)}
                />
              </Tooltip>
              <Tooltip title="Chỉnh sửa">
                <Button
                  disabled={editingId !== null && editingId !== record.id}
                  variant="ghost-reversed"
                  size="small"
                  icon={<Icon icon={<PencilIcon className="text-primary_text" />} />}
                  onClick={() => handleStartEdit(record)}
                />
              </Tooltip>
              <Tooltip title="Xóa">
                <Button
                  variant="ghost-reversed"
                  size="small"
                  icon={<Icon icon={<TrashIcon color="red" />} />}
                  onClick={() => handleDelete(record)}
                />
              </Tooltip>
            </>
          )}
          {editingId === record.id && (
            <>
              <Tooltip title="Lưu">
                <Button
                  variant="ghost-reversed"
                  size="small"
                  icon={<Icon icon={<CheckIcon color="green" />} />}
                  onClick={() => handleSaveEdit(record)}
                />
              </Tooltip>
              <Tooltip title="Hủy">
                <Button
                  variant="ghost-reversed"
                  size="small"
                  icon={<Icon icon={<XMarkIcon color="red" />} />}
                  onClick={handleCancelEdit}
                />
              </Tooltip>
            </>
          )}
        </div>
      ),
    },
  ];

  const rowSelection: TableProps<LibraryFile>['rowSelection'] = {
    selectedRowKeys: Array.from(selected),
    onChange: (selectedRowKeys) => {
      if (selectMode === 'single') {
        const lastSelected = selectedRowKeys[selectedRowKeys.length - 1];
        if (lastSelected) {
          onSelect(lastSelected as string);
        }
      } else {
        onSelectMultiple(selectedRowKeys as string[]);
      }
    },
    getCheckboxProps: (record) => {
      const fileSizeInKB = record.fileSize / 1024;
      const exceedsLimit = limitSize ? fileSizeInKB > limitSize : false;
      return {
        disabled: exceedsLimit,
      };
    },
  };

  return (
    <Table
      columns={columns}
      dataSource={files}
      rowKey="id"
      rowSelection={rowSelection}
      size="small"
      pagination={false}
    />
  );
};

export { DocumentContent };
