import {
  DragDropContext,
  Draggable,
  DraggableProvided,
  DraggableStateSnapshot,
  Droppable,
  DroppableProvided,
  DropResult,
} from '@hello-pangea/dnd';

type RenderItemParams<T> = {
  item: T;
  index: number;
  provided: DraggableProvided;
  snapshot: DraggableStateSnapshot;
};

interface DraggableListProps<T> {
  items: T[];
  droppableId: string;
  renderItem: (params: RenderItemParams<T>) => React.ReactNode;
  onDragEnd: (result: DropResult) => void;
  className?: string;
}

const DraggableList = <T extends { id: string }>(props: DraggableListProps<T>) => {
  const { items, droppableId, renderItem, onDragEnd, className = 'relative space-y-3' } = props;

  return (
    <DragDropContext onDragEnd={onDragEnd}>
      <Droppable droppableId={droppableId}>
        {(provided: DroppableProvided) => (
          <div {...provided.droppableProps} ref={provided.innerRef} className={className}>
            {items.map((item, index) => (
              <Draggable key={item.id} draggableId={String(item.id)} index={index}>
                {(provided, snapshot) => renderItem({ item, index, provided, snapshot })}
              </Draggable>
            ))}
            {provided.placeholder}
          </div>
        )}
      </Droppable>
    </DragDropContext>
  );
};

export default DraggableList;
