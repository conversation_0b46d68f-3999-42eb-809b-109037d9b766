import { Typography } from '@/components/ui';
import { secondsToHHMMSSTextFormat } from '@/lib/dateTime';

type CourseSummaryInfoProps = {
  totalSections: number;
  totalDuration: number;
};

export default function CourseSummaryInfo(props: CourseSummaryInfoProps) {
  const { totalSections, totalDuration } = props;

  return (
    <div className="flex h-full w-fit items-center gap-4">
      <div className="flex items-center gap-2">
        <Typography variant="titleSm">Tổng số chương:</Typography>
        <Typography variant="bodyMd">{totalSections}</Typography>
      </div>

      <div className="h-4 w-[2px] bg-neutral-100" />

      <div className="flex items-center gap-2">
        <Typography variant="titleSm">Tổng thời lượng video:</Typography>
        <Typography variant="bodyMd">{secondsToHHMMSSTextFormat(totalDuration)}</Typography>
      </div>
    </div>
  );
}
