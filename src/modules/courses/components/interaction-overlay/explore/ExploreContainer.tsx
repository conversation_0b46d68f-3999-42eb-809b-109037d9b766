'use client';

import { Typography } from '@/components/ui';
import { LectureInteract } from '@/modules/courses/types/interaction.type';
import { downloadFile } from '@/utils';
import { PaperClipIcon } from '@heroicons/react/24/outline';
import DOMPurify from 'dompurify';

type ExploreContainerProps = {
  currentInteraction: LectureInteract;
  onShowCloseButton: () => void;
};

function ExploreContainer(props: ExploreContainerProps) {
  const { currentInteraction } = props;

  const description = currentInteraction?.explore?.description ?? '';
  const sanitizedDescription = DOMPurify.sanitize(description);
  const attachments = currentInteraction?.explore?.attachFiles || [];

  const handleDownload = async (file: NonNullable<LectureInteract['explore']>['attachFiles'][number]) => {
    await downloadFile(file.fileUrl!, file.fileName);
  };

  return (
    <div className="p-4">
      <div className="prose max-w-none" dangerouslySetInnerHTML={{ __html: sanitizedDescription }} />

      <div className="mt-4 flex flex-col gap-4">
        <Typography variant="labelLg">Học liệu đính kèm</Typography>
        <ul className="flex h-[168px] flex-col flex-wrap gap-x-6 gap-y-2">
          {attachments.map((file) => (
            <li
              key={file.id}
              onClick={() => handleDownload(file)}
              className="flex cursor-pointer items-center gap-2 rounded border border-neutral-100 px-2 py-1"
            >
              <PaperClipIcon className="size-4 text-neutral-500" />
              <Typography variant="labelMd" className="text-blue-500">
                {file.fileName}
              </Typography>
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
}

export default ExploreContainer;
