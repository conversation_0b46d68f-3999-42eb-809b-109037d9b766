import { ExploreResponse } from '@/features/courses';
import { InteractionType } from '@/modules/courses/constants/course.const';
import { Question } from '../../../z-store/lesson.store';

// export interface QuestionOption {
//   optionIndex: number;
//   optionName: string;
//   optionThumbnailImage: string;
// }

// export interface Question {
//   id?: string; // This has id for update and no id for create
//   questionTypeId?: number;
//   questionName: string;
//   questionImage: string;
//   questionOptions: QuestionOption[] | null;
//   correctAnswer: number[];
//   questionRequired: boolean;
//   questionDuration: number;
//   replyRightAnswer: string;
//   replyWrongAnswer: string;
//   backgroundColor: string;
//   createdBy?: number | null;
//   updatedBy?: number | null;
//   createdAt?: string | null;
//   updatedAt?: string | null;
//   videoUrl?: string | null;
// }

export type LectureInteract = {
  id: string;
  interactType: InteractionType;
  interactName: string;
  startAt: number;
  duration: number;
  lectureId: string;
  isShowed: boolean;
  interactTypeId: number;
  question: Question | null;
  explore: ExploreResponse | null;
};
