import { LibraryFile } from '@/modules/courses/types/file.type';
import { UserInfo } from '@/type';

export type QuizFile = Pick<LibraryFile, 'fileUrl' | 'fileName' | 'id' | 'fileSize'>;

export type QuestionOption = {
  optionIndex: number;
  optionName: string;
  optionThumbnailImageFile?: QuizFile;
};

export type Question = {
  id: string;
  questionName: string;
  questionImage: string;
  questionImageFile?: QuizFile;
  questionTypeId: number;
  videoFile?: QuizFile;
  questionOptions: QuestionOption[];
  correctAnswer: number[];
  sortIndex: number;
};

export type Test = {
  id: string;
  testName: string;
  minCorrectAnswer: number;
  hasLimitTime: number;
  limitTime: number;
  questions: Question[];
  createdAt: string;
  updatedAt: string;
};

export type UserTestResult = {
  id: string;
  createdAt: string;
  updatedAt: string;
  times: number;
  answer: string[] | number[];
  point: number;
  createdBy: UserInfo;
  testId: string;
  questionId: string;
};
