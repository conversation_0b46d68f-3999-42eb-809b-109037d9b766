import { ChapterType, InteractionType, LessonType } from '../constants/course.const';

type PublishCourseRequest = {
  courseId: string;
};

type SectionCreateRequest = {
  courseId: string;
  sectionName: string;
  sortIndex: number;
  sectionType: ChapterType;
};

type SectionEditRequest = {
  sectionId: string;
  courseId: string;
  sectionName: string;
};

type LessonCreateRequest = {
  courseId: string;
  sectionId: string;

  lectureName: string;
  lectureType: LessonType;
  sortIndex: number;

  thumbnailFileId?: string;
};

type LessonEditRequest = {
  courseId: string;
  sectionId: string;
  lectureId: string;

  lectureName?: string;
  thumbnailFileId?: string | null;
};

type LessonDeleteRequest = {
  courseId: string;
  sectionId: string;
  lectureId: string;
};

type CreateInteractionRequest = {
  courseId: string;
  sectionId: string;
  lectureId: string;

  interactionType: InteractionType;
  startAt: number;
  duration: number;
};

type UpdateInteractionRequest = {
  courseId: string;
  sectionId: string;
  lectureId: string;
  interactId: string;

  startAt: number;
  duration: number;
};

type QuestionInteractRequest = {
  interact_type: InteractionType;
  questionTypeId?: number;
  questionName?: string;
  questionImageFile?: string | null;
  videoFile?: string | null;
  questionOptions?: string;
  questionAnswers?: string;
  correctAnswer?: string;
  questionRequired?: boolean;
  questionDuration?: number;
  replyRightAnswer?: string;
  replyWrongAnswer?: string;
  showResponse?: boolean;
  backgroundColor?: string;
};

type ExploreInteractionRequest = {
  interact_type: InteractionType;
  description: string;
  attached_files: string[];
};

type UpdateInteractionContentRequest = {
  courseId: string;
  sectionId: string;
  lectureId: string;
  interactId: string;
  content: QuestionInteractRequest;
};

type DeleteInteractionRequest = {
  courseId: string;
  sectionId: string;
  lectureId: string;
  interactId: string;
};

type GetInteractionDetailRequest = {
  courseId: string;
  sectionId: string;
  lectureId: string;
  interactId: string;
};

type ReviewCourseRequest = {
  courseId: string;
  rating: number;
  comment?: string;
  feelings?: string[];
};

export type {
  CreateInteractionRequest,
  DeleteInteractionRequest,
  ExploreInteractionRequest,
  GetInteractionDetailRequest,
  LessonCreateRequest,
  LessonDeleteRequest,
  LessonEditRequest,
  PublishCourseRequest,
  QuestionInteractRequest,
  ReviewCourseRequest,
  SectionCreateRequest,
  SectionEditRequest,
  UpdateInteractionContentRequest,
  UpdateInteractionRequest,
};
