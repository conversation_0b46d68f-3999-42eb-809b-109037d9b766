'use client';

import { updateFavoriteCourseService } from '@/modules/courses/services/course.service';
import { notifyError, notifySuccess } from '@/utils';
import { useRouter } from 'next/navigation';
import { useMutation } from 'react-query';

const showSuccessNotification = (isFavorite: boolean) => {
  if (isFavorite) {
    notifySuccess('Thêm khóa học yêu thích thành công!');
  } else {
    notifySuccess('Xóa khóa học yêu thích thành công!');
  }
};

const showErrorNotification = (message: string) => {
  notifyError(message);
};

const useFavoriteCourse = () => {
  const router = useRouter();

  const { mutate: updateFavoriteCourseMutate, isLoading: isUpdatingFavorite } = useMutation(
    (payload: { courseId: string; isFavorite: boolean }) => updateFavoriteCourseService(payload),
  );

  const handleFavorite = ({ courseId, isFavorite }: { courseId: string; isFavorite: boolean }) => {
    updateFavoriteCourseMutate(
      { courseId, isFavorite },
      {
        onSuccess: () => {
          showSuccessNotification(isFavorite);
          router.refresh();
        },
        onError: (err: unknown) => {
          const error = err as { data: { message: string } };
          const message = error?.data?.message || '';
          showErrorNotification(message);
        },
      },
    );
  };

  return {
    onFavorite: handleFavorite,
    isUpdatingFavorite,
  };
};

export default useFavoriteCourse;
