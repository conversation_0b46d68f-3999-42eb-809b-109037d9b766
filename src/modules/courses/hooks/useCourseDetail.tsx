import { QUERY_KEYS } from '@/constants/query-keys';
import { getCourseByIdService } from '@/modules/courses/services/course.service';
import { CourseInfo } from '@/modules/courses/types/course.type';
import { useQuery } from 'react-query';

const useCourseDetail = ({ courseId, initialData }: { courseId: string; initialData?: CourseInfo }) => {
  const { data } = useQuery({
    queryKey: [QUERY_KEYS.COURSE_DETAIL, courseId],
    enabled: !!courseId,
    initialData: initialData,
    queryFn: () => getCourseByIdService(courseId),
  });

  return {
    courseDetailData: data || null,
  };
};

export default useCourseDetail;
