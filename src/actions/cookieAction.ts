'use server';

import { UserInfo } from '@/type';
import { cookieConstant } from 'config';
import { ResponseCookie } from 'next/dist/compiled/@edge-runtime/cookies';
import { cookies } from 'next/headers';
import { COOKIE_NAMES } from '../constants/auth';

export async function removeUserInfoCookie() {
  const cookieStore = await cookies();

  cookieStore.set({
    name: cookieConstant.userInfoKey,
    value: '',
    maxAge: 0,
  });
}

export async function setCookie(key: string, value: string, options?: Partial<ResponseCookie>) {
  (await cookies()).set(key, value, {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict',
    maxAge: 60 * 60 * 24,
    path: '/',
    domain: 'studify.vn',
    ...options,
  });
}

export async function getCookieValue(key: string) {
  return (await cookies()).get(key)?.value;
}

export async function getCookieValues() {
  return (await cookies()).getAll();
}

export const getUserFromCookie = async () => {
  const userInfoCookie = (await getCookieValue(COOKIE_NAMES.USER_INFO)) || '';

  // This code fixes an app crash issue caused by the server occasionally returning user_info with a 'j:' prefix.
  if (userInfoCookie.startsWith('j:')) {
    const slicedUserInfo = userInfoCookie.slice(2);
    const cookieParsed = userInfoCookie ? JSON.parse(slicedUserInfo) : {};
    const userInfoParsed = userInfoCookie ? cookieParsed : {};
    return { userInfoParsed, userInfoCookie };
  }

  const cookieParsed = userInfoCookie ? JSON.parse(userInfoCookie) : {};
  const userInfoParsed: UserInfo = userInfoCookie ? cookieParsed : {};
  return { userInfoParsed, userInfoCookie };
};

export const getAccessToken = async () => {
  const accessTokenCookie = await getCookieValue(COOKIE_NAMES.ACCESS_TOKEN);
  return accessTokenCookie || '';
};

export const getCookiesAsString = async () => {
  const accessToken = await getAccessToken();
  const refreshToken = await getCookieValue(COOKIE_NAMES.REFRESH_TOKEN);
  const cookie = `accessToken=${accessToken}; refreshToken=${refreshToken}`;

  return cookie;
};

export const clearCookies = async (cookieOption = { maxAge: 0, domain: '.studify.vn', httpOnly: true }) => {
  const cookieStore = await cookies();
  cookieStore.set({ name: COOKIE_NAMES.USER_INFO, value: '', ...cookieOption });
  cookieStore.set({ name: COOKIE_NAMES.ACCESS_TOKEN, value: '', ...cookieOption });
  cookieStore.set({ name: COOKIE_NAMES.REFRESH_TOKEN, value: '', ...cookieOption });
};
