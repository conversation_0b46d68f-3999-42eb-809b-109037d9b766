p.is-editor-empty:first-child::before {
  color: var(--gray-4);
  content: attr(data-placeholder);
  float: left;
  height: 0;
  pointer-events: none;
}
.richTextEditor .ProseMirror {
  outline: none;
}

.richTextEditor .ProseMirror ul {
  list-style-type: disc;
  margin-left: 1rem;
  padding-left: 0.5rem;
}

.richTextEditor .ProseMirror ol {
  list-style-type: decimal;
  margin-left: 1rem;
  padding-left: 0.5rem;
}

.richTextEditor .ProseMirror li {
  margin: 0.25rem 0;
}

.richTextEditor .ProseMirror p {
  margin: 0;
}

.richTextEditor .ProseMirror strong {
  font-weight: bold;
}

.richTextEditor .ProseMirror em {
  font-style: italic;
}

.richTextEditor .ProseMirror u {
  text-decoration: underline;
}

.richTextEditor .ProseMirror s {
  text-decoration: line-through;
}

.richTextEditor .ProseMirror a {
  color: #2563eb;
  text-decoration: underline;
}
