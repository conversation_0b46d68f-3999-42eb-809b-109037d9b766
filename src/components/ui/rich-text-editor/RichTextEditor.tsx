'use client';

import { Icon } from '@/components/client/icon';
import { cn } from '@/lib/utils';
import {
  BoldIcon,
  ItalicIcon,
  LinkIcon,
  ListBulletIcon,
  QueueListIcon,
  StrikethroughIcon,
  UnderlineIcon,
} from '@heroicons/react/24/outline';
import { CharacterCount } from '@tiptap/extension-character-count';
import { Color } from '@tiptap/extension-color';
import Link from '@tiptap/extension-link';
import { TextStyle } from '@tiptap/extension-text-style';
import Underline from '@tiptap/extension-underline';
import { Placeholder } from '@tiptap/extensions';
import { EditorContent, useEditor, useEditorState } from '@tiptap/react';
import { BubbleMenu } from '@tiptap/react/menus';
import StarterKit from '@tiptap/starter-kit';
import { ColorPicker } from 'antd';
import { useCallback, useEffect, useRef, useState } from 'react';
import { Button } from '../button';
import styles from './RichTextEditor.module.css';
import './styles.scss';

interface RichTextEditorProps {
  content?: string;
  placeholder?: string;
  onChange?: (content: string) => void;
  onBlur?: () => void;
  className?: string;
  classNames?: {
    root?: string;
    editor?: string;
  };
  maxLength?: number;
}

export default function RichTextEditor({
  content = '',
  placeholder = 'Type something...',
  onChange,
  onBlur,
  className = '',
  classNames = {},
  maxLength,
}: RichTextEditorProps) {
  const [showBubbleMenu, setShowBubbleMenu] = useState(false);
  const [menuPosition, setMenuPosition] = useState({ top: 0, left: 0 });
  const [colorPickerOpen, setColorPickerOpen] = useState(false);
  const editorRef = useRef<HTMLDivElement>(null);

  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        bulletList: {
          keepMarks: true,
          keepAttributes: false,
        },
        orderedList: {
          keepMarks: true,
          keepAttributes: false,
        },
      }),
      Placeholder.configure({
        // Use a placeholder:
        placeholder: placeholder,
        // Use different placeholders depending on the node type:
        // placeholder: ({ node }) => {
        //   if (node.type.name === 'heading') {
        //     return 'What's the title?'
        //   }

        //   return 'Can you add some further context?'
        // },
      }),
      Underline,
      Link.configure({
        openOnClick: false,
        HTMLAttributes: {
          class: 'text-blue-600 underline',
        },
      }),
      TextStyle,
      Color,
      CharacterCount.configure({
        limit: maxLength || undefined,
      }),
    ],
    content,
    immediatelyRender: false,
    onUpdate: ({ editor }) => {
      const html = editor.getHTML();
      onChange?.(html);
    },
    onBlur: () => {
      onBlur?.();
    },
    editorProps: {
      attributes: {
        class: 'focus:outline-none min-h-[40px] px-3 py-2 text-sm prose prose-sm max-w-none',
      },
      handleKeyDown: (view, event) => {
        // Prevent typing when at character limit
        if (maxLength) {
          const currentCharCount = view.state.doc.textContent.length;
          // Allow backspace, delete, and navigation keys
          if (
            ['Backspace', 'Delete', 'ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown', 'Home', 'End'].includes(
              event.key,
            )
          ) {
            return false;
          }
          // Prevent adding content if it would exceed the limit
          if (currentCharCount >= maxLength) {
            event.preventDefault();
            return true;
          }
        }
        return false;
      },
      handlePaste: (view, event) => {
        // Prevent pasting when it would exceed character limit
        if (maxLength) {
          const currentCharCount = view.state.doc.textContent.length;
          const clipboardData = event.clipboardData;
          if (clipboardData) {
            const pastedText = clipboardData.getData('text/plain');
            if (currentCharCount + pastedText.length > maxLength) {
              event.preventDefault();
              return true;
            }
          }
        }
        return false;
      },
    },
    onSelectionUpdate: ({ editor }) => {
      const { from, to } = editor.state.selection;
      if (from !== to) {
        // Text is selected, show bubble menu
        setTimeout(() => {
          const selection = window.getSelection();
          if (selection && selection.rangeCount > 0 && !selection.isCollapsed && editorRef.current) {
            const range = selection.getRangeAt(0);
            const rect = range.getBoundingClientRect();
            const editorRect = editorRef.current.getBoundingClientRect();

            if (rect.width > 0 && rect.height > 0) {
              setMenuPosition({
                top: rect.top - editorRect.top - 50,
                left: rect.left - editorRect.left + rect.width / 2,
              });
              setShowBubbleMenu(true);
            }
          }
        }, 100);
      } else {
        setShowBubbleMenu(false);
      }
    },
  });

  useEffect(() => {
    if (editor && content !== editor.getHTML()) {
      editor.commands.setContent(content);
    }
  }, [content, editor]);

  const handleClickOutside = useCallback(
    (event: MouseEvent) => {
      // Don't close if ColorPicker is open
      if (colorPickerOpen) {
        return;
      }

      // Don't close if clicking on ColorPicker or its dropdown
      const target = event.target as HTMLElement;
      if (target.closest('.ant-color-picker, .ant-popover, .ant-tooltip')) {
        return;
      }

      setShowBubbleMenu(false);
    },
    [colorPickerOpen],
  );

  useEffect(() => {
    if (showBubbleMenu) {
      document.addEventListener('click', handleClickOutside);
      return () => document.removeEventListener('click', handleClickOutside);
    }
  }, [showBubbleMenu, handleClickOutside]);

  // Alternative bubble menu trigger using mouse events
  useEffect(() => {
    if (!editor) return;

    const handleMouseUp = (event: MouseEvent) => {
      // Don't trigger if clicking on ColorPicker
      const target = event.target as HTMLElement;
      if (target.closest('.ant-color-picker')) {
        return;
      }

      setTimeout(() => {
        const { from, to } = editor.state.selection;
        if (from !== to) {
          const selection = window.getSelection();
          if (selection && selection.rangeCount > 0 && !selection.isCollapsed && editorRef.current) {
            const range = selection.getRangeAt(0);
            const rect = range.getBoundingClientRect();
            const editorRect = editorRef.current.getBoundingClientRect();

            if (rect.width > 0 && rect.height > 0) {
              setMenuPosition({
                top: rect.top - editorRect.top - 50,
                left: rect.left - editorRect.left + rect.width / 2,
              });
              setShowBubbleMenu(true);
            }
          }
        }
      }, 10);
    };

    document.addEventListener('mouseup', handleMouseUp);
    return () => document.removeEventListener('mouseup', handleMouseUp);
  }, [editor]);

  const { charactersCount } = useEditorState<any>({
    editor,
    selector: (context: any) => ({
      charactersCount: context?.editor?.storage?.characterCount?.characters(),
      wordsCount: context?.editor?.storage?.characterCount?.words(),
    }),
  });

  if (!editor) {
    return null;
  }

  return (
    <div
      ref={editorRef}
      className={cn(
        `${styles.richTextEditor} relative bg-white focus-within:border-blue-500 focus-within:ring-2 focus-within:ring-blue-100 hover:border-blue-500`,
        className,
        classNames.root,
      )}
    >
      {true && (
        <BubbleMenu editor={editor} options={{ placement: 'bottom', offset: 8 }}>
          <div className="bubble-menu">
            <Button
              variant="ghost"
              size="small"
              onClick={() => editor.chain().focus().toggleBold().run()}
              className={`p-2 ${editor.isActive('bold') ? 'bg-neutral-100' : ''} text-black`}
            >
              <Icon icon={<BoldIcon className="size-4" />} />
            </Button>

            <Button
              variant="ghost"
              size="small"
              onClick={() => editor.chain().focus().toggleItalic().run()}
              className={`p-2 text-black ${editor.isActive('italic') ? 'bg-neutral-100' : ''}`}
            >
              <Icon icon={<ItalicIcon className="size-4" />} />
            </Button>

            <Button
              variant="ghost"
              size="small"
              onClick={() => editor.chain().focus().toggleUnderline().run()}
              className={`p-2 text-black ${editor.isActive('underline') ? 'bg-neutral-100' : ''}`}
            >
              <Icon icon={<UnderlineIcon className="size-4" />} />
            </Button>

            <Button
              variant="ghost"
              size="small"
              onClick={() => editor.chain().focus().toggleStrike().run()}
              className={`p-2 text-black ${editor.isActive('strike') ? 'bg-neutral-100' : ''}`}
            >
              <Icon icon={<StrikethroughIcon className="size-4" />} />
            </Button>

            <div className="h-6 w-px bg-neutral-200" />

            <Button
              variant="ghost"
              size="small"
              onClick={() => editor.chain().focus().toggleBulletList().run()}
              className={`p-2 text-black ${editor.isActive('bulletList') ? 'bg-neutral-100' : ''}`}
            >
              <Icon icon={<ListBulletIcon className="size-4" />} />
            </Button>

            <Button
              variant="ghost"
              size="small"
              onClick={() => editor.chain().focus().toggleOrderedList().run()}
              className={`p-2 text-black ${editor.isActive('orderedList') ? 'bg-neutral-100' : ''}`}
            >
              <Icon icon={<QueueListIcon className="size-4" />} />
            </Button>

            <Button
              variant="ghost"
              size="small"
              onClick={() => {
                const url = window.prompt('Enter URL:');
                if (url) {
                  editor.chain().focus().setLink({ href: url }).run();
                }
              }}
              className={`p-2 text-black ${editor.isActive('link') ? 'bg-neutral-100' : ''}`}
            >
              <Icon icon={<LinkIcon className="size-4" />} />
            </Button>

            <div className="h-6 w-px bg-neutral-200" />

            <div>
              <ColorPicker
                size="small"
                trigger="click"
                // showText={() => <div className="size-4 rounded-full border border-neutral-300 bg-neutral-800"></div>}
                onChangeComplete={(color) => {
                  const hexColor = color.toHexString();
                  editor.chain().focus().setColor(hexColor).run();
                }}
                onOpenChange={(open) => {
                  setColorPickerOpen(open);
                }}
              />
            </div>
          </div>
        </BubbleMenu>
      )}

      <EditorContent
        editor={editor}
        className={cn(
          'min-h-[40px] rounded-none border-x-0 border-b border-l-0 border-t-0 border-b-neutral-200 font-nunito text-[#0C0C0C]',
          classNames.editor,
        )}
        placeholder={placeholder}
      />

      {maxLength && (
        <div
          className={`mt-1 px-3 text-xs ${
            charactersCount >= maxLength
              ? 'text-red-500'
              : charactersCount >= maxLength * 0.9
                ? 'text-orange-500'
                : 'text-neutral-400'
          }`}
        >
          {charactersCount}/{maxLength} ký tự
          {charactersCount >= maxLength && <span className="ml-2"> - Đã đạt giới hạn ký tự</span>}
        </div>
      )}
    </div>
  );
}
