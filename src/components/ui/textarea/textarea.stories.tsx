import type { Meta, StoryObj } from '@storybook/react';
import TextArea from './textarea';

const meta = {
  title: 'UI/TextArea',
  component: TextArea,
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    size: {
      control: 'select',
      options: ['sm', 'md', 'lg'],
      description: 'Size of the textarea',
    },
    disabled: {
      control: 'boolean',
      description: 'Whether the textarea is disabled',
    },
    status: {
      control: 'select',
      options: ['none', 'error'],
      description: 'Status of the textarea',
    },
    helperText: {
      control: 'text',
      description: 'Helper text to display below textarea',
    },
  },
  tags: ['autodocs'],
} satisfies Meta<typeof TextArea>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    placeholder: 'Enter text here...',
  },
};

export const Disabled: Story = {
  args: {
    disabled: true,
    placeholder: 'Disabled textarea',
  },
};

export const WithError: Story = {
  args: {
    status: 'error',
    helperText: 'This field is required',
    placeholder: 'Textarea with error',
  },
};
