.text-area-custom {
  &.textarea-size-lg {
    textarea.ant-input {
      padding: 8px 16px;
    }
  }

  &.textarea-size-mmd {
    textarea.ant-input {
      padding: 5px 21px;
    }
  }

  &.textarea-size-sm {
    textarea.ant-input {
      padding: 1px 8px;
    }
  }

  &.ant-input-outlined:focus {
    @apply border-[1px] border-blue-500 ring-1 ring-blue-500;
  }

  &.ant-input-outlined:hover {
    @apply border-[1px] border-blue-500;
  }

  &.ant-input-outlined.ant-input-status-error:not(.ant-input-disabled):hover {
    @apply border-[1px] border-red-600;
  }

  &.ant-input-outlined.ant-input-status-error:not(.ant-input-disabled) {
    @apply border-[1px] border-red-600;
  }
}
