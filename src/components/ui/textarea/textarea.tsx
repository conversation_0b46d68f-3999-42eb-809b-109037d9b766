import { cn } from '@/lib/utils';
import { Input } from 'antd';
import { TextAreaRef } from 'antd/es/input/TextArea';
import clsx from 'clsx';
import React from 'react';
import './textarea.scss';

type TextAreaProps = {
  helperText?: React.ReactNode;
} & React.ComponentProps<typeof Input.TextArea>;

const TextArea = React.forwardRef<TextAreaRef, TextAreaProps>((props, ref) => {
  const { className, helperText, disabled = false, ...rest } = props;

  return (
    <React.Fragment>
      <Input.TextArea
        ref={ref}
        className={clsx('text-area-custom w-full', cn(className, !disabled && 'bg-white'))}
        showCount
        count={rest.count}
        disabled={disabled}
        {...rest}
      />

      {rest.status === 'error' && <div className="text-body-sm text-red-500">{helperText}</div>}
    </React.Fragment>
  );
});

TextArea.displayName = 'TextArea';

export default TextArea;
