import { cn } from '@/lib/utils';
import { Collapse as AntCollapse, CollapseProps as AntCollapseProps, ConfigProvider } from 'antd';
import './collapse.scss';

export type CollapseProps = {} & AntCollapseProps;

const Collapse = (props: CollapseProps) => {
  const classes = cn(props.className, 'collapse-custom');

  return (
    <ConfigProvider theme={{ components: { Collapse: { contentPadding: 0 } } }}>
      <AntCollapse {...props} className={classes} />
    </ConfigProvider>
  );
};

export default Collapse;
