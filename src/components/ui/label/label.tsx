'use client';

import { cn } from '@/lib/utils';
import { CheckCircleIcon, ExclamationCircleIcon } from '@heroicons/react/16/solid';
import clsx from 'clsx';
import React from 'react';

type HelperTextStatus = 'default' | 'validated' | 'invalid';

interface LabelProps extends React.LabelHTMLAttributes<HTMLLabelElement> {
  size?: 'sm' | 'md' | 'lg';
  status?: 'default' | 'validated' | 'invalid';
  helperText?: string;
  error?: boolean;
  required?: boolean;
  optional?: boolean;
}

const sizeClasses = {
  sm: clsx('text-label-sm'),
  md: clsx('text-label-md'),
  lg: clsx('text-label-lg'),
};

const statusClasses = {
  default: 'text-primary_text',
  validated: 'text-green-600',
  invalid: 'text-red-600',
};

const HelperText = ({ text, status = 'default' }: { text: string | undefined; status?: HelperTextStatus }) => {
  if (!text) return null;

  if (status === 'validated') {
    return (
      <div className="flex items-center gap-1">
        <CheckCircleIcon className="size-[10px] text-green-700" />
        <div className={clsx('font-nunito text-body-sm text-green-600')}>{text}</div>
      </div>
    );
  }

  if (status === 'invalid') {
    return (
      <div className="flex items-center gap-1">
        <ExclamationCircleIcon className="size-[10px] text-red-600" />
        <div className={clsx('font-nunito text-body-sm text-red-600')}>{text}</div>
      </div>
    );
  }

  return <p className={clsx('font-nunito text-body-md text-secondary_text', statusClasses[status])}>{text}</p>;
};

const Label = React.forwardRef<HTMLLabelElement, LabelProps>(
  ({ className, children, size = 'md', helperText, required, optional, status = 'default', ...props }, ref) => {
    return (
      <div className="flex flex-col">
        <label
          ref={ref}
          className={clsx(
            'flex items-center gap-0.5 font-nunito font-medium text-primary_text',
            sizeClasses[size],
            cn(className),
          )}
          {...props}
        >
          {children}
          {required && !optional && <span className="text-label-sm text-red-600">*</span>}
          {optional && !required && <span className="text-label-sm text-placeholder_text">(Optional)</span>}
        </label>
        <HelperText text={helperText} status={status} />
      </div>
    );
  },
);

Label.displayName = 'Label';

export default Label;
