import type { Meta, StoryObj } from '@storybook/react';
import Label from './label';

const meta = {
  title: 'UI/Label',
  component: Label,
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    size: {
      control: 'select',
      options: ['sm', 'md', 'lg'],
      description: 'Size of the label',
    },
    status: {
      control: 'select',
      options: ['default', 'validated', 'invalid'],
      description: 'Status of the label',
    },
    required: {
      control: 'boolean',
      description: 'Whether the label is required',
    },
    optional: {
      control: 'boolean',
      description: 'Whether the label is optional',
    },
  },
  tags: ['autodocs'],
} satisfies Meta<typeof Label>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    children: 'Label',
  },
};

export const Validated: Story = {
  args: {
    children: 'Validated Label',
    status: 'validated',
  },
};

export const Invalid: Story = {
  args: {
    children: 'Invalid Label',
    status: 'invalid',
  },
};

export const Required: Story = {
  args: {
    children: 'Required Label',
    required: true,
  },
};

export const Optional: Story = {
  args: {
    children: 'Optional Label',
    optional: true,
  },
};

export const HelperText: Story = {
  args: {
    children: 'Label',
    helperText: 'Helper Text',
  },
};

export const HelperTextValidated: Story = {
  args: {
    children: 'Label',
    helperText: 'Helper Text',
    status: 'validated',
  },
};

export const HelperTextInvalid: Story = {
  args: {
    children: 'Label',
    helperText: 'Helper Text',
    status: 'invalid',
  },
};
