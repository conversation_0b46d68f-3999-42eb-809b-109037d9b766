import { cn } from '@/lib/utils';
import { Switch as AntSwitch, SwitchProps as AntSwitchProps } from 'antd';
import React from 'react';

type SwitchProps = AntSwitchProps;

const Switch = React.forwardRef<HTMLButtonElement, SwitchProps>((props, ref) => {
  const { className, ...rest } = props;

  const switchClasses = cn(className);

  return <AntSwitch ref={ref} className={switchClasses} {...rest} />;
});

Switch.displayName = 'Switch';

export default Switch;
