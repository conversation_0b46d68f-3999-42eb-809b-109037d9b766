import { cn } from '@/lib/utils';
import { ChevronDownIcon } from '@heroicons/react/24/outline';
import { Select as AntSelect, type SelectProps as AntSelectProps } from 'antd';
import clsx from 'clsx';
import { ReactNode } from 'react';
import './select.scss';

export type SelectProps = {
  children?: ReactNode;
  size?: 'lg' | 'md' | 'sm' | 'xs';
  className?: string;
  disabled?: boolean;
} & Omit<AntSelectProps, 'size'>;

const sizeClasses = {
  lg: clsx('h-12'),
  md: clsx('h-10'),
  sm: clsx('h-9'),
  xs: clsx('h-8'),
};

const popupSizeClasses = {
  lg: clsx('popup-size-lg'),
  md: clsx('popup-size-md'),
  sm: clsx('popup-size-sm'),
  xs: clsx('popup-size-xs'),
};

const Select = ({ children, size = 'md', className = '', disabled = false, ...props }: SelectProps) => {
  const classes = clsx('select-custom', sizeClasses[size], cn(className));

  const popupClasses = clsx('select-popup-custom', popupSizeClasses[size], cn(props.popupClassName));

  return (
    <AntSelect
      popupClassName={popupClasses}
      suffixIcon={<ChevronDownIcon height={16} width={16} className="text-secondary_text" />}
      className={classes}
      disabled={disabled}
      {...props}
    >
      {children}
    </AntSelect>
  );
};
const Option = AntSelect.Option;
export { Option };
export default Select;
