.select-popup-custom {
  &.ant-select-dropdown {
    .ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
      @apply bg-neutral-100 text-body-lg;
    }

    .ant-select-item-option:not(.ant-select-item-option-disabled) {
      @apply text-body-lg;
    }
  }

  &.popup-size-lg {
    .ant-select-item {
      @apply min-h-12 px-4 py-3 !important;
    }
  }

  &.popup-size-md {
    .ant-select-item {
      @apply min-h-10 px-4 py-2 !important;
    }
  }

  &.popup-size-sm {
    .ant-select-item {
      @apply min-h-9 px-4 py-2 !important;
    }
  }

  &.popup-size-xs {
    .ant-select-item {
      @apply min-h-8 px-4 py-[6px] !important;
    }
  }
}

.select-custom {
  &.ant-select-outlined:not(.ant-select-disabled):not(.ant-select-customize-input):not(
      .ant-pagination-size-changer
    ):hover
    .ant-select-selector {
    @apply border-[1px] border-blue-500;
  }

  &.ant-select-focused.ant-select-outlined:not(.ant-select-disabled):not(.ant-select-customize-input):not(
      .ant-pagination-size-changer
    )
    .ant-select-selector {
    @apply border-[1px] border-blue-500 ring-offset-1 ring-offset-blue-500;
  }

  &.ant-select-outlined.ant-select-disabled:not(.ant-select-customize-input) .ant-select-selector {
    @apply border-[1px] border-neutral-200 bg-white !important;
  }

  &.ant-select-dropdown .ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
    @apply bg-white;
  }

  .ant-select-selector {
    @apply border-[1px] border-neutral-200 bg-white !important;
  }

  // Placeholder color
  .ant-select-selection-placeholder {
    color: #666666 !important;
  }
}
