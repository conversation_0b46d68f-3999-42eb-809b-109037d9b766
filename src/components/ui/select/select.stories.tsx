import type { Meta, StoryObj } from '@storybook/react';
import Select from './select';

const meta = {
  title: 'UI/Select',
  component: Select,
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    size: {
      control: 'select',
      options: ['small', 'middle', 'large'],
      description: 'Size of select input',
    },
    disabled: {
      control: 'boolean',
      description: 'Whether the select is disabled',
    },
    status: {
      control: 'select',
      options: ['none', 'error'],
      description: 'Status of the select',
    },
    placeholder: {
      control: 'text',
      description: 'Placeholder text',
    },
    loading: {
      control: 'boolean',
      description: 'Loading state of select',
    },
    mode: {
      control: 'select',
      options: ['single', 'multiple', 'tags'],
      description: 'Mode of select',
    },
  },
  tags: ['autodocs'],
} satisfies Meta<typeof Select>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    placeholder: 'Select an option',
    options: [
      { value: 'option1', label: 'Option 1' },
      { value: 'option2', label: 'Option 2' },
      { value: 'option3', label: 'Option 3' },
    ],
    size: 'md',
    className: 'w-[167px]',
    showArrow: false,
  },
};

export const Multiple: Story = {
  args: {
    mode: 'multiple',
    placeholder: 'Select multiple options',
    options: [
      { value: 'option1', label: 'Option 1' },
      { value: 'option2', label: 'Option 2' },
      { value: 'option3', label: 'Option 3' },
    ],
    defaultValue: ['option1', 'option2'],
    size: 'md',
    className: 'min-w-40',
    showArrow: false,
  },
};

export const Disabled: Story = {
  args: {
    disabled: true,
    placeholder: 'Disabled select',
    options: [
      { value: 'option1', label: 'Option 1' },
      { value: 'option2', label: 'Option 2' },
    ],
  },
};
