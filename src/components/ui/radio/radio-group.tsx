import { cn } from '@/lib/utils';
import { Radio as AntRadio, RadioGroupProps as AntRadioGroupProps } from 'antd';
import React from 'react';
import './radio.scss';

export type RadioGroupProps = AntRadioGroupProps;

const RadioGroup = React.forwardRef<HTMLDivElement, RadioGroupProps>((props, ref) => {
  const { className, children, ...rest } = props;

  const radioClasses = cn('ant-radio-custom', className);

  return (
    <AntRadio.Group className={radioClasses} {...rest} ref={ref}>
      {children}
    </AntRadio.Group>
  );
});

RadioGroup.displayName = 'RadioGroup';

export default RadioGroup;
