import { Button } from '@/components/ui/button';
import Typography from '@/components/ui/typography';
import { usePreventHydration } from '@/hooks';
import { cn } from '@/lib/utils';
import { XMarkIcon } from '@heroicons/react/24/outline';
import './modal.scss';

type ModalTitleProps = {
  title?: string;
  iconClassName?: string;
  onClose?: (e: React.MouseEvent<HTMLButtonElement, MouseEvent>) => void;
};

const ModalTitle = (props: ModalTitleProps) => {
  const { title, iconClassName, onClose } = props;

  usePreventHydration();

  return (
    <div className="flex items-center justify-between">
      <Typography variant="headlineXs">{title}</Typography>

      {onClose && (
        <Button
          variant="ghost"
          size="small"
          className={cn(
            'text-primary_text focus-within:bg-neutral-50 hover:bg-neutral-50 active:bg-neutral-50 active:text-primary_text',
          )}
          onClick={onClose}
        >
          <XMarkIcon className={cn('size-6', iconClassName)} />
        </Button>
      )}
    </div>
  );
};

export default ModalTitle;
