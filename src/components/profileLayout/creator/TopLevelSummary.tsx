'use client';
import { ExclamationCircleIcon } from '@heroicons/react/24/solid';
import { Tooltip } from 'antd';
import { useEffect, useState } from 'react';
import { useCourse } from '../../../hooks/apis';
import { CourseMetricsSummary } from '../../../type/course/course';
import { Typography } from '../../ui';

const TopLevelSummary = () => {
  const [metrics, setMetrics] = useState<CourseMetricsSummary | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { getMetricsSummary } = useCourse();

  useEffect(() => {
    const fetchMetrics = async () => {
      try {
        setLoading(true);
        const data = await getMetricsSummary();
        setMetrics(data);
        setError(null);
      } catch (err) {
        setError('Failed to load metrics');
        console.error('Error fetching metrics:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchMetrics();
  }, []);

  return (
    <div className="flex flex-col gap-3 pb-5">
      <div>
        <Typography variant="headlineSm">Tổng quan</Typography>
      </div>
      <div className="flex justify-between gap-4">
        <div className="flex h-[104px] flex-1 flex-col justify-center gap-2 rounded-sm border border-[#D4DAE5] bg-neutral-50 px-5 py-4">
          <Typography variant="displaySm" className="">
            {loading ? '...' : error ? '-' : (metrics?.totalSubscriptions ?? 0)}
          </Typography>
          <Typography>Tổng số lượt đăng ký</Typography>
        </div>
        <div className="flex h-[104px] flex-1 flex-col justify-center gap-2 rounded-sm border border-[#D4DAE5] bg-neutral-50 px-5 py-4">
          <Typography variant="displaySm" className="">
            {loading ? '...' : error ? '-' : (metrics?.totalCompletions ?? 0)}
          </Typography>
          <Typography>Tổng số lượt hoàn thành</Typography>
        </div>{' '}
        <div className="flex h-[104px] flex-1 flex-col justify-center gap-2 rounded-sm border border-[#D4DAE5] bg-neutral-50 px-5 py-4">
          <Typography variant="displaySm" className="">
            {loading ? '...' : error ? '-' : `${metrics?.averageStudyTime ?? 0}%`}
          </Typography>
          <div className="flex items-center gap-2">
            <Typography>Phần trăm thời lượng học trung bình </Typography>
            <Tooltip title="Cho biết trung bình học viên đã hoàn thành bao nhiêu phần trăm (%) bài học. Chỉ số càng cao cho thấy bài giảng của bạn càng hấp dẫn và giữ chân học viên tốt.">
              <ExclamationCircleIcon width={20} height={20} />
            </Tooltip>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TopLevelSummary;
