import dayjs from '@/lib/dayjs';
import { UserInfo } from '../../../../type';
import { UpdateUserProfileParams } from '../../../../type/user.type';
import { EditProfileFields, PROFILE_FIELDS } from './constant';

export const getTransformedValue = (
  field: EditProfileFields,
  formValue: UpdateUserProfileParams[keyof UpdateUserProfileParams],
  userInfo: UserInfo,
): string | File | null => {
  const valueTransformers: Partial<Record<EditProfileFields, (value: unknown, userInfo: UserInfo) => string | File>> = {
    [EditProfileFields.USER_ID]: () => userInfo.id,
    [EditProfileFields.DATE_OF_BIRTH]: (value: unknown) => dayjs(value as string | Date).format('YYYY-MM-DD'),
  };

  const transformer = valueTransformers[field];
  if (transformer) {
    return transformer(formValue, userInfo);
  }

  if (field === EditProfileFields.AVATAR) {
    return formValue instanceof File ? formValue : null;
  }

  return formValue ? String(formValue) : null;
};

export const transformProfileToFormData = (formValues: UpdateUserProfileParams, userInfo: UserInfo): FormData => {
  const formData = new FormData();

  const allFields = [...PROFILE_FIELDS.required, ...PROFILE_FIELDS.optional];

  allFields.forEach((field) => {
    const formValue = formValues[field];
    const transformedValue = getTransformedValue(field, formValue, userInfo);

    if (transformedValue !== null) {
      formData.append(field, transformedValue);
    }
  });

  return formData;
};
