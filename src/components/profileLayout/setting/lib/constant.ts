export enum EditProfileFields {
  USER_ID = 'userId',
  NAME = 'name',
  PHONE = 'phone',
  DATE_OF_BIRTH = 'date_of_birth',
  PROVINCE_ID = 'province_id',
  ADDRESS = 'address',
  DISTRICT_ID = 'district_id',
  WARD_ID = 'ward_id',
  SHORT_INTRODUCE = 'short_introduce',
  AVATAR = 'avatar',
}

export const PROFILE_FIELDS = {
  required: [
    EditProfileFields.USER_ID,
    EditProfileFields.NAME,
    EditProfileFields.PHONE,
    EditProfileFields.DATE_OF_BIRTH,
    EditProfileFields.PROVINCE_ID,
  ] as const,
  optional: [
    EditProfileFields.ADDRESS,
    EditProfileFields.DISTRICT_ID,
    EditProfileFields.WARD_ID,
    EditProfileFields.SHORT_INTRODUCE,
    EditProfileFields.AVATAR,
  ] as const,
};
