// components/CustomLink.jsx
'use client';

import Link from 'next/link';
import React, { useEffect, useRef } from 'react';

type SmartLinkProps = {
  href: string;
  children: React.ReactNode;
  prefetch?: boolean;
  prefetchOn?: 'none' | 'hover' | 'visible';
} & React.ComponentProps<typeof Link>;

export default function SmartLink(props: SmartLinkProps) {
  const { href, children, prefetch = false, prefetchOn = 'none', ...rest } = props;
  const linkRef = useRef<HTMLAnchorElement>(null);

  const handleMouseEnter = (): (() => void) | void => {
    if (prefetchOn !== 'hover' || !linkRef.current) return;
    const link: HTMLLinkElement = document.createElement('link');

    link.rel = 'prefetch';
    link.href = href;
    document?.head.appendChild(link);

    const handleMouseLeave = (): void => {
      if (document?.head.contains(link)) {
        document.head.removeChild(link);
      }
    };

    linkRef.current.addEventListener('mouseleave', handleMouseLeave);

    return () => {
      linkRef.current?.removeEventListener('mouseleave', handleMouseLeave);
    };
  };

  useEffect(() => {
    if (prefetchOn !== 'visible') return;

    const observer: IntersectionObserver = new IntersectionObserver(
      ([entry]: IntersectionObserverEntry[]) => {
        if (entry.isIntersecting) {
          const link: HTMLLinkElement = document.createElement('link');
          link.rel = 'prefetch';
          link.href = href;
          document?.head.appendChild(link);

          return () => {
            document?.head.removeChild(link);
          };
        }
      },
      { threshold: 0.1 },
    );

    if (linkRef.current) {
      observer.observe(linkRef.current);
    }

    return () => {
      observer.disconnect();
    };
  }, [href, prefetchOn]);

  return (
    <Link
      ref={linkRef}
      href={href}
      prefetch={prefetch}
      onMouseEnter={prefetchOn === 'hover' ? handleMouseEnter : undefined}
      {...rest}
    >
      {children}
    </Link>
  );
}
