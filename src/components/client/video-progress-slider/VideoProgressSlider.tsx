import React from 'react';
import { VideoSeekSlider } from 'react-video-seek-slider';
import './index.scss';

type VideoProgressSliderProps = {
  duration: number;
  currentTime: number;
  bufferTime: number;
} & Omit<React.ComponentProps<typeof VideoSeekSlider>, 'max'>;

const SECOND = 1000;

const VideoProgressSlider = (props: VideoProgressSliderProps) => {
  const { duration, currentTime, bufferTime, ...rest } = props;

  return (
    <VideoSeekSlider
      secondsPrefix="00:00:"
      minutesPrefix="00:"
      {...rest}
      onChange={(time, offsetTime) => {
        rest.onChange(time / SECOND, offsetTime / SECOND);
      }}
      currentTime={currentTime * SECOND}
      bufferTime={bufferTime * SECOND}
      max={duration * SECOND}
    />
  );
};

export default VideoProgressSlider;
