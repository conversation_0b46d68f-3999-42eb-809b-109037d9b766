a.container-slider {
  .slider-volume {
    height: 80% !important;
  }
}

.slider-custom {
  .ant-slider-handle,
  .ant-slider {
    z-index: 1000;
  }

  .seek-learner {
    background: rgba(239, 239, 239, 0.5);
    box-shadow: 1px 1px 4px rgba(179, 170, 170, 0.2);
  }

  .seek-border {
    &:before,
    &:after {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      width: 2px;
      @apply bg-primary-400;
    }
  }

  .ant-slider-rail {
    display: none;
  }

  .ant-slider-rail-block {
    .ant-slider-rail {
      display: block !important;
    }
  }
}

.ui-video-seek-slider {
  .track {
    .main {
      .connect {
        @apply bg-primary-400 !important;
      }
    }
  }
  .thumb {
    .handler {
      background: white !important;
      box-shadow: 0 0 0 2px #96a3ff;
    }
  }
}
