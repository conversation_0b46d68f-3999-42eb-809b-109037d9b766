import { Icon } from '@/components/client/icon';
import { cn } from '@/lib/utils';
import { MagnifyingGlassIcon } from '@heroicons/react/24/outline';

type Props = { inputProps?: React.InputHTMLAttributes<HTMLInputElement> };

function Searchbar(props: Props) {
  const { inputProps } = props;
  return (
    <div className="flex w-full gap-4 rounded-lg border border-neutral-200 px-4 py-2.5">
      <Icon icon={<MagnifyingGlassIcon />} />
      <input
        id="search-bar"
        name="search-bar"
        className={cn('w-full focus-visible:outline-none', 'placeholder:text-neutral-400')}
        {...inputProps}
      />
    </div>
  );
}

export default Searchbar;
