import { cn } from '@/lib/utils';
import clsx from 'clsx';
import Image from 'next/image';
import React from 'react';

interface IconProps extends React.HTMLAttributes<HTMLDivElement> {
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  icon: React.ReactNode;
}

const Icon: React.FC<IconProps> = (props) => {
  const { size = 'md', icon, className, ...rest } = props;

  const sizeClasses = {
    sm: cn('h-4 w-4'),
    md: cn('h-6 w-6'),
    lg: cn('h-8 w-8'),
  };

  if (typeof icon === 'string') {
    return <Image src={icon} alt={'icon'} width={24} height={24} className={clsx(cn(sizeClasses[size]), className)} />;
  }

  return (
    <div className={cn(`[&>svg]: ${sizeClasses[size]}`, className)} {...rest}>
      {icon}
    </div>
  );
};

export default Icon;
