import { Button } from '@/components/ui';
import { LibraryModal } from '@/modules/courses/components';
import { LibraryFileType } from '@/modules/courses/constants/file.const';
import { LibraryFile } from '@/modules/courses/types/file.type';
import Image from 'next/image';
import React, { useState } from 'react';

interface CourseImageUploadProps {
  title?: string;
  imageUrl?: string;
  onImageChange?: (file: File) => void;
  onImageUrlChange?: (url: string) => void;
  onLibraryFileSelect?: (file: LibraryFile) => void;
  onRemoveImage?: () => void;
  className?: string;
}

export const CourseImageUpload: React.FC<CourseImageUploadProps> = ({
  title = 'Ảnh đại diện kho<PERSON> học *',
  imageUrl,
  onImageChange,
  onImageUrlChange,
  onLibraryFileSelect,
  onRemoveImage,
  className = '',
}) => {
  const maxSize = 1024 * 2; // 2MB
  const [isLibraryModalOpen, setIsLibraryModalOpen] = useState(false);

  const handleLibraryFileSelect = (file: LibraryFile) => {
    if (file.fileUrl) {
      if (onLibraryFileSelect) {
        onLibraryFileSelect(file);
      } else if (onImageUrlChange) {
        onImageUrlChange(file.fileUrl);
      }
    }
    setIsLibraryModalOpen(false);
  };

  const handleChooseImageClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsLibraryModalOpen(true);
  };

  return (
    <div className={`course-image-upload ${className}`}>
      <div>
        <div
          onClick={handleChooseImageClick}
          className="hover:border-gray-300 relative h-[250px] w-full cursor-pointer rounded-lg border border-[#D4DAE5] bg-white transition-colors"
        >
          {imageUrl ? (
            // Show uploaded image
            <div className="relative h-full w-full">
              <Image src={imageUrl} alt="Course thumbnail" fill className="rounded-lg object-cover" />
              {onRemoveImage && (
                <button
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    onRemoveImage();
                  }}
                  className="absolute right-2 top-2 flex h-6 w-6 items-center justify-center rounded-full bg-red-500 text-white transition-colors hover:bg-red-600"
                >
                  ×
                </button>
              )}
            </div>
          ) : (
            // Show placeholder
            <div className="flex h-full flex-col items-center justify-center p-4">
              <div className="mb-4 h-[250px]">
                <Image
                  src="/images/placeholder.png"
                  alt="Upload placeholder"
                  fill
                  className="h-full w-full object-contain"
                />
              </div>

              {/* Instructions */}
            </div>
          )}
        </div>
        <div className="text-gray-600 mb-4 text-[12px]">
          <p>Hãy sử dụng tệp hình ảnh có có đuôi .jpg hoặc .png với kích thước khuyến nghị 1280x720 (Tỷ lệ ảnh 16:9)</p>
        </div>

        {/* Upload Button */}
        <Button
          variant="secondary"
          onClick={handleChooseImageClick}
          className="border-gray-300 text-gray-700 hover:bg-gray-50 border px-4 py-2 text-sm transition-colors"
        >
          Chọn ảnh
        </Button>
      </div>
      {/* Library Modal */}
      <LibraryModal
        limitSize={maxSize}
        open={isLibraryModalOpen}
        title="Chọn ảnh"
        enabledTypes={[LibraryFileType.IMAGE]}
        onAddFile={handleLibraryFileSelect}
        onClose={() => setIsLibraryModalOpen(false)}
      />
    </div>
  );
};

export default CourseImageUpload;
