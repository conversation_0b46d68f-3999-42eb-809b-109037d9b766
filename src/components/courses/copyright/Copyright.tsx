import { Form, Radio } from 'antd';
import { CopyrightEnum } from 'constants/enum';
import { authorOptions } from 'constants/option';

export function Copyright({ value: copyRightAuthor }: Readonly<{ value: CopyrightEnum }>) {
  const form = Form.useFormInstance();
  return (
    <Form.Item
      rules={[
        {
          required: true,
          message: 'Vui lòng chọn quyền tác giả',
        },
      ]}
      label={'Quyền tác giả*'}
      required={true}
      name={'copyright'}
    >
      <div className={'grid grid-cols-2 gap-2'}>
        {authorOptions.map((authorOption, index) => {
          return (
            <div
              onClick={() => {
                form.setFieldValue('copyright', authorOption.value);
              }}
              key={`author-${authorOption.value}`}
            >
              <div
                className={`w-full cursor-pointer rounded-lg px-4 py-[12px] ${
                  copyRightAuthor === authorOption.value ? 'border border-primary' : 'bg-ink-100'
                }`}
              >
                <div className={'flex justify-between'}>
                  <p className={'text-base'}>{authorOption.label}</p>
                  <Radio
                    onChange={() => {
                      form.setFieldValue('copyright', authorOption.value);
                    }}
                    checked={copyRightAuthor === authorOption.value}
                  />
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </Form.Item>
  );
}

export default Copyright;
