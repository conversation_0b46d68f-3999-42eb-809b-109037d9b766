import { DeleteOutlined, PlusCircleOutlined } from '@ant-design/icons/lib/icons';
import { Slider, Tooltip } from 'antd';
import Space from 'antd/es/space';
import MediumImageIcon from 'icons/MediumImageIcon';
import SmallImageIcon from 'icons/SmallImageIcon';

type ControlCenterProps = {
  onSetSegment: () => void;
  onDeleteSegment: () => void;
  onSetNumberOfMark: (value: number) => void;
  numberOfMark: number;
  numberOfSlides: string;
};

const ControlCenter = ({
  onSetSegment,
  onDeleteSegment,
  numberOfMark,
  onSetNumberOfMark,
  numberOfSlides,
}: ControlCenterProps) => {
  return (
    <div className={'relative flex items-center justify-between bg-white px-[22px] py-[2px]'}>
      <Space className={''} size={5}>
        <Tooltip title={'Thêm slide'}>
          <span onClick={onSetSegment}>
            <span className={'flex cursor-pointer items-center justify-center rounded-[5px] p-2 hover:bg-neutral-50'}>
              <PlusCircleOutlined />
            </span>
          </span>
        </Tooltip>
        <Tooltip title={'Xoá slide'}>
          <span onClick={onDeleteSegment} className={'cursor-pointer'}>
            <span className={'flex cursor-pointer items-center justify-center rounded-[5px] p-2 hover:bg-neutral-50'}>
              <DeleteOutlined style={{ color: '#65697b' }} />
            </span>
          </span>
        </Tooltip>
      </Space>

      <Space>
        <p className={'text-[14px]'}>{numberOfSlides}</p>
      </Space>
      <Space size={13}>
        <SmallImageIcon />
        <div onClick={() => {}} className={'w-[100px]'}>
          <Slider
            styles={{
              track: {
                backgroundColor: '#999',
              },
              rail: {
                width: '2px',
              },
            }}
            min={0}
            max={7}
            step={1}
            value={numberOfMark}
            onChange={(value: number) => {
              onSetNumberOfMark(value);
            }}
            tooltip={{ formatter: null }}
          />
        </div>
        <MediumImageIcon />
      </Space>
    </div>
  );
};

export default ControlCenter;
